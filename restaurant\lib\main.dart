import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'config/firebase_config.dart';
import 'providers/auth_provider.dart';
import 'providers/orders_provider.dart';
import 'providers/discount_provider.dart';
import 'screens/auth/login_screen.dart';
import 'screens/profile/restaurant_setup_screen.dart';
import 'screens/dashboard/dashboard_screen.dart';
import 'screens/main/main_screen.dart';
import 'shared/app_colors.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  try {
    await FirebaseConfig.initialize();
    print('✅ Firebase initialized successfully for Restaurant app');
  } catch (e) {
    print('❌ Firebase initialization failed: $e');
    print('🏪 Restaurant app will continue without Firebase features');
    // Continue without Firebase for now - you can handle this differently
  }

  runApp(const BRSIMARestaurantApp());
}

class BRSIMARestaurantApp extends StatelessWidget {
  const BRSIMARestaurantApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => OrdersProvider()),
        ChangeNotifierProvider(create: (_) => DiscountProvider()),
      ],
      child: MaterialApp(
        title: 'BRSIMA Restaurant',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFFE91E63), // AppColors.primary
            brightness: Brightness.light,
          ),
          useMaterial3: true,
          textTheme: GoogleFonts.poppinsTextTheme(),
          scaffoldBackgroundColor: const Color(
            0xFFFAFAFA,
          ), // AppColors.background
          appBarTheme: const AppBarTheme(
            backgroundColor: Color(0xFFE91E63), // AppColors.primary
            foregroundColor: Colors.white,
            elevation: 0,
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              textStyle: GoogleFonts.poppins(fontWeight: FontWeight.w600),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          inputDecorationTheme: InputDecorationTheme(
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.orange),
            ),
            labelStyle: GoogleFonts.poppins(),
            hintStyle: GoogleFonts.poppins(color: Colors.grey.shade500),
          ),
        ),
        home: const AuthWrapper(),
      ),
    );
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<AuthProvider>(context, listen: false).checkAuthStatus();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // Show loading only during initial auth check
        if (authProvider.isLoading && !authProvider.isAuthenticated) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        // If not authenticated, show login screen
        if (!authProvider.isAuthenticated) {
          return const LoginScreen();
        }

        // If authenticated but no profile, show setup screen
        if (authProvider.profile == null) {
          return const RestaurantSetupScreen();
        }

        // Otherwise show main screen with bottom navigation
        return const MainScreen();
      },
    );
  }
}
