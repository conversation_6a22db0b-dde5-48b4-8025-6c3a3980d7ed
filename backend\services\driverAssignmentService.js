const { query, pool } = require('../config/database');

class DriverAssignmentService {
  constructor() {
    this.ASSIGNMENT_TIMEOUT_MINUTES = 3; // 3 minutes for driver to accept
    this.assignmentTimers = new Map(); // Track active timers
  }

  /**
   * Assign an order to the next available driver
   * @param {number} orderId - The order ID to assign
   * @param {string} orderType - 'restaurant' or 'mart'
   * @returns {Promise<boolean>} - True if assignment was successful
   */
  async assignOrderToNextDriver(orderId, orderType = 'restaurant') {
    try {
      console.log(`🚀 Starting assignment for order ${orderId} (${orderType})`);

      // Get the order details
      const orderResult = await query(
        'SELECT id, status, assigned_at, assignment_expires_at FROM orders WHERE id = $1',
        [orderId]
      );

      if (orderResult.rows.length === 0) {
        console.log(`❌ Order ${orderId} not found`);
        return false;
      }

      const order = orderResult.rows[0];

      // Only assign orders that are ready and not already assigned
      if (order.status !== 'ready') {
        console.log(`❌ Order ${orderId} is not ready for assignment (status: ${order.status})`);
        return false;
      }

      // Check if order is already assigned and not expired
      if (order.assigned_at && order.assignment_expires_at && new Date() < new Date(order.assignment_expires_at)) {
        console.log(`❌ Order ${orderId} is already assigned and not expired`);
        return false;
      }

      // Get next available driver (excluding those who have declined or are currently assigned)
      const nextDriver = await this.getNextAvailableDriver(orderId, orderType);

      if (!nextDriver) {
        console.log(`❌ No available drivers for order ${orderId}`);
        return false;
      }

      // Assign the order to the driver
      const success = await this.assignOrderToDriver(orderId, nextDriver.id);

      if (success) {
        console.log(`✅ Order ${orderId} assigned to driver ${nextDriver.id}`);
        // Start the assignment timer
        this.startAssignmentTimer(orderId, orderType);
        return true;
      }

      return false;
    } catch (error) {
      console.error(`❌ Error assigning order ${orderId}:`, error);
      return false;
    }
  }

  /**
   * Get the next available driver for an order
   * @param {number} orderId - The order ID
   * @param {string} orderType - 'restaurant' or 'mart'
   * @returns {Promise<Object|null>} - Driver object or null
   */
  async getNextAvailableDriver(orderId, orderType) {
    try {
      // Ensure declined_orders table exists
      await query(`
        CREATE TABLE IF NOT EXISTS declined_orders (
          id SERIAL PRIMARY KEY,
          driver_id INTEGER NOT NULL REFERENCES driver_profiles(id) ON DELETE CASCADE,
          order_id INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
          declined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(driver_id, order_id)
        )
      `);

      // First, let's check how many drivers exist and their availability
      const allDriversResult = await query(`
        SELECT id, full_name, is_available FROM driver_profiles
      `);
      console.log(`📊 Total drivers in system: ${allDriversResult.rows.length}`);
      console.log(`📊 Available drivers: ${allDriversResult.rows.filter(d => d.is_available).length}`);

      if (allDriversResult.rows.length === 0) {
        console.log(`❌ No drivers found in the system`);
        return null;
      }

      // Get drivers who:
      // 1. Are available
      // 2. Haven't declined this order
      // 3. Don't have a pending assignment for this order
      // 4. Are not currently assigned to another order
      const driversResult = await query(`
        SELECT dp.id, dp.user_id, dp.full_name, dp.current_latitude, dp.current_longitude
        FROM driver_profiles dp
        WHERE dp.is_available = true
        AND dp.id NOT IN (
          -- Exclude drivers who have declined this order
          SELECT DISTINCT declined.driver_id
          FROM declined_orders declined
          WHERE declined.order_id = $1
        )
        AND dp.id NOT IN (
          -- Exclude drivers with pending assignments for this order
          SELECT DISTINCT doa.driver_id
          FROM driver_order_assignments doa
          WHERE doa.order_id = $1 AND doa.status = 'pending'
        )
        AND dp.id NOT IN (
          -- Exclude drivers currently assigned to other orders
          SELECT DISTINCT o.driver_id
          FROM orders o
          WHERE o.driver_id IS NOT NULL
          AND o.status IN ('assigned', 'picked_up')
          AND o.id != $1
        )
        ORDER BY dp.id ASC
        LIMIT 1
      `, [orderId]);

      if (driversResult.rows.length === 0) {
        console.log(`🔍 Debugging why no drivers are available for order ${orderId}:`);

        // Check each exclusion condition separately
        const declinedDrivers = await query(`
          SELECT DISTINCT declined.driver_id
          FROM declined_orders declined
          WHERE declined.order_id = $1
        `, [orderId]);
        console.log(`📊 Drivers who declined this order: ${declinedDrivers.rows.length}`);

        const pendingAssignments = await query(`
          SELECT DISTINCT doa.driver_id
          FROM driver_order_assignments doa
          WHERE doa.order_id = $1 AND doa.status = 'pending'
        `, [orderId]);
        console.log(`📊 Drivers with pending assignments: ${pendingAssignments.rows.length}`);

        const busyDrivers = await query(`
          SELECT o.driver_id, o.id as order_id, o.status, o.created_at
          FROM orders o
          WHERE o.driver_id IS NOT NULL
          AND o.status IN ('assigned', 'picked_up')
          AND o.id != $1
        `, [orderId]);
        console.log(`📊 Drivers currently busy with other orders: ${busyDrivers.rows.length}`);
        if (busyDrivers.rows.length > 0) {
          console.log(`📋 Busy drivers details:`, busyDrivers.rows.map(row =>
            `Driver ${row.driver_id} on order ${row.order_id} (${row.status}) since ${row.created_at}`
          ));
        }

        return null;
      }

      return driversResult.rows[0];
    } catch (error) {
      console.error('Error getting next available driver:', error);
      return null;
    }
  }

  /**
   * Assign an order to a specific driver
   * @param {number} orderId - The order ID
   * @param {number} driverId - The driver ID
   * @returns {Promise<boolean>} - True if assignment was successful
   */
  async assignOrderToDriver(orderId, driverId) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');

      const expiresAt = new Date(Date.now() + this.ASSIGNMENT_TIMEOUT_MINUTES * 60 * 1000);

      // Update the order with assignment details
      const orderUpdateResult = await client.query(`
        UPDATE orders 
        SET assigned_at = CURRENT_TIMESTAMP, 
            assignment_expires_at = $1,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $2 AND status = 'ready'
        RETURNING id
      `, [expiresAt, orderId]);

      if (orderUpdateResult.rows.length === 0) {
        await client.query('ROLLBACK');
        return false;
      }

      // Create driver assignment record
      await client.query(`
        INSERT INTO driver_order_assignments 
        (order_id, driver_id, expires_at, status)
        VALUES ($1, $2, $3, 'pending')
        ON CONFLICT (order_id, driver_id) 
        DO UPDATE SET 
          expires_at = EXCLUDED.expires_at,
          status = 'pending',
          assigned_at = CURRENT_TIMESTAMP
      `, [orderId, driverId, expiresAt]);

      await client.query('COMMIT');
      return true;
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Error assigning order to driver:', error);
      return false;
    } finally {
      client.release();
    }
  }

  /**
   * Start assignment timer for an order
   * @param {number} orderId - The order ID
   * @param {string} orderType - 'restaurant' or 'mart'
   */
  startAssignmentTimer(orderId, orderType) {
    // Clear existing timer if any
    if (this.assignmentTimers.has(orderId)) {
      clearTimeout(this.assignmentTimers.get(orderId));
    }

    const timer = setTimeout(async () => {
      console.log(`⏰ Assignment timer expired for order ${orderId}`);
      await this.handleAssignmentTimeout(orderId, orderType);
      this.assignmentTimers.delete(orderId);
    }, this.ASSIGNMENT_TIMEOUT_MINUTES * 60 * 1000);

    this.assignmentTimers.set(orderId, timer);
    console.log(`⏰ Started ${this.ASSIGNMENT_TIMEOUT_MINUTES} minute timer for order ${orderId}`);
  }

  /**
   * Handle assignment timeout - reassign to next driver
   * @param {number} orderId - The order ID
   * @param {string} orderType - 'restaurant' or 'mart'
   */
  async handleAssignmentTimeout(orderId, orderType) {
    try {
      console.log(`⏰ Handling timeout for order ${orderId}`);

      // Mark current assignment as expired
      await query(`
        UPDATE driver_order_assignments 
        SET status = 'expired', responded_at = CURRENT_TIMESTAMP
        WHERE order_id = $1 AND status = 'pending'
      `, [orderId]);

      // Clear assignment from order
      await query(`
        UPDATE orders 
        SET assigned_at = NULL, assignment_expires_at = NULL
        WHERE id = $1 AND status = 'ready'
      `, [orderId]);

      // Try to assign to next available driver
      const reassigned = await this.assignOrderToNextDriver(orderId, orderType);
      
      if (!reassigned) {
        console.log(`❌ Could not reassign order ${orderId} - no more available drivers`);
        // Optionally, you could implement a notification system here
      }
    } catch (error) {
      console.error(`Error handling assignment timeout for order ${orderId}:`, error);
    }
  }

  /**
   * Accept an order assignment
   * @param {number} orderId - The order ID
   * @param {number} driverId - The driver ID
   * @returns {Promise<boolean>} - True if acceptance was successful
   */
  async acceptOrderAssignment(orderId, driverId) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');

      // Check if the assignment is still valid
      const assignmentResult = await client.query(`
        SELECT id, expires_at 
        FROM driver_order_assignments 
        WHERE order_id = $1 AND driver_id = $2 AND status = 'pending'
      `, [orderId, driverId]);

      if (assignmentResult.rows.length === 0) {
        await client.query('ROLLBACK');
        return false;
      }

      const assignment = assignmentResult.rows[0];
      
      // Check if assignment has expired
      if (new Date() > new Date(assignment.expires_at)) {
        await client.query('ROLLBACK');
        return false;
      }

      // Update assignment status
      await client.query(`
        UPDATE driver_order_assignments 
        SET status = 'accepted', responded_at = CURRENT_TIMESTAMP
        WHERE order_id = $1 AND driver_id = $2
      `, [orderId, driverId]);

      // Update order with driver assignment
      await client.query(`
        UPDATE orders 
        SET driver_id = $1, status = 'assigned', 
            assigned_at = CURRENT_TIMESTAMP,
            assignment_expires_at = NULL,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $2 AND status = 'ready'
      `, [driverId, orderId]);

      await client.query('COMMIT');

      // Clear the assignment timer
      if (this.assignmentTimers.has(orderId)) {
        clearTimeout(this.assignmentTimers.get(orderId));
        this.assignmentTimers.delete(orderId);
      }

      console.log(`✅ Order ${orderId} accepted by driver ${driverId}`);
      return true;
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Error accepting order assignment:', error);
      return false;
    } finally {
      client.release();
    }
  }

  /**
   * Decline an order assignment
   * @param {number} orderId - The order ID
   * @param {number} driverId - The driver ID
   * @param {string} orderType - 'restaurant' or 'mart'
   * @returns {Promise<boolean>} - True if decline was successful
   */
  async declineOrderAssignment(orderId, driverId, orderType = 'restaurant') {
    try {
      // Update assignment status
      await query(`
        UPDATE driver_order_assignments 
        SET status = 'declined', responded_at = CURRENT_TIMESTAMP
        WHERE order_id = $1 AND driver_id = $2 AND status = 'pending'
      `, [orderId, driverId]);

      // Add to declined orders table
      await query(`
        INSERT INTO declined_orders (driver_id, order_id)
        VALUES ($1, $2)
        ON CONFLICT (driver_id, order_id) DO NOTHING
      `, [driverId, orderId]);

      // Clear assignment from order
      await query(`
        UPDATE orders 
        SET assigned_at = NULL, assignment_expires_at = NULL
        WHERE id = $1 AND status = 'ready'
      `, [orderId]);

      // Clear the assignment timer
      if (this.assignmentTimers.has(orderId)) {
        clearTimeout(this.assignmentTimers.get(orderId));
        this.assignmentTimers.delete(orderId);
      }

      console.log(`❌ Order ${orderId} declined by driver ${driverId}`);

      // Try to assign to next available driver
      setTimeout(() => {
        this.assignOrderToNextDriver(orderId, orderType);
      }, 1000); // Small delay to ensure database consistency

      return true;
    } catch (error) {
      console.error('Error declining order assignment:', error);
      return false;
    }
  }

  /**
   * Get current assignment for a driver
   * @param {number} driverId - The driver ID
   * @returns {Promise<Object|null>} - Assignment object or null
   */
  async getCurrentAssignment(driverId) {
    try {
      const result = await query(`
        SELECT doa.*, o.order_number, o.order_type
        FROM driver_order_assignments doa
        JOIN orders o ON doa.order_id = o.id
        WHERE doa.driver_id = $1 
        AND doa.status = 'pending' 
        AND doa.expires_at > CURRENT_TIMESTAMP
        ORDER BY doa.assigned_at DESC
        LIMIT 1
      `, [driverId]);

      return result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      console.error('Error getting current assignment:', error);
      return null;
    }
  }

  /**
   * Clean up expired assignments
   */
  async cleanupExpiredAssignments() {
    try {
      const result = await query(`
        UPDATE driver_order_assignments 
        SET status = 'expired', responded_at = CURRENT_TIMESTAMP
        WHERE status = 'pending' AND expires_at < CURRENT_TIMESTAMP
        RETURNING order_id
      `);

      if (result.rows.length > 0) {
        console.log(`🧹 Cleaned up ${result.rows.length} expired assignments`);
        
        // Clear assignments from orders
        const orderIds = result.rows.map(row => row.order_id);
        await query(`
          UPDATE orders 
          SET assigned_at = NULL, assignment_expires_at = NULL
          WHERE id = ANY($1) AND status = 'ready'
        `, [orderIds]);
      }
    } catch (error) {
      console.error('Error cleaning up expired assignments:', error);
    }
  }
}

module.exports = new DriverAssignmentService();
