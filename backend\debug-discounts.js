#!/usr/bin/env node

/**
 * Debug script to check discount system database setup
 */

const { query } = require('./config/database');

async function debugDiscountSystem() {
  try {
    console.log('🔍 Debugging Discount System...\n');

    // Check if discounts table exists
    console.log('1. Checking if discounts table exists...');
    const tableCheck = await query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'discounts'
      )
    `);
    
    if (!tableCheck.rows[0].exists) {
      console.log('❌ Discounts table does not exist!');
      console.log('💡 Run the database migration first:');
      console.log('   psql -d food_delivery -f backend/migrations/001_create_discounts_table.sql');
      return;
    }
    console.log('✅ Discounts table exists');

    // Check table structure
    console.log('\n2. Checking discounts table structure...');
    const columns = await query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'discounts'
      ORDER BY ordinal_position
    `);
    
    console.log('📋 Columns:');
    columns.rows.forEach(col => {
      console.log(`   ${col.column_name} (${col.data_type}) ${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });

    // Check if there are any discounts
    console.log('\n3. Checking existing discounts...');
    const discountCount = await query('SELECT COUNT(*) as count FROM discounts');
    console.log(`📊 Total discounts: ${discountCount.rows[0].count}`);

    if (discountCount.rows[0].count > 0) {
      const discounts = await query('SELECT id, name, discount_type, is_active, created_at FROM discounts ORDER BY created_at DESC LIMIT 5');
      console.log('📝 Recent discounts:');
      discounts.rows.forEach(discount => {
        console.log(`   ${discount.id}: ${discount.name} (${discount.discount_type}) - ${discount.is_active ? 'Active' : 'Inactive'}`);
      });
    }

    // Check related tables
    console.log('\n4. Checking related tables...');
    
    const relatedTables = ['discount_usage', 'restaurants', 'restaurant_profiles'];
    for (const tableName of relatedTables) {
      const tableExists = await query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = $1
        )
      `, [tableName]);
      
      if (tableExists.rows[0].exists) {
        const count = await query(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`   ✅ ${tableName}: ${count.rows[0].count} records`);
      } else {
        console.log(`   ❌ ${tableName}: table does not exist`);
      }
    }

    // Test a simple discount query
    console.log('\n5. Testing discount query...');
    try {
      const testQuery = await query(`
        SELECT d.*, COUNT(du.id) as usage_count
        FROM discounts d
        LEFT JOIN discount_usage du ON d.id = du.discount_id
        GROUP BY d.id
        ORDER BY d.created_at DESC
        LIMIT 5
      `);
      console.log(`✅ Query successful, returned ${testQuery.rows.length} rows`);
    } catch (error) {
      console.log('❌ Query failed:', error.message);
    }

    console.log('\n🎉 Debug complete!');

  } catch (error) {
    console.error('❌ Debug error:', error);
  } finally {
    process.exit(0);
  }
}

debugDiscountSystem();
