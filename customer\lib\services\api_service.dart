import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../models/restaurant.dart';
import '../models/address.dart';
import '../models/discount.dart';

class ApiService {
  // Dynamic base URL based on platform
  static String get baseUrl {
    if (kIsWeb) {
      return 'http://localhost:3000/api';
    } else if (Platform.isAndroid) {
      // Use your actual Wi-Fi IP address for physical device
      // Use ******** for emulator
      return 'http://**************:3000/api';
    } else {
      return 'http://localhost:3000/api';
    }
  }

  // Retrieve saved token (JWT). Try new key first, then legacy key.
  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('auth_token');
    token ??= prefs.getString('token');
    return token;
  }

  // Store token under both new and legacy keys for compatibility.
  static Future<void> setToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('auth_token', token);
    await prefs.setString('token', token);
  }

  // Remove token from both keys.
  static Future<void> removeToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
    await prefs.remove('token');
  }

  // Default headers without authentication
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
  };

  // Get headers with auth token
  static Future<Map<String, String>> getHeaders() async {
    final token = await getToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // Register user
  static Future<Map<String, dynamic>> register({
    required String email,
    required String password,
  }) async {
    print('🔗 Attempting to connect to: $baseUrl/auth/register');

    try {
      final response = await http
          .post(
            Uri.parse('$baseUrl/auth/register'),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({
              'email': email,
              'password': password,
              'userType': 'customer',
            }),
          )
          .timeout(const Duration(seconds: 10));

      print('📡 Response status: ${response.statusCode}');
      print('📡 Response body: ${response.body}');

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        await setToken(data['token']);
        return data;
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['error'] ?? 'Registration failed');
      }
    } catch (e) {
      print('❌ Registration error: $e');
      throw Exception('Connection failed: $e');
    }
  }

  // Login user
  static Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    print('🔗 Attempting to login at: $baseUrl/auth/login');

    try {
      final response = await http
          .post(
            Uri.parse('$baseUrl/auth/login'),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({'email': email, 'password': password}),
          )
          .timeout(const Duration(seconds: 10));

      print('📡 Login response status: ${response.statusCode}');
      print('📡 Login response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        await setToken(data['token']);
        return data;
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['error'] ?? 'Login failed');
      }
    } catch (e) {
      print('❌ Login error: $e');
      throw Exception('Connection failed: $e');
    }
  }

  // Get current user profile
  static Future<Map<String, dynamic>> getCurrentUser() async {
    final headers = await getHeaders();
    final response = await http.get(
      Uri.parse('$baseUrl/auth/me'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get user profile');
    }
  }

  // Complete customer profile
  static Future<Map<String, dynamic>> completeProfile({
    required String fullName,
    required String phone,
    required String address,
    required double latitude,
    required double longitude,
  }) async {
    final headers = await getHeaders();
    final response = await http.post(
      Uri.parse('$baseUrl/customer/profile'),
      headers: headers,
      body: jsonEncode({
        'fullName': fullName,
        'phone': phone,
        'address': address,
        'latitude': latitude,
        'longitude': longitude,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to complete profile');
    }
  }

  // Update location
  static Future<Map<String, dynamic>> updateLocation({
    required double latitude,
    required double longitude,
    String? address,
  }) async {
    final headers = await getHeaders();
    final response = await http.patch(
      Uri.parse('$baseUrl/customer/location'),
      headers: headers,
      body: jsonEncode({
        'latitude': latitude,
        'longitude': longitude,
        if (address != null) 'address': address,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to update location');
    }
  }

  // Logout
  static Future<void> logout() async {
    await removeToken();
  }

  // Get nearby restaurants (supports both authenticated and guest access)
  static Future<List<Restaurant>> getRestaurants({
    double? latitude,
    double? longitude,
    double radius = 10,
    bool isGuest = false,
  }) async {
    final headers = isGuest ? defaultHeaders : await getHeaders();

    String endpoint = isGuest
        ? '/customer/public/restaurants'
        : '/customer/restaurants';
    String url = '$baseUrl$endpoint';
    if (latitude != null && longitude != null) {
      url += '?latitude=$latitude&longitude=$longitude&radius=$radius';
    }

    final response = await http.get(Uri.parse(url), headers: headers);

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return (data['restaurants'] as List)
          .map((restaurant) => Restaurant.fromJson(restaurant))
          .toList();
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get restaurants');
    }
  }

  // Get restaurant details with menu (supports both authenticated and guest access)
  static Future<RestaurantDetail> getRestaurantDetails(
    int restaurantId, {
    bool isGuest = false,
  }) async {
    final headers = isGuest ? defaultHeaders : await getHeaders();
    final endpoint = isGuest
        ? '/customer/public/restaurants/$restaurantId'
        : '/customer/restaurants/$restaurantId';

    final response = await http.get(
      Uri.parse('$baseUrl$endpoint'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return RestaurantDetail.fromJson(data);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get restaurant details');
    }
  }

  // Search restaurants and menu items (supports both authenticated and guest access)
  static Future<List<Restaurant>> searchRestaurants({
    required String query,
    double? latitude,
    double? longitude,
    String? cuisine,
    bool isGuest = false,
  }) async {
    final headers = isGuest ? defaultHeaders : await getHeaders();

    final endpoint = isGuest ? '/customer/public/search' : '/customer/search';
    String url = '$baseUrl$endpoint?q=${Uri.encodeComponent(query)}';
    if (latitude != null && longitude != null) {
      url += '&latitude=$latitude&longitude=$longitude';
    }
    if (cuisine != null) {
      url += '&cuisine=${Uri.encodeComponent(cuisine)}';
    }

    final response = await http.get(Uri.parse(url), headers: headers);

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return (data['restaurants'] as List)
          .map((restaurant) => Restaurant.fromJson(restaurant))
          .toList();
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to search restaurants');
    }
  }

  // Create order from cart
  static Future<Map<String, dynamic>> createOrder({
    required int restaurantId,
    required List<Map<String, dynamic>> items,
    required String deliveryAddress,
    required double deliveryLatitude,
    required double deliveryLongitude,
    String? specialInstructions,
    double deliveryFee = 0.0,
    int? discountId,
    double discountAmount = 0.0,
  }) async {
    final headers = await getHeaders();

    final response = await http.post(
      Uri.parse('$baseUrl/customer/orders'),
      headers: headers,
      body: jsonEncode({
        'restaurantId': restaurantId,
        'items': items,
        'deliveryAddress': deliveryAddress,
        'deliveryLatitude': deliveryLatitude,
        'deliveryLongitude': deliveryLongitude,
        'specialInstructions': specialInstructions,
        'deliveryFee': deliveryFee,
        'discountId': discountId,
        'discountAmount': discountAmount,
      }),
    );

    if (response.statusCode == 201) {
      return jsonDecode(response.body);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to create order');
    }
  }

  // Get customer orders
  static Future<List<dynamic>> getOrders({
    String? status,
    int limit = 20,
    int offset = 0,
  }) async {
    final headers = await getHeaders();

    String url = '$baseUrl/customer/orders?limit=$limit&offset=$offset';
    if (status != null) {
      url += '&status=$status';
    }

    final response = await http.get(Uri.parse(url), headers: headers);

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['orders'];
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get orders');
    }
  }

  // Get order details
  static Future<Map<String, dynamic>> getOrderDetails(int orderId) async {
    final headers = await getHeaders();

    final response = await http.get(
      Uri.parse('$baseUrl/customer/orders/$orderId'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['order'];
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get order details');
    }
  }

  // Cancel order
  static Future<Map<String, dynamic>> cancelOrder(int orderId) async {
    final headers = await getHeaders();

    final response = await http.put(
      Uri.parse('$baseUrl/customer/orders/$orderId/cancel'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to cancel order');
    }
  }

  // Get customer addresses
  static Future<List<CustomerAddress>> getAddresses() async {
    final headers = await getHeaders();

    final response = await http.get(
      Uri.parse('$baseUrl/customer/addresses'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return (data['addresses'] as List)
          .map((address) => CustomerAddress.fromJson(address))
          .toList();
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get addresses');
    }
  }

  // Add new address
  static Future<CustomerAddress> addAddress({
    required String label,
    required String address,
    required double latitude,
    required double longitude,
    bool isDefault = false,
  }) async {
    final headers = await getHeaders();

    final response = await http.post(
      Uri.parse('$baseUrl/customer/addresses'),
      headers: headers,
      body: jsonEncode({
        'label': label,
        'address': address,
        'latitude': latitude,
        'longitude': longitude,
        'isDefault': isDefault,
      }),
    );

    if (response.statusCode == 201) {
      final data = jsonDecode(response.body);
      return CustomerAddress.fromJson(data['address']);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to add address');
    }
  }

  // Update address
  static Future<CustomerAddress> updateAddress({
    required int addressId,
    String? label,
    String? address,
    double? latitude,
    double? longitude,
    bool? isDefault,
  }) async {
    final headers = await getHeaders();

    final response = await http.put(
      Uri.parse('$baseUrl/customer/addresses/$addressId'),
      headers: headers,
      body: jsonEncode({
        if (label != null) 'label': label,
        if (address != null) 'address': address,
        if (latitude != null) 'latitude': latitude,
        if (longitude != null) 'longitude': longitude,
        if (isDefault != null) 'isDefault': isDefault,
      }),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return CustomerAddress.fromJson(data['address']);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to update address');
    }
  }

  // Delete address
  static Future<void> deleteAddress(int addressId) async {
    final headers = await getHeaders();

    final response = await http.delete(
      Uri.parse('$baseUrl/customer/addresses/$addressId'),
      headers: headers,
    );

    if (response.statusCode != 200) {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to delete address');
    }
  }

  // ============================================================================
  // FAVORITES API METHODS
  // ============================================================================

  // Get customer favorites
  static Future<Map<String, dynamic>> getFavorites({
    int limit = 50,
    int offset = 0,
  }) async {
    final headers = await getHeaders();

    final response = await http.get(
      Uri.parse('$baseUrl/customer/favorites?limit=$limit&offset=$offset'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get favorites');
    }
  }

  // Add item to favorites
  static Future<Map<String, dynamic>> addToFavorites(
    int menuItemId,
    int restaurantId,
  ) async {
    final headers = await getHeaders();

    final response = await http.post(
      Uri.parse('$baseUrl/customer/favorites'),
      headers: headers,
      body: jsonEncode({
        'menuItemId': menuItemId,
        'restaurantId': restaurantId,
      }),
    );

    if (response.statusCode == 201) {
      return jsonDecode(response.body);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to add to favorites');
    }
  }

  // Remove item from favorites
  static Future<void> removeFromFavorites(int menuItemId) async {
    final headers = await getHeaders();

    final response = await http.delete(
      Uri.parse('$baseUrl/customer/favorites/$menuItemId'),
      headers: headers,
    );

    if (response.statusCode != 200) {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to remove from favorites');
    }
  }

  // Check if item is favorited
  static Future<Map<String, dynamic>> checkFavoriteStatus(
    int menuItemId,
  ) async {
    final headers = await getHeaders();

    final response = await http.get(
      Uri.parse('$baseUrl/customer/favorites/check/$menuItemId'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to check favorite status');
    }
  }

  // ============================================================================
  // DISCOUNT API METHODS
  // ============================================================================

  // Get available discounts
  static Future<List<Discount>> getAvailableDiscounts({
    required int restaurantId,
    List<int>? menuItemIds,
  }) async {
    final headers = await getHeaders();

    String url =
        '$baseUrl/customer/discounts/available?restaurantId=$restaurantId';
    if (menuItemIds != null && menuItemIds.isNotEmpty) {
      final itemIdsParam = menuItemIds.map((id) => 'menuItemIds=$id').join('&');
      url += '&$itemIdsParam';
    }

    final response = await http.get(Uri.parse(url), headers: headers);

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return (data['discounts'] as List)
          .map((discount) => Discount.fromJson(discount))
          .toList();
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get available discounts');
    }
  }

  // Validate discount code
  static Future<DiscountValidationResult> validateDiscount({
    required String code,
    required int restaurantId,
    required List<Map<String, dynamic>> items,
    required double subtotal,
  }) async {
    final headers = await getHeaders();

    final response = await http.post(
      Uri.parse('$baseUrl/customer/discounts/validate'),
      headers: headers,
      body: jsonEncode({
        'code': code,
        'restaurantId': restaurantId,
        'items': items,
        'subtotal': subtotal,
      }),
    );

    final data = jsonDecode(response.body);

    if (response.statusCode == 200) {
      return DiscountValidationResult.fromJson(data);
    } else {
      return DiscountValidationResult(
        isValid: false,
        errorMessage: data['error'] ?? 'Failed to validate discount',
        minimumAmount: data['minimumAmount']?.toDouble(),
      );
    }
  }

  // ============================================================================
  // MART API METHODS
  // ============================================================================

  // Get mart categories
  static Future<List<dynamic>> getMartCategories() async {
    final response = await http.get(Uri.parse('$baseUrl/mart/categories'));

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['categories'];
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get mart categories');
    }
  }

  // Get mart products by category
  static Future<List<dynamic>> getMartProductsByCategory(
    int categoryId, {
    int limit = 20,
    int offset = 0,
    String? search,
  }) async {
    String url =
        '$baseUrl/mart/categories/$categoryId/products?limit=$limit&offset=$offset';
    if (search != null && search.isNotEmpty) {
      url += '&search=${Uri.encodeComponent(search)}';
    }

    final response = await http.get(Uri.parse(url));

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['products'];
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get mart products');
    }
  }

  // Get all mart products
  static Future<List<dynamic>> getMartProducts({
    int limit = 20,
    int offset = 0,
    String? search,
    int? categoryId,
  }) async {
    String url = '$baseUrl/mart/products?limit=$limit&offset=$offset';
    if (search != null && search.isNotEmpty) {
      url += '&search=${Uri.encodeComponent(search)}';
    }
    if (categoryId != null) {
      url += '&categoryId=$categoryId';
    }

    final response = await http.get(Uri.parse(url));

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['products'];
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get mart products');
    }
  }

  // Get mart product details
  static Future<Map<String, dynamic>> getMartProductDetails(
    int productId,
  ) async {
    final response = await http.get(
      Uri.parse('$baseUrl/mart/products/$productId'),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['product'];
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get product details');
    }
  }

  // Create mart order
  static Future<Map<String, dynamic>> createMartOrder({
    required int marketLocationId,
    required List<Map<String, dynamic>> items,
    required String deliveryAddress,
    required double deliveryLatitude,
    required double deliveryLongitude,
    String? specialInstructions,
    required double deliveryFee,
  }) async {
    final headers = await getHeaders();

    final body = jsonEncode({
      'marketLocationId': marketLocationId,
      'items': items,
      'deliveryAddress': deliveryAddress,
      'deliveryLatitude': deliveryLatitude,
      'deliveryLongitude': deliveryLongitude,
      'specialInstructions': specialInstructions,
      'deliveryFee': deliveryFee,
    });

    final response = await http.post(
      Uri.parse('$baseUrl/mart/orders'),
      headers: headers,
      body: body,
    );

    if (response.statusCode == 201) {
      final data = jsonDecode(response.body);
      return data;
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to create mart order');
    }
  }

  // Get market locations
  static Future<List<dynamic>> getMarketLocations() async {
    final response = await http.get(Uri.parse('$baseUrl/mart/locations'));

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['locations'];
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get market locations');
    }
  }
}
