const { pool } = require('../config/database');

const favoritesMigrations = [
  // Customer favorites table
  `
    CREATE TABLE IF NOT EXISTS customer_favorites (
      id SERIAL PRIMARY KEY,
      customer_id INTEGER REFERENCES customer_profiles(id) ON DELETE CASCADE,
      menu_item_id INTEGER REFERENCES menu_items(id) ON DELETE CASCADE,
      restaurant_id INTEGER REFERENCES restaurant_profiles(id) ON DELETE CASCADE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(customer_id, menu_item_id)
    );
  `,

  // Create index for faster queries
  `
    CREATE INDEX IF NOT EXISTS idx_customer_favorites_customer_id 
    ON customer_favorites(customer_id);
  `,

  `
    CREATE INDEX IF NOT EXISTS idx_customer_favorites_menu_item_id 
    ON customer_favorites(menu_item_id);
  `,

  `
    CREATE INDEX IF NOT EXISTS idx_customer_favorites_restaurant_id 
    ON customer_favorites(restaurant_id);
  `,
];

async function runFavoritesMigrations() {
  const client = await pool.connect();
  
  try {
    console.log('🔄 Running favorites system migrations...');
    
    for (let i = 0; i < favoritesMigrations.length; i++) {
      const migration = favoritesMigrations[i];
      console.log(`Running migration ${i + 1}/${favoritesMigrations.length}...`);
      await client.query(migration);
    }
    
    console.log('✅ Favorites system migrations completed successfully!');
  } catch (error) {
    console.error('❌ Error running favorites migrations:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run migrations if this file is executed directly
if (require.main === module) {
  runFavoritesMigrations()
    .then(() => {
      console.log('Favorites migrations completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { runFavoritesMigrations };
