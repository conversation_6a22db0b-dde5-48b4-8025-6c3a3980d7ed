import 'package:flutter/foundation.dart';
import '../models/favorite.dart';
import '../models/restaurant.dart';
import '../services/api_service.dart';

class FavoritesProvider with ChangeNotifier {
  FavoritesList _favorites = FavoritesList();
  bool _isLoading = false;
  String? _error;
  bool _hasLoadedInitial = false;

  FavoritesList get favorites => _favorites;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasLoadedInitial => _hasLoadedInitial;
  bool get isEmpty => _favorites.isEmpty;
  bool get isNotEmpty => _favorites.isNotEmpty;
  int get count => _favorites.items.length;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Load favorites from server
  Future<void> loadFavorites({bool forceRefresh = false}) async {
    if (_isLoading) return;
    
    // Don't reload if already loaded unless forced
    if (_hasLoadedInitial && !forceRefresh) return;

    try {
      _setLoading(true);
      _setError(null);

      final response = await ApiService.getFavorites();
      _favorites = FavoritesList.fromJson(response);
      _hasLoadedInitial = true;
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to load favorites: $e');
      print('❌ Load favorites error: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Add item to favorites
  Future<bool> addToFavorites(MenuItem menuItem, int restaurantId, String restaurantName, String cuisineType) async {
    try {
      _setError(null);

      // Optimistically add to local state
      final tempFavorite = FavoriteItem.fromMenuItem(
        favoriteId: -1, // Temporary ID
        menuItem: menuItem,
        restaurantId: restaurantId,
        restaurantName: restaurantName,
        cuisineType: cuisineType,
        createdAt: DateTime.now(),
      );

      final updatedItems = [tempFavorite, ..._favorites.items];
      _favorites = _favorites.copyWith(
        items: updatedItems,
        totalCount: _favorites.totalCount + 1,
      );
      notifyListeners();

      // Make API call
      final response = await ApiService.addToFavorites(menuItem.id, restaurantId);
      
      // Update with real favorite ID from server
      final realFavorite = FavoriteItem.fromMenuItem(
        favoriteId: response['favorite']['id'],
        menuItem: menuItem,
        restaurantId: restaurantId,
        restaurantName: restaurantName,
        cuisineType: cuisineType,
        createdAt: DateTime.parse(response['favorite']['createdAt']),
      );

      final finalItems = _favorites.items.map((item) {
        if (item.id == -1 && item.menuItemId == menuItem.id) {
          return realFavorite;
        }
        return item;
      }).toList();

      _favorites = _favorites.copyWith(items: finalItems);
      notifyListeners();

      return true;
    } catch (e) {
      // Revert optimistic update on error
      final revertedItems = _favorites.items
          .where((item) => !(item.id == -1 && item.menuItemId == menuItem.id))
          .toList();
      
      _favorites = _favorites.copyWith(
        items: revertedItems,
        totalCount: _favorites.totalCount - 1,
      );
      
      _setError('Failed to add to favorites: $e');
      notifyListeners();
      return false;
    }
  }

  // Remove item from favorites
  Future<bool> removeFromFavorites(int menuItemId) async {
    try {
      _setError(null);

      // Find the item to remove
      final itemToRemove = _favorites.getFavoriteByMenuItemId(menuItemId);
      if (itemToRemove == null) return false;

      // Optimistically remove from local state
      final updatedItems = _favorites.items
          .where((item) => item.menuItemId != menuItemId)
          .toList();
      
      _favorites = _favorites.copyWith(
        items: updatedItems,
        totalCount: _favorites.totalCount - 1,
      );
      notifyListeners();

      // Make API call
      await ApiService.removeFromFavorites(menuItemId);
      
      return true;
    } catch (e) {
      // Revert optimistic update on error
      await loadFavorites(forceRefresh: true);
      _setError('Failed to remove from favorites: $e');
      return false;
    }
  }

  // Check if item is favorited
  bool isFavorited(int menuItemId) {
    return _favorites.isFavorited(menuItemId);
  }

  // Get favorite item by menu item ID
  FavoriteItem? getFavoriteByMenuItemId(int menuItemId) {
    return _favorites.getFavoriteByMenuItemId(menuItemId);
  }

  // Toggle favorite status
  Future<bool> toggleFavorite(MenuItem menuItem, int restaurantId, String restaurantName, String cuisineType) async {
    if (isFavorited(menuItem.id)) {
      return await removeFromFavorites(menuItem.id);
    } else {
      return await addToFavorites(menuItem, restaurantId, restaurantName, cuisineType);
    }
  }

  // Group favorites by restaurant
  Map<String, List<FavoriteItem>> getFavoritesByRestaurant() {
    return _favorites.groupByRestaurant();
  }

  // Clear all favorites (local only)
  void clearFavorites() {
    _favorites = FavoritesList();
    _hasLoadedInitial = false;
    _setError(null);
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _setError(null);
  }

  // Refresh favorites
  Future<void> refresh() async {
    await loadFavorites(forceRefresh: true);
  }

  // Get favorites count for a specific restaurant
  int getFavoritesCountForRestaurant(int restaurantId) {
    return _favorites.items
        .where((item) => item.restaurantId == restaurantId)
        .length;
  }

  // Get all favorited menu item IDs (useful for checking multiple items at once)
  Set<int> getFavoritedMenuItemIds() {
    return _favorites.items.map((item) => item.menuItemId).toSet();
  }
}
