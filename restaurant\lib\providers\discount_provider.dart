import 'package:flutter/foundation.dart';
import '../models/discount.dart';
import '../services/api_service.dart';

class DiscountProvider with ChangeNotifier {
  List<Discount> _discounts = [];
  bool _isLoading = false;
  String? _error;

  // Analytics data
  Map<String, dynamic>? _analytics;
  bool _isLoadingAnalytics = false;

  List<Discount> get discounts => _discounts;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Map<String, dynamic>? get analytics => _analytics;
  bool get isLoadingAnalytics => _isLoadingAnalytics;

  // Load restaurant's discounts
  Future<void> loadDiscounts() async {
    _setLoading(true);
    _error = null;

    try {
      final response = await ApiService.get('/restaurant/discounts');

      if (response['success']) {
        _discounts = (response['data']['discounts'] as List)
            .map((json) => Discount.fromJson(json))
            .toList();
      } else {
        _error = response['message'] ?? 'Failed to load discounts';
      }
    } catch (e) {
      _error = 'Network error: ${e.toString()}';
      debugPrint('Load discounts error: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Create new discount
  Future<void> createDiscount(Map<String, dynamic> discountData) async {
    try {
      final response = await ApiService.post(
        '/restaurant/discounts',
        discountData,
      );

      if (response['success']) {
        final newDiscount = Discount.fromJson(response['data']['discount']);
        _discounts.insert(0, newDiscount);
        notifyListeners();
      } else {
        throw Exception(response['message'] ?? 'Failed to create discount');
      }
    } catch (e) {
      debugPrint('Create discount error: $e');
      rethrow;
    }
  }

  // Update existing discount
  Future<void> updateDiscount(
    int discountId,
    Map<String, dynamic> discountData,
  ) async {
    try {
      final response = await ApiService.put(
        '/restaurant/discounts/$discountId',
        discountData,
      );

      if (response['success']) {
        final updatedDiscount = Discount.fromJson(response['data']['discount']);
        final index = _discounts.indexWhere((d) => d.id == discountId);
        if (index != -1) {
          _discounts[index] = updatedDiscount;
          notifyListeners();
        }
      } else {
        throw Exception(response['message'] ?? 'Failed to update discount');
      }
    } catch (e) {
      debugPrint('Update discount error: $e');
      rethrow;
    }
  }

  // Toggle discount active status
  Future<void> toggleDiscountStatus(int discountId) async {
    try {
      final response = await ApiService.patch(
        '/restaurant/discounts/$discountId/toggle',
      );

      if (response['success']) {
        final index = _discounts.indexWhere((d) => d.id == discountId);
        if (index != -1) {
          _discounts[index] = _discounts[index].copyWith(
            isActive: !_discounts[index].isActive,
          );
          notifyListeners();
        }
      } else {
        throw Exception(
          response['message'] ?? 'Failed to toggle discount status',
        );
      }
    } catch (e) {
      debugPrint('Toggle discount status error: $e');
      rethrow;
    }
  }

  // Delete discount
  Future<void> deleteDiscount(int discountId) async {
    try {
      final response = await ApiService.delete(
        '/restaurant/discounts/$discountId',
      );

      if (response['success']) {
        _discounts.removeWhere((d) => d.id == discountId);
        notifyListeners();
      } else {
        throw Exception(response['message'] ?? 'Failed to delete discount');
      }
    } catch (e) {
      debugPrint('Delete discount error: $e');
      rethrow;
    }
  }

  // Load discount analytics
  Future<void> loadAnalytics({String period = '30d'}) async {
    _isLoadingAnalytics = true;
    notifyListeners();

    try {
      final response = await ApiService.get(
        '/restaurant/discounts/analytics?period=$period',
      );

      if (response['success']) {
        _analytics = response['data'];
      } else {
        throw Exception(response['message'] ?? 'Failed to load analytics');
      }
    } catch (e) {
      debugPrint('Load analytics error: $e');
      // Don't throw here, just log the error
    } finally {
      _isLoadingAnalytics = false;
      notifyListeners();
    }
  }

  // Get discount by ID
  Discount? getDiscountById(int id) {
    try {
      return _discounts.firstWhere((discount) => discount.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get active discounts
  List<Discount> get activeDiscounts {
    return _discounts.where((discount) => discount.isCurrentlyActive).toList();
  }

  // Get expired discounts
  List<Discount> get expiredDiscounts {
    return _discounts.where((discount) => discount.isExpired).toList();
  }

  // Get scheduled discounts
  List<Discount> get scheduledDiscounts {
    return _discounts.where((discount) => discount.isScheduled).toList();
  }

  // Get discounts by type
  List<Discount> getDiscountsByType(DiscountType type) {
    return _discounts
        .where((discount) => discount.discountType == type)
        .toList();
  }

  // Get total savings generated
  double get totalSavings {
    return _discounts.fold(
      0.0,
      (sum, discount) => sum + (discount.totalSavings ?? 0.0),
    );
  }

  // Get total usage count
  int get totalUsage {
    return _discounts.fold(
      0,
      (sum, discount) => sum + (discount.totalUsageCount ?? 0),
    );
  }

  // Get performance metrics
  Map<String, dynamic> get performanceMetrics {
    final totalDiscounts = _discounts.length;
    final activeCount = activeDiscounts.length;
    final expiredCount = expiredDiscounts.length;
    final scheduledCount = scheduledDiscounts.length;

    return {
      'total_discounts': totalDiscounts,
      'active_discounts': activeCount,
      'expired_discounts': expiredCount,
      'scheduled_discounts': scheduledCount,
      'total_savings': totalSavings,
      'total_usage': totalUsage,
      'average_usage_per_discount': totalDiscounts > 0
          ? totalUsage / totalDiscounts
          : 0.0,
    };
  }

  // Validate discount data
  String? validateDiscountData(Map<String, dynamic> data) {
    if (data['name'] == null || data['name'].toString().trim().isEmpty) {
      return 'Discount name is required';
    }

    if (data['discount_value'] == null ||
        double.tryParse(data['discount_value'].toString()) == null) {
      return 'Valid discount value is required';
    }

    final discountValue = double.parse(data['discount_value'].toString());
    if (data['discount_type'] == 'percentage' &&
        (discountValue <= 0 || discountValue > 100)) {
      return 'Percentage discount must be between 1 and 100';
    }

    if (data['discount_type'] == 'fixed_amount' && discountValue <= 0) {
      return 'Fixed amount discount must be greater than 0';
    }

    if (data['start_date'] == null || data['end_date'] == null) {
      return 'Start date and end date are required';
    }

    final startDate = DateTime.tryParse(data['start_date'].toString());
    final endDate = DateTime.tryParse(data['end_date'].toString());

    if (startDate == null || endDate == null) {
      return 'Invalid date format';
    }

    if (endDate.isBefore(startDate)) {
      return 'End date must be after start date';
    }

    if (data['usage_limit_per_customer'] != null) {
      final limitPerCustomer = int.tryParse(
        data['usage_limit_per_customer'].toString(),
      );
      if (limitPerCustomer == null || limitPerCustomer < 1) {
        return 'Usage limit per customer must be at least 1';
      }
    }

    return null; // No validation errors
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Refresh data
  Future<void> refresh() async {
    await loadDiscounts();
    await loadAnalytics();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  @override
  void dispose() {
    super.dispose();
  }
}
