import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../models/order.dart';

class ApiService {
  // Dynamic base URL based on platform
  static String get baseUrl {
    if (kIsWeb) {
      return 'http://localhost:3000/api';
    } else if (Platform.isAndroid) {
      // Use your actual Wi-Fi IP address for physical device
      // Use ******** for emulator
      return 'http://**************:3000/api';
    } else {
      return 'http://localhost:3000/api';
    }
  }

  // Retrieve saved token (JWT) - using consistent key with other apps
  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  // Store token
  static Future<void> setToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('auth_token', token);
  }

  // Remove token
  static Future<void> removeToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
  }

  // Get headers with auth token
  static Future<Map<String, String>> getHeaders() async {
    final token = await getToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // Register driver
  static Future<Map<String, dynamic>> register({
    required String email,
    required String password,
  }) async {
    print('🔗 Attempting to connect to: $baseUrl/auth/register');

    try {
      final response = await http
          .post(
            Uri.parse('$baseUrl/auth/register'),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({
              'email': email,
              'password': password,
              'userType': 'driver',
            }),
          )
          .timeout(const Duration(seconds: 10));

      print('📡 Response status: ${response.statusCode}');
      print('📡 Response body: ${response.body}');

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        await setToken(data['token']);
        return data;
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['error'] ?? 'Registration failed');
      }
    } catch (e) {
      print('❌ Registration error: $e');
      throw Exception('Connection failed: $e');
    }
  }

  // Login driver
  static Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    print('🔗 Attempting to login at: $baseUrl/auth/login');

    try {
      final response = await http
          .post(
            Uri.parse('$baseUrl/auth/login'),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({'email': email, 'password': password}),
          )
          .timeout(const Duration(seconds: 10));

      print('📡 Login response status: ${response.statusCode}');
      print('📡 Login response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        await setToken(data['token']);
        return data;
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['error'] ?? 'Login failed');
      }
    } catch (e) {
      print('❌ Login error: $e');
      throw Exception('Connection failed: $e');
    }
  }

  // Get current user profile
  static Future<Map<String, dynamic>> getCurrentUser() async {
    final headers = await getHeaders();
    final response = await http.get(
      Uri.parse('$baseUrl/auth/me'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get user profile');
    }
  }

  // Complete driver profile
  static Future<Map<String, dynamic>> completeProfile({
    required String fullName,
    required String phone,
    required String licenseNumber,
    required String vehicleType,
    required String vehiclePlate,
  }) async {
    final headers = await getHeaders();
    final response = await http.post(
      Uri.parse('$baseUrl/driver/profile'),
      headers: headers,
      body: jsonEncode({
        'fullName': fullName,
        'phone': phone,
        'licenseNumber': licenseNumber,
        'vehicleType': vehicleType,
        'vehiclePlate': vehiclePlate,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to complete profile');
    }
  }

  // Get driver profile
  static Future<DriverProfile> getDriverProfile() async {
    final headers = await getHeaders();
    final response = await http.get(
      Uri.parse('$baseUrl/driver/profile'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return DriverProfile.fromJson(data['profile']);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get driver profile');
    }
  }

  // Update availability status
  static Future<DriverProfile> updateAvailability({
    required bool isAvailable,
  }) async {
    final headers = await getHeaders();
    final response = await http.patch(
      Uri.parse('$baseUrl/driver/availability'),
      headers: headers,
      body: jsonEncode({'isAvailable': isAvailable}),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return DriverProfile.fromJson(data['profile']);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to update availability');
    }
  }

  // Update driver location
  static Future<DriverProfile> updateLocation({
    required double latitude,
    required double longitude,
  }) async {
    final headers = await getHeaders();
    final response = await http.patch(
      Uri.parse('$baseUrl/driver/location'),
      headers: headers,
      body: jsonEncode({'latitude': latitude, 'longitude': longitude}),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return DriverProfile.fromJson(data['profile']);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to update location');
    }
  }

  // Get current assignment (new system)
  static Future<Map<String, dynamic>?> getCurrentAssignment() async {
    final headers = await getHeaders();

    final response = await http.get(
      Uri.parse('$baseUrl/driver/current-assignment'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['assignment'];
    } else if (response.statusCode == 404) {
      return null; // No current assignment
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get current assignment');
    }
  }

  // Get available orders for pickup
  static Future<List<Order>> getAvailableOrders() async {
    final headers = await getHeaders();
    print('🔑 Headers: $headers');
    final response = await http.get(
      Uri.parse('$baseUrl/driver/available-orders'),
      headers: headers,
    );

    print('📡 Available orders response status: ${response.statusCode}');
    print('📡 Available orders response body: ${response.body}');

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final ordersList = data['orders'] as List;
      print('📋 Raw orders list length: ${ordersList.length}');

      if (ordersList.isEmpty) {
        print('⚠️ No orders returned from API');
        return [];
      }

      final orders = ordersList.map((orderJson) {
        print('🔄 Parsing order: $orderJson');
        try {
          final order = Order.fromJson(orderJson);
          print(
            '✅ Parsed order: ${order.orderNumber}, Fee: ${order.deliveryFee}',
          );
          return order;
        } catch (e) {
          print('❌ Error parsing order: $e');
          rethrow;
        }
      }).toList();
      print('📦 Total available orders parsed: ${orders.length}');
      return orders;
    } else {
      final error = jsonDecode(response.body);
      print('❌ API Error: ${error}');
      throw Exception(error['error'] ?? 'Failed to get available orders');
    }
  }

  // Decline an order
  static Future<void> declineOrder(int orderId) async {
    final headers = await getHeaders();
    final response = await http.post(
      Uri.parse('$baseUrl/driver/decline-order/$orderId'),
      headers: headers,
    );

    if (response.statusCode != 200) {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to decline order');
    }
  }

  // Accept an order
  static Future<Order> acceptOrder(int orderId) async {
    final headers = await getHeaders();
    final response = await http.post(
      Uri.parse('$baseUrl/driver/accept-order/$orderId'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return Order.fromJson(data['order']);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to accept order');
    }
  }

  // Get driver's current orders
  static Future<List<Order>> getMyOrders() async {
    final headers = await getHeaders();
    final response = await http.get(
      Uri.parse('$baseUrl/driver/my-orders'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return (data['orders'] as List)
          .map((orderJson) => Order.fromJson(orderJson))
          .toList();
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get my orders');
    }
  }

  // Get order details with items
  static Future<Order> getOrderDetails(int orderId) async {
    final headers = await getHeaders();
    final response = await http.get(
      Uri.parse('$baseUrl/driver/orders/$orderId'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return Order.fromJson(data['order']);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get order details');
    }
  }

  // Update order status (picked_up, delivered, etc.)
  static Future<Order> updateOrderStatus(int orderId, String status) async {
    final headers = await getHeaders();
    final response = await http.patch(
      Uri.parse('$baseUrl/driver/update-order-status/$orderId'),
      headers: headers,
      body: jsonEncode({'status': status}),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return Order.fromJson(data['order']);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to update order status');
    }
  }

  // Update order status with location validation
  static Future<Order> updateOrderStatusWithLocation(
    int orderId,
    String status,
    double latitude,
    double longitude,
  ) async {
    final headers = await getHeaders();
    final response = await http.patch(
      Uri.parse('$baseUrl/driver/update-order-status/$orderId'),
      headers: headers,
      body: jsonEncode({
        'status': status,
        'driverLatitude': latitude,
        'driverLongitude': longitude,
      }),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return Order.fromJson(data['order']);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to update order status');
    }
  }

  // Mark order as delivered
  static Future<Order> completeOrder(int orderId) async {
    final headers = await getHeaders();
    final response = await http.patch(
      Uri.parse('$baseUrl/driver/complete-order/$orderId'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return Order.fromJson(data['order']);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to complete order');
    }
  }

  // Get driver statistics
  static Future<Map<String, dynamic>> getDriverStats() async {
    final headers = await getHeaders();
    final response = await http.get(
      Uri.parse('$baseUrl/driver/stats'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get driver statistics');
    }
  }

  // Logout
  static Future<void> logout() async {
    await removeToken();
  }
}
