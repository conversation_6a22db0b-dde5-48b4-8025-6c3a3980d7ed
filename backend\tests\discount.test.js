const request = require('supertest');
const app = require('../app');
const { query } = require('../config/database');
const DiscountValidator = require('../utils/discountValidator');

describe('Discount System Tests', () => {
  let adminToken, restaurantToken, customerToken;
  let testRestaurantId, testCustomerId, testDiscountId;

  beforeAll(async () => {
    // Setup test data
    await setupTestData();
  });

  afterAll(async () => {
    // Cleanup test data
    await cleanupTestData();
  });

  describe('Admin Discount Management', () => {
    test('should create platform-wide discount', async () => {
      const discountData = {
        name: 'Platform Welcome Discount',
        description: 'Welcome discount for new customers',
        discountType: 'percentage',
        discountValue: 20,
        scope: 'platform',
        minimumOrderAmount: 25,
        usageLimitPerCustomer: 1,
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      };

      const response = await request(app)
        .post('/api/admin/discounts')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(discountData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.discount.name).toBe(discountData.name);
      testDiscountId = response.body.data.discount.id;
    });

    test('should get all discounts with pagination', async () => {
      const response = await request(app)
        .get('/api/admin/discounts?page=1&limit=10')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.discounts).toBeInstanceOf(Array);
      expect(response.body.data.pagination).toBeDefined();
    });

    test('should update discount', async () => {
      const updateData = {
        name: 'Updated Platform Discount',
        discountValue: 25
      };

      const response = await request(app)
        .put(`/api/admin/discounts/${testDiscountId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.discount.name).toBe(updateData.name);
    });

    test('should get discount analytics', async () => {
      const response = await request(app)
        .get('/api/admin/discounts/analytics?period=30d')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.overview).toBeDefined();
      expect(response.body.data.discountTypes).toBeInstanceOf(Array);
    });
  });

  describe('Restaurant Discount Management', () => {
    test('should create restaurant-specific discount', async () => {
      const discountData = {
        name: 'Restaurant Special',
        description: 'Special discount for our restaurant',
        discountType: 'fixed_amount',
        discountValue: 5,
        scope: 'restaurant',
        minimumOrderAmount: 20,
        usageLimitPerCustomer: 2,
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      };

      const response = await request(app)
        .post('/api/restaurant/discounts')
        .set('Authorization', `Bearer ${restaurantToken}`)
        .send(discountData)
        .expect(201);

      expect(response.body.message).toBe('Discount created successfully');
      expect(response.body.discount.name).toBe(discountData.name);
    });

    test('should get restaurant discounts', async () => {
      const response = await request(app)
        .get('/api/restaurant/discounts')
        .set('Authorization', `Bearer ${restaurantToken}`)
        .expect(200);

      expect(response.body.discounts).toBeInstanceOf(Array);
      expect(response.body.pagination).toBeDefined();
    });

    test('should get restaurant analytics', async () => {
      const response = await request(app)
        .get('/api/restaurant/discounts/analytics?period=30d')
        .set('Authorization', `Bearer ${restaurantToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.overview).toBeDefined();
    });
  });

  describe('Customer Discount Usage', () => {
    test('should get available discounts', async () => {
      const response = await request(app)
        .get(`/api/customer/discounts/available?restaurantId=${testRestaurantId}`)
        .set('Authorization', `Bearer ${customerToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.discounts).toBeInstanceOf(Array);
    });

    test('should validate discount code', async () => {
      // First create a discount with a code
      const discountData = {
        code: 'TEST20',
        name: 'Test Code Discount',
        discountType: 'percentage',
        discountValue: 20,
        scope: 'platform',
        minimumOrderAmount: 10,
        usageLimitPerCustomer: 1,
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      };

      await request(app)
        .post('/api/admin/discounts')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(discountData)
        .expect(201);

      // Now validate the code
      const response = await request(app)
        .post('/api/customer/discounts/validate-code')
        .set('Authorization', `Bearer ${customerToken}`)
        .send({
          code: 'TEST20',
          restaurantId: testRestaurantId,
          orderItems: [],
          subtotalAmount: 25
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.discount).toBeDefined();
      expect(response.body.data.discountAmount).toBeGreaterThan(0);
    });

    test('should apply discount in order', async () => {
      const orderData = {
        restaurantId: testRestaurantId,
        items: [
          {
            menuItemId: 1,
            quantity: 2,
            unitPrice: 15,
            specialInstructions: null
          }
        ],
        deliveryAddress: '123 Test Street',
        deliveryLatitude: 40.7128,
        deliveryLongitude: -74.0060,
        deliveryFee: 3.99,
        discountId: testDiscountId,
        discountAmount: 6.00
      };

      const response = await request(app)
        .post('/api/customer/orders')
        .set('Authorization', `Bearer ${customerToken}`)
        .send(orderData)
        .expect(201);

      expect(response.body.message).toBe('Order created successfully');
      expect(response.body.order.discountAmount).toBe(6.00);
    });
  });

  describe('Discount Validation Logic', () => {
    test('should validate discount correctly', async () => {
      const result = await DiscountValidator.validateDiscount(
        testDiscountId,
        testCustomerId,
        testRestaurantId,
        [{ menuItemId: 1, quantity: 2, unitPrice: 15 }],
        30
      );

      expect(result.isValid).toBe(true);
      expect(result.discountAmount).toBeGreaterThan(0);
    });

    test('should reject expired discount', async () => {
      // Create expired discount
      const expiredDiscountResult = await query(`
        INSERT INTO discounts (
          name, discount_type, discount_value, scope, restaurant_id,
          minimum_order_amount, usage_limit_per_customer,
          start_date, end_date, is_active
        ) VALUES (
          'Expired Discount', 'percentage', 10, 'platform', NULL,
          0, 1, '2023-01-01', '2023-01-02', true
        ) RETURNING id
      `);

      const expiredDiscountId = expiredDiscountResult.rows[0].id;

      const result = await DiscountValidator.validateDiscount(
        expiredDiscountId,
        testCustomerId,
        testRestaurantId,
        [],
        30
      );

      expect(result.isValid).toBe(false);
      expect(result.errorCode).toBe('DISCOUNT_EXPIRED');
    });

    test('should reject discount below minimum order amount', async () => {
      const result = await DiscountValidator.validateDiscount(
        testDiscountId,
        testCustomerId,
        testRestaurantId,
        [],
        10 // Below minimum order amount
      );

      expect(result.isValid).toBe(false);
      expect(result.errorCode).toBe('MINIMUM_ORDER_NOT_MET');
    });

    test('should calculate discount amount correctly', async () => {
      // Test percentage discount
      const percentageResult = await DiscountValidator.validateDiscount(
        testDiscountId,
        testCustomerId,
        testRestaurantId,
        [],
        100
      );

      expect(percentageResult.isValid).toBe(true);
      expect(percentageResult.discountAmount).toBe(25); // 25% of 100
    });
  });

  describe('Edge Cases and Error Handling', () => {
    test('should handle invalid discount ID', async () => {
      const response = await request(app)
        .get('/api/admin/discounts/99999')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);

      expect(response.body.error).toBe('Discount not found');
    });

    test('should prevent duplicate discount codes', async () => {
      const discountData = {
        code: 'DUPLICATE',
        name: 'First Discount',
        discountType: 'percentage',
        discountValue: 10,
        scope: 'platform',
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      };

      // Create first discount
      await request(app)
        .post('/api/admin/discounts')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(discountData)
        .expect(201);

      // Try to create duplicate
      const response = await request(app)
        .post('/api/admin/discounts')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ ...discountData, name: 'Second Discount' })
        .expect(400);

      expect(response.body.error).toBe('Discount code already exists');
    });

    test('should handle usage limit exceeded', async () => {
      // Create discount with usage limit of 1
      const limitedDiscountResult = await query(`
        INSERT INTO discounts (
          name, discount_type, discount_value, scope, restaurant_id,
          minimum_order_amount, usage_limit, usage_limit_per_customer,
          start_date, end_date, is_active
        ) VALUES (
          'Limited Discount', 'percentage', 10, 'platform', NULL,
          0, 1, 1, NOW(), NOW() + INTERVAL '30 days', true
        ) RETURNING id
      `);

      const limitedDiscountId = limitedDiscountResult.rows[0].id;

      // Create usage record to exceed limit
      await query(`
        INSERT INTO discount_usage (discount_id, customer_id, order_id, discount_amount, original_amount, final_amount)
        VALUES ($1, $2, 1, 5, 50, 45)
      `, [limitedDiscountId, testCustomerId]);

      const result = await DiscountValidator.validateDiscount(
        limitedDiscountId,
        testCustomerId,
        testRestaurantId,
        [],
        30
      );

      expect(result.isValid).toBe(false);
      expect(result.errorCode).toBe('USAGE_LIMIT_EXCEEDED');
    });
  });

  // Helper functions
  async function setupTestData() {
    // Create test admin user and token
    const adminResult = await query(`
      INSERT INTO users (email, password, role) 
      VALUES ('<EMAIL>', 'hashedpassword', 'admin') 
      RETURNING id
    `);
    adminToken = 'test-admin-token'; // Mock token

    // Create test restaurant user and profile
    const restaurantUserResult = await query(`
      INSERT INTO users (email, password, role) 
      VALUES ('<EMAIL>', 'hashedpassword', 'restaurant') 
      RETURNING id
    `);
    
    const restaurantProfileResult = await query(`
      INSERT INTO restaurant_profiles (user_id, restaurant_name, address, latitude, longitude, cuisine_type)
      VALUES ($1, 'Test Restaurant', '123 Restaurant St', 40.7128, -74.0060, 'Italian')
      RETURNING id
    `, [restaurantUserResult.rows[0].id]);
    
    testRestaurantId = restaurantProfileResult.rows[0].id;
    restaurantToken = 'test-restaurant-token'; // Mock token

    // Create test customer user and profile
    const customerUserResult = await query(`
      INSERT INTO users (email, password, role) 
      VALUES ('<EMAIL>', 'hashedpassword', 'customer') 
      RETURNING id
    `);
    
    const customerProfileResult = await query(`
      INSERT INTO customer_profiles (user_id, full_name, phone, address, latitude, longitude)
      VALUES ($1, 'Test Customer', '1234567890', '456 Customer Ave', 40.7589, -73.9851)
      RETURNING id
    `, [customerUserResult.rows[0].id]);
    
    testCustomerId = customerProfileResult.rows[0].id;
    customerToken = 'test-customer-token'; // Mock token
  }

  async function cleanupTestData() {
    // Clean up test data in reverse order of creation
    await query('DELETE FROM discount_usage WHERE customer_id = $1', [testCustomerId]);
    await query('DELETE FROM discounts WHERE name LIKE %Test%');
    await query('DELETE FROM customer_profiles WHERE id = $1', [testCustomerId]);
    await query('DELETE FROM restaurant_profiles WHERE id = $1', [testRestaurantId]);
    await query('DELETE FROM users WHERE <NAME_EMAIL>%');
  }
});
