const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');
const driverAssignmentService = require('../services/driverAssignmentService');
const orderAssignmentScheduler = require('../services/orderAssignmentScheduler');

// Get all mart categories
router.get('/categories', async (req, res) => {
  try {
    const result = await query(`
      SELECT * FROM mart_categories 
      WHERE is_active = true 
      ORDER BY sort_order ASC, name ASC
    `);

    res.json({
      categories: result.rows
    });
  } catch (error) {
    console.error('Error fetching mart categories:', error);
    res.status(500).json({ error: 'Failed to fetch categories' });
  }
});

// Get products by category
router.get('/categories/:categoryId/products', async (req, res) => {
  try {
    const { categoryId } = req.params;
    const { limit = 20, offset = 0, search } = req.query;

    let whereClause = 'WHERE mp.category_id = $1 AND mp.is_available = true';
    let queryParams = [categoryId];
    let paramCount = 1;

    if (search) {
      paramCount++;
      whereClause += ` AND (mp.name ILIKE $${paramCount} OR mp.description ILIKE $${paramCount})`;
      queryParams.push(`%${search}%`);
    }

    const result = await query(`
      SELECT 
        mp.*,
        mc.name as category_name
      FROM mart_products mp
      JOIN mart_categories mc ON mp.category_id = mc.id
      ${whereClause}
      ORDER BY mp.name ASC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `, [...queryParams, limit, offset]);

    res.json({
      products: result.rows,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: result.rows.length
      }
    });
  } catch (error) {
    console.error('Error fetching mart products:', error);
    res.status(500).json({ error: 'Failed to fetch products' });
  }
});

// Get all products with search and filtering
router.get('/products', async (req, res) => {
  try {
    const { limit = 20, offset = 0, search, categoryId } = req.query;

    let whereClause = 'WHERE mp.is_available = true';
    let queryParams = [];
    let paramCount = 0;

    if (categoryId) {
      paramCount++;
      whereClause += ` AND mp.category_id = $${paramCount}`;
      queryParams.push(categoryId);
    }

    if (search) {
      paramCount++;
      whereClause += ` AND (mp.name ILIKE $${paramCount} OR mp.description ILIKE $${paramCount} OR mp.brand ILIKE $${paramCount})`;
      queryParams.push(`%${search}%`);
    }

    const result = await query(`
      SELECT 
        mp.*,
        mc.name as category_name
      FROM mart_products mp
      LEFT JOIN mart_categories mc ON mp.category_id = mc.id
      ${whereClause}
      ORDER BY mp.name ASC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `, [...queryParams, limit, offset]);

    res.json({
      products: result.rows,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: result.rows.length
      }
    });
  } catch (error) {
    console.error('Error fetching mart products:', error);
    res.status(500).json({ error: 'Failed to fetch products' });
  }
});

// Get product details
router.get('/products/:productId', async (req, res) => {
  try {
    const { productId } = req.params;

    const result = await query(`
      SELECT 
        mp.*,
        mc.name as category_name
      FROM mart_products mp
      LEFT JOIN mart_categories mc ON mp.category_id = mc.id
      WHERE mp.id = $1
    `, [productId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Product not found' });
    }

    res.json({
      product: result.rows[0]
    });
  } catch (error) {
    console.error('Error fetching product details:', error);
    res.status(500).json({ error: 'Failed to fetch product details' });
  }
});

// Get market locations
router.get('/locations', async (req, res) => {
  try {
    const result = await query(`
      SELECT * FROM market_locations 
      WHERE is_active = true 
      ORDER BY name ASC
    `);

    res.json({
      locations: result.rows
    });
  } catch (error) {
    console.error('Error fetching market locations:', error);
    res.status(500).json({ error: 'Failed to fetch market locations' });
  }
});

// Create mart order
router.post('/orders', 
  authenticateToken, 
  requireRole(['customer']), 
  async (req, res) => {
    try {
      const {
        marketLocationId,
        items,
        deliveryAddress,
        deliveryLatitude,
        deliveryLongitude,
        specialInstructions,
        deliveryFee = 0.00,
        discountId,
        discountAmount = 0.00
      } = req.body;
      
      const userId = req.user.id;

      // Get customer profile
      const customerProfile = await query(
        'SELECT id FROM customer_profiles WHERE user_id = $1',
        [userId]
      );

      if (customerProfile.rows.length === 0) {
        return res.status(400).json({ error: 'Customer profile not found' });
      }

      const customerId = customerProfile.rows[0].id;

      // Validate market location exists
      const marketLocation = await query(
        'SELECT id FROM market_locations WHERE id = $1 AND is_active = true',
        [marketLocationId]
      );

      if (marketLocation.rows.length === 0) {
        return res.status(400).json({ error: 'Market location not found or inactive' });
      }

      // Validate items and calculate totals
      if (!items || items.length === 0) {
        return res.status(400).json({ error: 'Order must contain at least one item' });
      }

      let subtotalAmount = 0;
      const validatedItems = [];

      for (const item of items) {
        const productResult = await query(
          'SELECT id, name, price, is_available FROM mart_products WHERE id = $1',
          [item.productId]
        );

        if (productResult.rows.length === 0) {
          return res.status(400).json({ error: `Product ${item.productId} not found` });
        }

        const product = productResult.rows[0];

        if (!product.is_available) {
          return res.status(400).json({ error: `Product ${product.name} is not available` });
        }

        const itemTotal = parseFloat(product.price) * parseInt(item.quantity);
        subtotalAmount += itemTotal;

        validatedItems.push({
          productId: product.id,
          name: product.name,
          price: product.price,
          quantity: item.quantity,
          total: itemTotal,
          specialInstructions: item.specialInstructions || null
        });
      }

      // Apply discount if provided
      let finalDiscountAmount = 0;
      if (discountId && discountAmount > 0) {
        // Validate discount (implement discount validation logic here)
        finalDiscountAmount = Math.min(discountAmount, subtotalAmount);
      }

      const totalAmount = subtotalAmount + parseFloat(deliveryFee) - finalDiscountAmount;

      // Generate order number
      const orderNumber = 'M' + Date.now().toString().slice(-8);

      // Create order
      const orderResult = await query(
        `INSERT INTO orders
         (customer_id, market_location_id, order_type, order_number, status, total_amount, subtotal_amount,
          delivery_fee, discount_id, discount_amount, delivery_address, delivery_latitude,
          delivery_longitude, special_instructions, estimated_delivery_time)
         VALUES ($1, $2, 'mart', $3, 'pending', $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
         RETURNING *`,
        [
          customerId,
          marketLocationId,
          orderNumber,
          totalAmount,
          subtotalAmount,
          deliveryFee,
          discountId || null,
          finalDiscountAmount,
          deliveryAddress,
          deliveryLatitude,
          deliveryLongitude,
          specialInstructions,
          new Date(Date.now() + 30 * 60 * 1000) // 30 minutes from now
        ]
      );

      const order = orderResult.rows[0];

      // Create order items
      for (const item of validatedItems) {
        await query(
          `INSERT INTO order_items 
           (order_id, mart_product_id, quantity, unit_price, total_price, special_instructions)
           VALUES ($1, $2, $3, $4, $5, $6)`,
          [
            order.id,
            item.productId,
            item.quantity,
            item.price,
            item.total,
            item.specialInstructions
          ]
        );
      }

      // Update order status to ready (mart orders skip restaurant confirmation)
      await query(
        'UPDATE orders SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        ['ready', order.id]
      );

      // Trigger driver assignment
      setTimeout(() => {
        orderAssignmentScheduler.triggerOrderAssignment(order.id, 'mart');
      }, 1000);

      res.status(201).json({
        message: 'Mart order created successfully',
        order: {
          ...order,
          items: validatedItems
        }
      });
    } catch (error) {
      console.error('Error creating mart order:', error);
      res.status(500).json({ error: 'Failed to create order' });
    }
  }
);

module.exports = router;
