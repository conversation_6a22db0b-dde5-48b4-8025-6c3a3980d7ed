const { pool } = require('../config/database');

const martMigrations = [
  // Market locations for mart orders
  `
    CREATE TABLE IF NOT EXISTS market_locations (
      id SERIAL PRIMARY KEY,
      name VARCHA<PERSON>(255) NOT NULL,
      address TEXT NOT NULL,
      latitude DECIMAL(10, 8) NOT NULL,
      longitude DECIMAL(11, 8) NOT NULL,
      phone VARCHAR(20),
      operating_hours JSONB,
      is_active BOOLEAN DEFAULT true,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,

  // Mart categories
  `
    CREATE TABLE IF NOT EXISTS mart_categories (
      id SERIAL PRIMARY KEY,
      name VARCHAR(255) NOT NULL,
      description TEXT,
      image_url TEXT,
      is_active BOOLEAN DEFAULT true,
      sort_order INTEGER DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,

  // Mart products
  `
    CREATE TABLE IF NOT EXISTS mart_products (
      id SERIAL PRIMARY KEY,
      category_id INTEGER REFERENCES mart_categories(id) ON DELETE SET NULL,
      name VARCHAR(255) NOT NULL,
      description TEXT,
      price DECIMAL(10, 2) NOT NULL,
      image_url TEXT,
      is_available BOOLEAN DEFAULT true,
      stock_quantity INTEGER DEFAULT 0,
      unit VARCHAR(50) DEFAULT 'piece',
      barcode VARCHAR(100),
      brand VARCHAR(100),
      weight DECIMAL(8, 2),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,

  // Driver order assignments - tracks which driver is currently assigned to an order
  `
    CREATE TABLE IF NOT EXISTS driver_order_assignments (
      id SERIAL PRIMARY KEY,
      order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
      driver_id INTEGER REFERENCES driver_profiles(id) ON DELETE CASCADE,
      assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      expires_at TIMESTAMP NOT NULL,
      status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'expired')),
      responded_at TIMESTAMP,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(order_id, driver_id)
    );
  `,

  // Add new columns to orders table for mart support
  `
    ALTER TABLE orders 
    ADD COLUMN IF NOT EXISTS market_location_id INTEGER REFERENCES market_locations(id) ON DELETE SET NULL,
    ADD COLUMN IF NOT EXISTS order_type VARCHAR(20) DEFAULT 'restaurant' CHECK (order_type IN ('restaurant', 'mart')),
    ADD COLUMN IF NOT EXISTS subtotal_amount DECIMAL(10, 2) DEFAULT 0.00,
    ADD COLUMN IF NOT EXISTS discount_id INTEGER,
    ADD COLUMN IF NOT EXISTS discount_amount DECIMAL(10, 2) DEFAULT 0.00,
    ADD COLUMN IF NOT EXISTS assigned_at TIMESTAMP,
    ADD COLUMN IF NOT EXISTS assignment_expires_at TIMESTAMP;
  `,

  // Add constraint to ensure proper order type relationships
  `
    ALTER TABLE orders 
    ADD CONSTRAINT IF NOT EXISTS orders_type_check CHECK (
      (order_type = 'restaurant' AND restaurant_id IS NOT NULL AND market_location_id IS NULL) OR
      (order_type = 'mart' AND restaurant_id IS NULL AND market_location_id IS NOT NULL)
    );
  `,

  // Add new columns to order_items table for mart support
  `
    ALTER TABLE order_items 
    ADD COLUMN IF NOT EXISTS mart_product_id INTEGER REFERENCES mart_products(id) ON DELETE CASCADE;
  `,

  // Add constraint to ensure proper item type relationships
  `
    ALTER TABLE order_items 
    ADD CONSTRAINT IF NOT EXISTS order_items_type_check CHECK (
      (menu_item_id IS NOT NULL AND mart_product_id IS NULL) OR
      (menu_item_id IS NULL AND mart_product_id IS NOT NULL)
    );
  `,

  // Create indexes for mart tables
  `
    CREATE INDEX IF NOT EXISTS idx_orders_market_location ON orders(market_location_id);
    CREATE INDEX IF NOT EXISTS idx_orders_type ON orders(order_type);
    CREATE INDEX IF NOT EXISTS idx_orders_assignment_expires ON orders(assignment_expires_at);
    CREATE INDEX IF NOT EXISTS idx_mart_products_category ON mart_products(category_id);
    CREATE INDEX IF NOT EXISTS idx_mart_products_available ON mart_products(is_available);
    CREATE INDEX IF NOT EXISTS idx_order_items_mart_product ON order_items(mart_product_id);
    CREATE INDEX IF NOT EXISTS idx_driver_assignments_order ON driver_order_assignments(order_id);
    CREATE INDEX IF NOT EXISTS idx_driver_assignments_driver ON driver_order_assignments(driver_id);
    CREATE INDEX IF NOT EXISTS idx_driver_assignments_status ON driver_order_assignments(status);
    CREATE INDEX IF NOT EXISTS idx_driver_assignments_expires ON driver_order_assignments(expires_at);
  `,

  // Insert default mart categories
  `
    INSERT INTO mart_categories (name, description, sort_order) VALUES
    ('Groceries', 'Fresh groceries and daily essentials', 1),
    ('Beverages', 'Drinks and beverages', 2),
    ('Snacks', 'Snacks and quick bites', 3),
    ('Personal Care', 'Personal care and hygiene products', 4),
    ('Household', 'Household items and cleaning supplies', 5)
    ON CONFLICT DO NOTHING;
  `,

  // Insert default market location (can be updated by admin)
  `
    INSERT INTO market_locations (name, address, latitude, longitude, phone, operating_hours) VALUES
    ('Main Market Hub', '123 Market Street, City Center', 40.7128, -74.0060, '+1234567890', 
     '{"monday": "06:00-22:00", "tuesday": "06:00-22:00", "wednesday": "06:00-22:00", "thursday": "06:00-22:00", "friday": "06:00-22:00", "saturday": "06:00-22:00", "sunday": "08:00-20:00"}')
    ON CONFLICT DO NOTHING;
  `
];

async function runMartMigrations() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 Starting mart system migrations...');
    
    for (let i = 0; i < martMigrations.length; i++) {
      console.log(`📝 Running mart migration ${i + 1}/${martMigrations.length}...`);
      await client.query(martMigrations[i]);
    }
    
    console.log('✅ All mart migrations completed successfully!');
  } catch (error) {
    console.error('❌ Mart migration failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run migrations if this file is executed directly
if (require.main === module) {
  runMartMigrations()
    .then(() => {
      console.log('🎉 Mart system setup complete!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Mart migration failed:', error);
      process.exit(1);
    });
}

module.exports = { runMartMigrations };
