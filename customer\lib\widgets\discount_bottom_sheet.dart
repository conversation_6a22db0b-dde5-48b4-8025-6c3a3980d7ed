import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/discount.dart';
import '../providers/cart_provider.dart';
import '../shared/app_colors.dart';
import '../shared/app_dimensions.dart';
import 'discount_card.dart';

class DiscountBottomSheet extends StatefulWidget {
  final List<Discount> availableDiscounts;

  const DiscountBottomSheet({
    super.key,
    required this.availableDiscounts,
  });

  @override
  State<DiscountBottomSheet> createState() => _DiscountBottomSheetState();
}

class _DiscountBottomSheetState extends State<DiscountBottomSheet> {
  bool _isApplyingCode = false;
  String? _codeError;

  @override
  Widget build(BuildContext context) {
    return Consumer<CartProvider>(
      builder: (context, cartProvider, child) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              
              // Header
              Padding(
                padding: const EdgeInsets.all(AppDimensions.paddingL),
                child: Row(
                  children: [
                    Text(
                      'Available Discounts',
                      style: GoogleFonts.poppins(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[800],
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                      color: Colors.grey[600],
                    ),
                  ],
                ),
              ),
              
              // Content
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.paddingL,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Current discount display
                      if (cartProvider.hasDiscount) ...[
                        Container(
                          padding: const EdgeInsets.all(AppDimensions.paddingM),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: AppColors.primary),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.check_circle,
                                color: AppColors.primary,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Discount Applied',
                                      style: GoogleFonts.poppins(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.primary,
                                      ),
                                    ),
                                    Text(
                                      cartProvider.appliedDiscount?.name ?? 'Unknown',
                                      style: GoogleFonts.poppins(
                                        fontSize: 14,
                                        color: Colors.grey[700],
                                      ),
                                    ),
                                    Text(
                                      'You saved \$${cartProvider.discountAmount.toStringAsFixed(2)}',
                                      style: GoogleFonts.poppins(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.primary,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              TextButton(
                                onPressed: () {
                                  cartProvider.removeDiscount();
                                  Navigator.pop(context);
                                },
                                child: Text(
                                  'Remove',
                                  style: GoogleFonts.poppins(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.red[600],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],
                      
                      // Discount code input
                      if (!cartProvider.hasDiscount)
                        DiscountCodeInput(
                          onApply: _applyDiscountCode,
                          isLoading: _isApplyingCode,
                          errorMessage: _codeError,
                        ),
                      
                      if (!cartProvider.hasDiscount) ...[
                        const SizedBox(height: 24),
                        
                        // Available discounts section
                        if (widget.availableDiscounts.isNotEmpty) ...[
                          Text(
                            'Available Offers',
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[800],
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          // Discount list
                          ...widget.availableDiscounts.map((discount) {
                            return DiscountCard(
                              discount: discount,
                              onTap: () => _applyDiscount(discount),
                              showApplyButton: true,
                            );
                          }).toList(),
                        ] else ...[
                          // No discounts available
                          Container(
                            padding: const EdgeInsets.all(AppDimensions.paddingL),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.local_offer_outlined,
                                  size: 64,
                                  color: Colors.grey[400],
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'No discounts available',
                                  style: GoogleFonts.poppins(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.grey[600],
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Check back later for new offers and deals',
                                  style: GoogleFonts.poppins(
                                    fontSize: 14,
                                    color: Colors.grey[500],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                      
                      // Bottom padding
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _applyDiscountCode(String code) async {
    setState(() {
      _isApplyingCode = true;
      _codeError = null;
    });

    final cartProvider = Provider.of<CartProvider>(context, listen: false);
    final success = await cartProvider.applyDiscount(code);

    setState(() {
      _isApplyingCode = false;
    });

    if (success) {
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Discount applied successfully!'),
          backgroundColor: AppColors.primary,
          duration: const Duration(seconds: 2),
        ),
      );
    } else {
      setState(() {
        _codeError = cartProvider.error;
      });
    }
  }

  void _applyDiscount(Discount discount) {
    final cartProvider = Provider.of<CartProvider>(context, listen: false);
    
    if (discount.code != null) {
      _applyDiscountCode(discount.code!);
    } else {
      // For discounts without codes, apply directly
      final updatedCart = cartProvider.cart?.applyDiscount(discount);
      if (updatedCart != null) {
        // This would need to be implemented in the cart provider
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Discount applied successfully!'),
            backgroundColor: AppColors.primary,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }
}

// Helper function to show the discount bottom sheet
void showDiscountBottomSheet(BuildContext context, List<Discount> availableDiscounts) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => DraggableScrollableSheet(
      initialChildSize: 0.7,
      minChildSize: 0.5,
      maxChildSize: 0.9,
      builder: (context, scrollController) => DiscountBottomSheet(
        availableDiscounts: availableDiscounts,
      ),
    ),
  );
}
