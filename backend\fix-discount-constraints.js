#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to fix discount table constraints
 */

const { query } = require('./config/database');

async function fixDiscountConstraints() {
  try {
    console.log('🔧 Checking and fixing discount table constraints...\n');

    // Check if the valid_dates constraint exists
    const constraintCheck = await query(`
      SELECT constraint_name, check_clause
      FROM information_schema.check_constraints
      WHERE constraint_name = 'valid_dates'
      AND constraint_schema = 'public'
    `);

    if (constraintCheck.rows.length > 0) {
      console.log('Found existing valid_dates constraint:');
      console.log('Constraint:', constraintCheck.rows[0].check_clause);
      
      // Drop the existing constraint
      console.log('\n🗑️  Dropping existing constraint...');
      await query('ALTER TABLE discounts DROP CONSTRAINT IF EXISTS valid_dates');
      console.log('✅ Existing constraint dropped');
    } else {
      console.log('No existing valid_dates constraint found');
    }

    // Add a proper constraint that allows same-day discounts but requires end_date >= start_date
    console.log('\n➕ Adding new date validation constraint...');
    await query(`
      ALTER TABLE discounts 
      ADD CONSTRAINT valid_dates 
      CHECK (end_date >= start_date)
    `);
    console.log('✅ New constraint added: end_date >= start_date');

    // Test the constraint with a valid example
    console.log('\n🧪 Testing constraint with valid dates...');
    const testResult = await query(`
      SELECT 
        CASE 
          WHEN '2025-07-25 23:59:59'::timestamp >= '2025-07-25 00:00:00'::timestamp 
          THEN 'Valid: Same day allowed'
          ELSE 'Invalid'
        END as test_result
    `);
    console.log('Test result:', testResult.rows[0].test_result);

    // Check current discounts that might violate the constraint
    console.log('\n📊 Checking existing discounts for constraint violations...');
    const violatingDiscounts = await query(`
      SELECT id, name, start_date, end_date
      FROM discounts
      WHERE end_date < start_date
    `);

    if (violatingDiscounts.rows.length > 0) {
      console.log('⚠️  Found discounts with invalid dates:');
      violatingDiscounts.rows.forEach(discount => {
        console.log(`   ID ${discount.id}: ${discount.name} (${discount.start_date} to ${discount.end_date})`);
      });
      
      console.log('\n🔧 Fixing invalid discounts...');
      for (const discount of violatingDiscounts.rows) {
        const newEndDate = new Date(discount.start_date);
        newEndDate.setDate(newEndDate.getDate() + 1); // Add 1 day
        
        await query(`
          UPDATE discounts 
          SET end_date = $1 
          WHERE id = $2
        `, [newEndDate, discount.id]);
        
        console.log(`   ✅ Fixed discount ID ${discount.id}: end date set to ${newEndDate.toISOString()}`);
      }
    } else {
      console.log('✅ No discounts with invalid dates found');
    }

    console.log('\n🎉 Discount constraints fixed successfully!');
    console.log('\nNow you can create discounts with:');
    console.log('- Same day: start_date = end_date (allowed)');
    console.log('- Multiple days: end_date > start_date (allowed)');
    console.log('- Invalid: end_date < start_date (blocked)');

  } catch (error) {
    console.error('❌ Error fixing constraints:', error);
    
    if (error.code === '23514') {
      console.log('\n💡 This error suggests there are existing discounts with invalid dates.');
      console.log('   Please check and fix any discounts where end_date < start_date');
    }
  } finally {
    process.exit(0);
  }
}

fixDiscountConstraints();
