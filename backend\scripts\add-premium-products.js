require('dotenv').config();
const { query } = require('../config/database');

async function addPremiumProducts() {
  try {
    console.log('🌟 Adding premium and local products...');

    // Get category IDs
    const categoriesResult = await query('SELECT id, name FROM mart_categories');
    const categoryIds = {};
    categoriesResult.rows.forEach(cat => {
      categoryIds[cat.name] = cat.id;
    });

    // Premium and local products
    const premiumProducts = [
      // Premium Fruits
      { category: 'Fresh Fruits', name: 'Organic Avocados', description: 'Premium organic avocados', price: 8.99, unit: 'kg', stock: 20, brand: 'Organic Plus', image: 'https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=400' },
      { category: 'Fresh Fruits', name: 'Dragon Fruit', description: 'Exotic dragon fruit', price: 12.99, unit: 'kg', stock: 15, brand: 'Exotic Fruits', image: 'https://images.unsplash.com/photo-1526318472351-c75fcf070305?w=400' },
      { category: 'Fresh Fruits', name: 'Pomegranate', description: 'Fresh pomegranate', price: 6.99, unit: 'kg', stock: 25, brand: 'Middle East Fresh', image: 'https://images.unsplash.com/photo-1553575992-6b7dc0d3e8b8?w=400' },
      
      // Local Specialties
      { category: 'Pantry Staples', name: 'Kurdish Honey', description: 'Pure mountain honey from Kurdistan', price: 15.99, unit: 'jar', stock: 30, brand: 'Mountain Gold', image: 'https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=400' },
      { category: 'Pantry Staples', name: 'Sumac Spice', description: 'Traditional Middle Eastern sumac', price: 4.99, unit: 'pack', stock: 40, brand: 'Spice Bazaar', image: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=400' },
      { category: 'Pantry Staples', name: 'Za\'atar Mix', description: 'Traditional za\'atar herb mix', price: 3.99, unit: 'pack', stock: 35, brand: 'Heritage Spices', image: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=400' },
      { category: 'Pantry Staples', name: 'Tahini', description: 'Pure sesame tahini paste', price: 7.99, unit: 'jar', stock: 25, brand: 'Sesame King', image: 'https://images.unsplash.com/photo-1609501676725-7186f0a1b4d3?w=400' },
      
      // Premium Dairy
      { category: 'Dairy & Eggs', name: 'Buffalo Mozzarella', description: 'Fresh buffalo mozzarella', price: 9.99, unit: 'pack', stock: 20, brand: 'Italian Imports', image: 'https://images.unsplash.com/photo-1628088062854-d1870b4553da?w=400' },
      { category: 'Dairy & Eggs', name: 'Organic Butter', description: 'Organic grass-fed butter', price: 6.99, unit: 'pack', stock: 30, brand: 'Pure Pastures', image: 'https://images.unsplash.com/photo-1589985270826-4b7bb135bc9d?w=400' },
      
      // Gourmet Items
      { category: 'Pantry Staples', name: 'Truffle Oil', description: 'Premium truffle-infused oil', price: 24.99, unit: 'bottle', stock: 10, brand: 'Gourmet Select', image: 'https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?w=400' },
      { category: 'Snacks & Sweets', name: 'Dark Chocolate 85%', description: 'Premium dark chocolate', price: 4.99, unit: 'bar', stock: 25, brand: 'Artisan Cocoa', image: 'https://images.unsplash.com/photo-**********-cb92caebd54b?w=400' },
      { category: 'Snacks & Sweets', name: 'Pistachio Nuts', description: 'Roasted pistachio nuts', price: 12.99, unit: 'pack', stock: 20, brand: 'Nut Paradise', image: 'https://images.unsplash.com/photo-1599599810694-57a2ca8276a8?w=400' },
      
      // Health & Wellness
      { category: 'Beverages', name: 'Kombucha', description: 'Probiotic kombucha drink', price: 4.99, unit: 'bottle', stock: 30, brand: 'Gut Health', image: 'https://images.unsplash.com/photo-**********-c3190ca9959b?w=400' },
      { category: 'Beverages', name: 'Coconut Water', description: 'Pure coconut water', price: 2.99, unit: 'bottle', stock: 40, brand: 'Tropical Pure', image: 'https://images.unsplash.com/photo-1481671703460-040cb8a2d909?w=400' },
      { category: 'Snacks & Sweets', name: 'Protein Bars', description: 'High-protein energy bars', price: 8.99, unit: 'pack', stock: 35, brand: 'Fit Life', image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400' },
      
      // International Foods
      { category: 'Pantry Staples', name: 'Soy Sauce', description: 'Premium soy sauce', price: 5.99, unit: 'bottle', stock: 30, brand: 'Asian Kitchen', image: 'https://images.unsplash.com/photo-1609501676725-7186f0a1b4d3?w=400' },
      { category: 'Pantry Staples', name: 'Coconut Milk', description: 'Creamy coconut milk', price: 3.49, unit: 'can', stock: 45, brand: 'Tropical Taste', image: 'https://images.unsplash.com/photo-1481671703460-040cb8a2d909?w=400' },
      { category: 'Snacks & Sweets', name: 'Rice Crackers', description: 'Crispy rice crackers', price: 3.99, unit: 'pack', stock: 40, brand: 'Asian Snacks', image: 'https://images.unsplash.com/photo-1566478989037-eec170784d0b?w=400' },
      
      // Baby & Kids
      { category: 'Baby Care', name: 'Baby Formula', description: 'Infant formula milk', price: 18.99, unit: 'can', stock: 25, brand: 'Little Angels', image: 'https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400' },
      { category: 'Baby Care', name: 'Baby Diapers', description: 'Ultra-soft baby diapers', price: 12.99, unit: 'pack', stock: 30, brand: 'Comfort Baby', image: 'https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400' },
      { category: 'Baby Care', name: 'Baby Wipes', description: 'Gentle baby wipes', price: 4.99, unit: 'pack', stock: 50, brand: 'Soft Touch', image: 'https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400' },
      
      // Seasonal Items
      { category: 'Vegetables', name: 'Organic Spinach', description: 'Fresh organic spinach leaves', price: 3.99, unit: 'pack', stock: 30, brand: 'Green Organic', image: 'https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=400' },
      { category: 'Vegetables', name: 'Cherry Tomatoes', description: 'Sweet cherry tomatoes', price: 4.99, unit: 'pack', stock: 25, brand: 'Garden Select', image: 'https://images.unsplash.com/photo-1592924357228-91a4daadcfea?w=400' },
      { category: 'Fresh Fruits', name: 'Kiwi Fruit', description: 'Fresh kiwi fruit', price: 5.99, unit: 'kg', stock: 20, brand: 'Exotic Garden', image: 'https://images.unsplash.com/photo-1585059895524-72359e06133a?w=400' },
      
      // Premium Meat & Seafood
      { category: 'Meat & Poultry', name: 'Salmon Fillet', description: 'Fresh Atlantic salmon', price: 19.99, unit: 'kg', stock: 15, brand: 'Ocean Fresh', image: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400' },
      { category: 'Meat & Poultry', name: 'Chicken Breast', description: 'Boneless chicken breast', price: 12.99, unit: 'kg', stock: 25, brand: 'Farm Fresh', image: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=400' },
      { category: 'Meat & Poultry', name: 'Ground Beef', description: 'Lean ground beef', price: 15.99, unit: 'kg', stock: 20, brand: 'Prime Cuts', image: 'https://images.unsplash.com/photo-1588347818133-38c4106ca7b8?w=400' },
      
      // Luxury Items
      { category: 'Beverages', name: 'Sparkling Water', description: 'Premium sparkling mineral water', price: 2.49, unit: 'bottle', stock: 60, brand: 'Crystal Springs', image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400' },
      { category: 'Snacks & Sweets', name: 'Artisan Cookies', description: 'Handmade artisan cookies', price: 6.99, unit: 'pack', stock: 20, brand: 'Cookie Craft', image: 'https://images.unsplash.com/photo-1499636136210-6f4ee915583e?w=400' },
      { category: 'Beverages', name: 'Cold Brew Coffee', description: 'Premium cold brew coffee', price: 4.99, unit: 'bottle', stock: 25, brand: 'Coffee Masters', image: 'https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=400' }
    ];

    console.log(`🛒 Adding ${premiumProducts.length} premium products...`);

    for (const product of premiumProducts) {
      await query(
        `INSERT INTO mart_products (category_id, name, description, price, image_url, is_available, stock_quantity, unit, brand) 
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
        [
          categoryIds[product.category],
          product.name,
          product.description,
          product.price,
          product.image,
          true,
          product.stock,
          product.unit,
          product.brand
        ]
      );
    }

    // Get final counts
    const totalProducts = await query('SELECT COUNT(*) as count FROM mart_products');
    const totalCategories = await query('SELECT COUNT(*) as count FROM mart_categories');
    const totalLocations = await query('SELECT COUNT(*) as count FROM market_locations');

    console.log('✅ Premium products added successfully!');
    console.log(`📊 Final Statistics:`);
    console.log(`   📍 Market Locations: ${totalLocations.rows[0].count}`);
    console.log(`   🏷️ Categories: ${totalCategories.rows[0].count}`);
    console.log(`   🛒 Total Products: ${totalProducts.rows[0].count}`);
    console.log(`   🌟 Premium Products Added: ${premiumProducts.length}`);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error adding premium products:', error);
    process.exit(1);
  }
}

addPremiumProducts();
