// Helper function to parse double values from various types
double _parseDouble(dynamic value) {
  if (value == null) return 0.0;
  if (value is double) return value;
  if (value is int) return value.toDouble();
  if (value is String) {
    return double.tryParse(value) ?? 0.0;
  }
  return 0.0;
}

class OrderItem {
  final String name;
  final String description;
  final String? imageUrl;
  final int quantity;
  final double unitPrice;
  final double totalPrice;
  final String? specialInstructions;

  OrderItem({
    required this.name,
    required this.description,
    this.imageUrl,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.specialInstructions,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      name: json['name'] ?? json['menu_item_name'] ?? '',
      description: json['description'] ?? '',
      imageUrl: json['imageUrl'] ?? json['image_url'],
      quantity: json['quantity'] ?? 0,
      unitPrice: _parseDouble(json['unitPrice'] ?? json['unit_price']),
      totalPrice: _parseDouble(json['totalPrice'] ?? json['total_price']),
      specialInstructions:
          json['specialInstructions'] ?? json['special_instructions'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'totalPrice': totalPrice,
      'specialInstructions': specialInstructions,
    };
  }
}

class RestaurantInfo {
  final String name;
  final String address;
  final String? phone;
  final double? latitude;
  final double? longitude;

  RestaurantInfo({
    required this.name,
    required this.address,
    this.phone,
    this.latitude,
    this.longitude,
  });

  factory RestaurantInfo.fromJson(Map<String, dynamic> json) {
    return RestaurantInfo(
      name: json['name'],
      address: json['address'],
      phone: json['phone'],
      latitude: json['location']?['latitude']?.toDouble(),
      longitude: json['location']?['longitude']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'address': address,
      'phone': phone,
      'location': latitude != null && longitude != null
          ? {'latitude': latitude, 'longitude': longitude}
          : null,
    };
  }
}

class CustomerInfo {
  final String name;
  final String phone;

  CustomerInfo({required this.name, required this.phone});

  factory CustomerInfo.fromJson(Map<String, dynamic> json) {
    return CustomerInfo(name: json['name'], phone: json['phone']);
  }

  Map<String, dynamic> toJson() {
    return {'name': name, 'phone': phone};
  }
}

class Order {
  final int id;
  final String orderNumber;
  final String orderType; // 'restaurant' or 'mart'
  final double totalAmount;
  final double subtotalAmount;
  final double deliveryFee;
  final double discountAmount;
  final String? discountName;
  final String status;
  final String deliveryAddress;
  final double? deliveryLatitude;
  final double? deliveryLongitude;
  final String? specialInstructions;
  final DateTime? estimatedDeliveryTime;
  final DateTime createdAt;
  // Restaurant info (for restaurant orders)
  final String? restaurantName;
  final String? restaurantAddress;
  final String? restaurantPhone;
  final double? restaurantLatitude;
  final double? restaurantLongitude;
  // Market info (for mart orders)
  final String? marketName;
  final String? marketAddress;
  final String? marketPhone;
  final double? marketLatitude;
  final double? marketLongitude;
  // Customer info
  final String customerName;
  final String customerPhone;
  final List<OrderItem> items;

  Order({
    required this.id,
    required this.orderNumber,
    this.orderType = 'restaurant',
    required this.totalAmount,
    required this.subtotalAmount,
    required this.deliveryFee,
    this.discountAmount = 0.0,
    this.discountName,
    required this.status,
    required this.deliveryAddress,
    this.deliveryLatitude,
    this.deliveryLongitude,
    this.specialInstructions,
    this.estimatedDeliveryTime,
    required this.createdAt,
    // Restaurant info
    this.restaurantName,
    this.restaurantAddress,
    this.restaurantPhone,
    this.restaurantLatitude,
    this.restaurantLongitude,
    // Market info
    this.marketName,
    this.marketAddress,
    this.marketPhone,
    this.marketLatitude,
    this.marketLongitude,
    // Customer info
    required this.customerName,
    required this.customerPhone,
    this.items = const [],
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    // Handle both snake_case and camelCase formats
    final orderType = json['order_type'] ?? 'restaurant';

    return Order(
      id: json['id'] ?? 0,
      orderNumber: json['order_number'] ?? json['orderNumber'] ?? '',
      orderType: orderType,
      totalAmount: _parseDouble(json['total_amount'] ?? json['totalAmount']),
      subtotalAmount: _parseDouble(
        json['subtotal_amount'] ??
            json['subtotalAmount'] ??
            json['total_amount'] ??
            json['totalAmount'],
      ),
      deliveryFee: _parseDouble(json['delivery_fee'] ?? json['deliveryFee']),
      discountAmount: _parseDouble(
        json['discount_amount'] ?? json['discountAmount'],
      ),
      discountName: json['discount_name'] ?? json['discountName'],
      status: json['status'] ?? 'pending',
      deliveryAddress:
          json['delivery_address'] ?? json['deliveryAddress'] ?? '',
      deliveryLatitude: _parseDouble(
        json['delivery_latitude'] ?? json['deliveryLatitude'],
      ),
      deliveryLongitude: _parseDouble(
        json['delivery_longitude'] ?? json['deliveryLongitude'],
      ),
      specialInstructions:
          json['special_instructions'] ?? json['specialInstructions'],
      estimatedDeliveryTime:
          (json['estimated_delivery_time'] ?? json['estimatedDeliveryTime']) !=
              null
          ? DateTime.tryParse(
              json['estimated_delivery_time'] ?? json['estimatedDeliveryTime'],
            )
          : null,
      createdAt:
          DateTime.tryParse(json['created_at'] ?? json['createdAt'] ?? '') ??
          DateTime.now(),
      // Restaurant info (for restaurant orders)
      restaurantName: orderType == 'restaurant'
          ? (json['restaurant_name'] ?? json['restaurant']?['name'])
          : null,
      restaurantAddress: orderType == 'restaurant'
          ? (json['restaurant_address'] ?? json['restaurant']?['address'])
          : null,
      restaurantPhone: orderType == 'restaurant'
          ? (json['restaurant_phone'] ?? json['restaurant']?['phone'])
          : null,
      restaurantLatitude: orderType == 'restaurant'
          ? _parseDouble(
              json['restaurant_latitude'] ??
                  json['restaurant']?['location']?['latitude'],
            )
          : null,
      restaurantLongitude: orderType == 'restaurant'
          ? _parseDouble(
              json['restaurant_longitude'] ??
                  json['restaurant']?['location']?['longitude'],
            )
          : null,
      // Market info (for mart orders)
      marketName: orderType == 'mart'
          ? (json['market_name'] ?? json['market']?['name'])
          : null,
      marketAddress: orderType == 'mart'
          ? (json['market_address'] ?? json['market']?['address'])
          : null,
      marketPhone: orderType == 'mart'
          ? (json['market_phone'] ?? json['market']?['phone'])
          : null,
      marketLatitude: orderType == 'mart'
          ? _parseDouble(
              json['market_latitude'] ??
                  json['market']?['location']?['latitude'],
            )
          : null,
      marketLongitude: orderType == 'mart'
          ? _parseDouble(
              json['market_longitude'] ??
                  json['market']?['location']?['longitude'],
            )
          : null,
      // Customer info
      customerName: json['customer_name'] ?? json['customer']?['name'] ?? '',
      customerPhone: json['customer_phone'] ?? json['customer']?['phone'] ?? '',
      items: json['items'] != null
          ? (json['items'] as List)
                .map((item) => OrderItem.fromJson(item))
                .toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_number': orderNumber,
      'total_amount': totalAmount,
      'delivery_fee': deliveryFee,
      'status': status,
      'delivery_address': deliveryAddress,
      'delivery_latitude': deliveryLatitude,
      'delivery_longitude': deliveryLongitude,
      'special_instructions': specialInstructions,
      'estimated_delivery_time': estimatedDeliveryTime?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'restaurant_name': restaurantName,
      'restaurant_address': restaurantAddress,
      'restaurant_phone': restaurantPhone,
      'restaurant_latitude': restaurantLatitude,
      'restaurant_longitude': restaurantLongitude,
      'customer_name': customerName,
      'customer_phone': customerPhone,
      'items': items.map((item) => item.toJson()).toList(),
    };
  }

  String get statusDisplayName {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'confirmed':
        return 'Confirmed';
      case 'preparing':
        return 'Preparing';
      case 'ready':
        return 'Ready for Pickup';
      case 'assigned':
        return 'Assigned to You';
      case 'picked_up':
        return 'Picked Up';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  }

  String get formattedTotal {
    return '\$${totalAmount.toStringAsFixed(2)}';
  }

  String get formattedDeliveryFee {
    return '\$${deliveryFee.toStringAsFixed(2)}';
  }

  // Helper methods for pickup location based on order type
  String? get pickupLocationName {
    return orderType == 'mart' ? marketName : restaurantName;
  }

  String? get pickupLocationAddress {
    return orderType == 'mart' ? marketAddress : restaurantAddress;
  }

  String? get pickupLocationPhone {
    return orderType == 'mart' ? marketPhone : restaurantPhone;
  }

  double? get pickupLocationLatitude {
    return orderType == 'mart' ? marketLatitude : restaurantLatitude;
  }

  double? get pickupLocationLongitude {
    return orderType == 'mart' ? marketLongitude : restaurantLongitude;
  }

  bool get isMartOrder {
    return orderType == 'mart';
  }

  bool get isRestaurantOrder {
    return orderType == 'restaurant';
  }

  String get orderTypeDisplayName {
    return orderType == 'mart' ? 'Mart Order' : 'Restaurant Order';
  }
}
