const express = require('express');
const { query } = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { validateRequest, schemas } = require('../middleware/validation');
const imageService = require('../services/imageService');

const router = express.Router();

// Complete restaurant profile
router.post('/profile', 
  authenticateToken, 
  requireRole(['restaurant']), 
  validateRequest(schemas.restaurantProfile), 
  async (req, res) => {
    try {
      const { restaurantName, description, phone, address, latitude, longitude, cuisineType } = req.body;
      const userId = req.user.id;

      // Check if profile already exists
      const existingProfile = await query(
        'SELECT id FROM restaurant_profiles WHERE user_id = $1',
        [userId]
      );

      let result;
      if (existingProfile.rows.length > 0) {
        // Update existing profile
        result = await query(
          `UPDATE restaurant_profiles 
           SET restaurant_name = $1, description = $2, phone = $3, address = $4, 
               latitude = $5, longitude = $6, cuisine_type = $7, updated_at = CURRENT_TIMESTAMP
           WHERE user_id = $8 
           RETURNING *`,
          [restaurantName, description, phone, address, latitude, longitude, cuisineType, userId]
        );
      } else {
        // Create new profile
        result = await query(
          `INSERT INTO restaurant_profiles 
           (user_id, restaurant_name, description, phone, address, latitude, longitude, cuisine_type) 
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8) 
           RETURNING *`,
          [userId, restaurantName, description, phone, address, latitude, longitude, cuisineType]
        );
      }

      const profile = result.rows[0];

      res.json({
        message: 'Restaurant profile completed successfully',
        profile: {
          id: profile.id,
          restaurantName: profile.restaurant_name,
          description: profile.description,
          phone: profile.phone,
          address: profile.address,
          latitude: profile.latitude,
          longitude: profile.longitude,
          cuisineType: profile.cuisine_type,
          isOpen: profile.is_open,
          rating: profile.rating,
          totalOrders: profile.total_orders
        }
      });

    } catch (error) {
      console.error('Restaurant profile completion error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get restaurant profile
router.get('/profile', authenticateToken, requireRole(['restaurant']), async (req, res) => {
  try {
    const userId = req.user.id;

    const result = await query(
      'SELECT * FROM restaurant_profiles WHERE user_id = $1',
      [userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Restaurant profile not found' });
    }

    const profile = result.rows[0];

    res.json({
      profile: {
        id: profile.id,
        restaurantName: profile.restaurant_name,
        description: profile.description,
        phone: profile.phone,
        address: profile.address,
        latitude: profile.latitude,
        longitude: profile.longitude,
        cuisineType: profile.cuisine_type,
        isOpen: profile.is_open,
        rating: profile.rating,
        totalOrders: profile.total_orders,
        createdAt: profile.created_at,
        updatedAt: profile.updated_at
      }
    });

  } catch (error) {
    console.error('Get restaurant profile error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Toggle restaurant open/closed status
router.patch('/status', authenticateToken, requireRole(['restaurant']), async (req, res) => {
  try {
    const { isOpen } = req.body;
    const userId = req.user.id;

    if (typeof isOpen !== 'boolean') {
      return res.status(400).json({ error: 'isOpen must be a boolean value' });
    }

    const result = await query(
      `UPDATE restaurant_profiles 
       SET is_open = $1, updated_at = CURRENT_TIMESTAMP
       WHERE user_id = $2 
       RETURNING is_open`,
      [isOpen, userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Restaurant profile not found' });
    }

    res.json({
      message: `Restaurant ${isOpen ? 'opened' : 'closed'} successfully`,
      isOpen: result.rows[0].is_open
    });

  } catch (error) {
    console.error('Update restaurant status error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get restaurant orders
router.get('/orders', authenticateToken, requireRole(['restaurant']), async (req, res) => {
  try {
    const userId = req.user.id;
    const { status } = req.query;

    // Get restaurant profile first
    const restaurantResult = await query(
      'SELECT id FROM restaurant_profiles WHERE user_id = $1',
      [userId]
    );

    if (restaurantResult.rows.length === 0) {
      return res.status(404).json({ error: 'Restaurant profile not found' });
    }

    const restaurantId = restaurantResult.rows[0].id;

    let ordersQuery = `
      SELECT o.id, o.order_number, o.status, o.total_amount, o.delivery_fee,
             o.delivery_address, o.delivery_latitude, o.delivery_longitude,
             o.special_instructions, o.estimated_delivery_time,
             o.created_at, o.updated_at,
             cp.full_name as customer_name, cp.phone as customer_phone
      FROM orders o
      JOIN customer_profiles cp ON o.customer_id = cp.id
      WHERE o.restaurant_id = $1
    `;
    let queryParams = [restaurantId];

    if (status) {
      ordersQuery += ' AND o.status = $2';
      queryParams.push(status);
    }

    ordersQuery += ' ORDER BY o.created_at DESC';

    const result = await query(ordersQuery, queryParams);

    res.json({
      orders: result.rows.map(order => ({
        id: order.id,
        orderNumber: order.order_number,
        status: order.status,
        totalAmount: parseFloat(order.total_amount),
        deliveryFee: parseFloat(order.delivery_fee),
        deliveryAddress: order.delivery_address,
        deliveryLatitude: order.delivery_latitude ? parseFloat(order.delivery_latitude) : null,
        deliveryLongitude: order.delivery_longitude ? parseFloat(order.delivery_longitude) : null,
        specialInstructions: order.special_instructions,
        estimatedDeliveryTime: order.estimated_delivery_time,
        customer: {
          name: order.customer_name,
          phone: order.customer_phone
        },
        createdAt: order.created_at,
        updatedAt: order.updated_at
      }))
    });

  } catch (error) {
    console.error('Get restaurant orders error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get specific order details with items
router.get('/orders/:orderId', authenticateToken, requireRole(['restaurant']), async (req, res) => {
  try {
    const { orderId } = req.params;
    const userId = req.user.id;

    // Get restaurant profile first
    const restaurantResult = await query(
      'SELECT id FROM restaurant_profiles WHERE user_id = $1',
      [userId]
    );

    if (restaurantResult.rows.length === 0) {
      return res.status(404).json({ error: 'Restaurant profile not found' });
    }

    const restaurantId = restaurantResult.rows[0].id;

    // Get order details
    const orderResult = await query(
      `SELECT
         o.id, o.order_number, o.status, o.total_amount, o.delivery_fee,
         o.delivery_address, o.delivery_latitude, o.delivery_longitude,
         o.special_instructions, o.estimated_delivery_time,
         o.created_at, o.updated_at,
         cp.full_name as customer_name, cp.phone as customer_phone,
         dp.full_name as driver_name, dp.phone as driver_phone
       FROM orders o
       JOIN customer_profiles cp ON o.customer_id = cp.id
       LEFT JOIN driver_profiles dp ON o.driver_id = dp.id
       WHERE o.id = $1 AND o.restaurant_id = $2`,
      [orderId, restaurantId]
    );

    if (orderResult.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    const order = orderResult.rows[0];

    // Get order items
    const itemsResult = await query(
      `SELECT
         oi.quantity, oi.unit_price, oi.total_price, oi.special_instructions,
         mi.name, mi.description, mi.image_url
       FROM order_items oi
       JOIN menu_items mi ON oi.menu_item_id = mi.id
       WHERE oi.order_id = $1`,
      [orderId]
    );

    const orderDetails = {
      id: order.id,
      orderNumber: order.order_number,
      status: order.status,
      totalAmount: parseFloat(order.total_amount),
      deliveryFee: parseFloat(order.delivery_fee),
      deliveryAddress: order.delivery_address,
      deliveryLatitude: order.delivery_latitude ? parseFloat(order.delivery_latitude) : null,
      deliveryLongitude: order.delivery_longitude ? parseFloat(order.delivery_longitude) : null,
      deliveryLocation: {
        latitude: order.delivery_latitude ? parseFloat(order.delivery_latitude) : null,
        longitude: order.delivery_longitude ? parseFloat(order.delivery_longitude) : null
      },
      specialInstructions: order.special_instructions,
      estimatedDeliveryTime: order.estimated_delivery_time,
      createdAt: order.created_at,
      updatedAt: order.updated_at,
      customer: {
        name: order.customer_name,
        phone: order.customer_phone
      },
      driver: order.driver_name ? {
        name: order.driver_name,
        phone: order.driver_phone
      } : null,
      items: itemsResult.rows.map(item => ({
        name: item.name,
        description: item.description,
        imageUrl: item.image_url,
        quantity: item.quantity,
        unitPrice: parseFloat(item.unit_price),
        totalPrice: parseFloat(item.total_price),
        specialInstructions: item.special_instructions
      }))
    };

    res.json({ order: orderDetails });

  } catch (error) {
    console.error('Get restaurant order details error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Cancel order (restaurant decline)
router.patch('/orders/:orderId/cancel', authenticateToken, requireRole(['restaurant']), async (req, res) => {
  try {
    const { orderId } = req.params;
    const { reason } = req.body;
    const userId = req.user.id;

    // Get restaurant profile first
    const restaurantResult = await query(
      'SELECT id FROM restaurant_profiles WHERE user_id = $1',
      [userId]
    );

    if (restaurantResult.rows.length === 0) {
      return res.status(404).json({ error: 'Restaurant profile not found' });
    }

    const restaurantId = restaurantResult.rows[0].id;

    // Check if order exists and belongs to this restaurant
    const orderCheck = await query(
      'SELECT id, status FROM orders WHERE id = $1 AND restaurant_id = $2',
      [orderId, restaurantId]
    );

    if (orderCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    const currentStatus = orderCheck.rows[0].status;

    // Only allow cancellation for pending or confirmed orders
    if (!['pending', 'confirmed'].includes(currentStatus)) {
      return res.status(400).json({
        error: 'Order cannot be cancelled at this stage'
      });
    }

    // Update order status to cancelled
    const result = await query(
      `UPDATE orders
       SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
       WHERE id = $1 AND restaurant_id = $2
       RETURNING *`,
      [orderId, restaurantId]
    );

    // Record the cancellation
    await query(
      `INSERT INTO order_cancellations (order_id, cancelled_by_type, cancelled_by_id, cancellation_reason)
       VALUES ($1, 'restaurant', $2, $3)`,
      [orderId, restaurantId, reason || 'Restaurant cancelled the order']
    );

    res.json({
      message: 'Order cancelled successfully',
      order: result.rows[0]
    });

  } catch (error) {
    console.error('Cancel order error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update order status
router.patch('/orders/:orderId/status', authenticateToken, requireRole(['restaurant']), async (req, res) => {
  try {
    const { orderId } = req.params;
    const { status } = req.body;
    const userId = req.user.id;

    const validStatuses = ['pending', 'confirmed', 'preparing', 'ready', 'picked_up', 'delivered', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: 'Invalid status' });
    }

    // Get restaurant profile first
    const restaurantResult = await query(
      'SELECT id FROM restaurant_profiles WHERE user_id = $1',
      [userId]
    );

    if (restaurantResult.rows.length === 0) {
      return res.status(404).json({ error: 'Restaurant profile not found' });
    }

    const restaurantId = restaurantResult.rows[0].id;

    // Update order status
    const result = await query(
      `UPDATE orders 
       SET status = $1, updated_at = CURRENT_TIMESTAMP
       WHERE id = $2 AND restaurant_id = $3
       RETURNING *`,
      [status, orderId, restaurantId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    res.json({
      message: 'Order status updated successfully',
      order: {
        id: result.rows[0].id,
        status: result.rows[0].status,
        updatedAt: result.rows[0].updated_at
      }
    });

  } catch (error) {
    console.error('Update order status error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update restaurant image
router.patch('/:restaurantId/image',
  authenticateToken,
  requireRole(['restaurant']),
  async (req, res) => {
    try {
      const { restaurantId } = req.params;
      const { imageUrl, imageType = 'profile' } = req.body;

      if (!imageUrl) {
        return res.status(400).json({ error: 'Image URL is required' });
      }

      // Check if user owns this restaurant
      const restaurantCheck = await query(
        'SELECT id FROM restaurant_profiles WHERE id = $1 AND user_id = $2',
        [restaurantId, req.user.id]
      );

      if (restaurantCheck.rows.length === 0) {
        return res.status(403).json({ error: 'Access denied to this restaurant' });
      }

      // Update restaurant image
      const updatedRestaurant = await imageService.updateRestaurantImage(
        restaurantId,
        imageUrl,
        imageType
      );

      res.json({
        success: true,
        restaurant: updatedRestaurant,
        message: 'Restaurant image updated successfully'
      });

    } catch (error) {
      console.error('Restaurant image update error:', error);
      res.status(500).json({
        error: 'Failed to update restaurant image',
        message: error.message
      });
    }
  }
);

// Get restaurant images
router.get('/:restaurantId/images',
  authenticateToken,
  requireRole(['restaurant']),
  async (req, res) => {
    try {
      const { restaurantId } = req.params;

      // Check if user owns this restaurant
      const restaurantCheck = await query(
        'SELECT id FROM restaurant_profiles WHERE id = $1 AND user_id = $2',
        [restaurantId, req.user.id]
      );

      if (restaurantCheck.rows.length === 0) {
        return res.status(403).json({ error: 'Access denied to this restaurant' });
      }

      // Get all images for this restaurant
      const images = await imageService.getRestaurantImages(restaurantId);

      res.json({
        success: true,
        images
      });

    } catch (error) {
      console.error('Get restaurant images error:', error);
      res.status(500).json({
        error: 'Failed to get restaurant images',
        message: error.message
      });
    }
  }
);

// ============================================================================
// DISCOUNT MANAGEMENT ENDPOINTS (Restaurant)
// ============================================================================

// Get restaurant's discounts
router.get('/discounts', authenticateToken, requireRole(['restaurant']), async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 20, status } = req.query;
    const offset = (page - 1) * limit;

    // Get restaurant profile
    const restaurantProfile = await query(
      'SELECT id FROM restaurant_profiles WHERE user_id = $1',
      [userId]
    );

    if (restaurantProfile.rows.length === 0) {
      return res.status(400).json({ error: 'Restaurant profile not found' });
    }

    const restaurantId = restaurantProfile.rows[0].id;

    let whereClause = 'WHERE (d.restaurant_id = $1 OR (d.scope = \'menu_item\' AND d.restaurant_id = $1))';
    let queryParams = [restaurantId];

    if (status === 'active') {
      whereClause += ' AND d.is_active = true AND d.start_date <= NOW() AND d.end_date > NOW()';
    } else if (status === 'inactive') {
      whereClause += ' AND (d.is_active = false OR d.start_date > NOW() OR d.end_date <= NOW())';
    }

    // Get discounts
    const discountsResult = await query(`
      SELECT
        d.*,
        mi.name as menu_item_name,
        COALESCE(usage_stats.total_usage, 0) as total_usage,
        COALESCE(usage_stats.total_savings, 0) as total_savings
      FROM discounts d
      LEFT JOIN menu_items mi ON d.menu_item_id = mi.id
      LEFT JOIN (
        SELECT
          discount_id,
          COUNT(*) as total_usage,
          SUM(discount_amount) as total_savings
        FROM discount_usage
        GROUP BY discount_id
      ) usage_stats ON d.id = usage_stats.discount_id
      ${whereClause}
      ORDER BY d.created_at DESC
      LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
    `, [...queryParams, limit, offset]);

    // Get total count
    const countResult = await query(`
      SELECT COUNT(*) as total
      FROM discounts d
      ${whereClause}
    `, queryParams);

    const discounts = discountsResult.rows.map(row => ({
      id: row.id,
      code: row.code,
      name: row.name,
      description: row.description,
      discountType: row.discount_type,
      discountValue: parseFloat(row.discount_value),
      scope: row.scope,
      menuItemId: row.menu_item_id,
      menuItemName: row.menu_item_name,
      minimumOrderAmount: parseFloat(row.minimum_order_amount),
      maximumDiscountAmount: row.maximum_discount_amount ? parseFloat(row.maximum_discount_amount) : null,
      usageLimit: row.usage_limit,
      usageLimitPerCustomer: row.usage_limit_per_customer,
      startDate: row.start_date,
      endDate: row.end_date,
      isActive: row.is_active,
      totalUsage: parseInt(row.total_usage),
      totalSavings: parseFloat(row.total_savings),
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }));

    res.json({
      discounts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(countResult.rows[0].total),
        totalPages: Math.ceil(countResult.rows[0].total / limit)
      }
    });

  } catch (error) {
    console.error('Get restaurant discounts error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create restaurant discount
router.post('/discounts', authenticateToken, requireRole(['restaurant']), async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      code,
      name,
      description,
      discountType,
      discountValue,
      scope,
      menuItemId,
      minimumOrderAmount = 0,
      maximumDiscountAmount,
      usageLimit,
      usageLimitPerCustomer = 1,
      startDate,
      endDate,
      timeRestrictions = []
    } = req.body;

    // Get restaurant profile
    const restaurantProfile = await query(
      'SELECT id FROM restaurant_profiles WHERE user_id = $1',
      [userId]
    );

    if (restaurantProfile.rows.length === 0) {
      return res.status(400).json({ error: 'Restaurant profile not found' });
    }

    const restaurantId = restaurantProfile.rows[0].id;

    // Validation
    if (!name || !discountType || !discountValue || !scope || !startDate || !endDate) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Restaurants can only create restaurant or menu_item scope discounts
    if (scope !== 'restaurant' && scope !== 'menu_item') {
      return res.status(400).json({ error: 'Restaurants can only create restaurant or menu item discounts' });
    }

    // Validate menu item belongs to restaurant
    if (scope === 'menu_item') {
      if (!menuItemId) {
        return res.status(400).json({ error: 'Menu item ID required for menu item scope' });
      }

      const menuItemCheck = await query(
        'SELECT id FROM menu_items WHERE id = $1 AND restaurant_id = $2',
        [menuItemId, restaurantId]
      );

      if (menuItemCheck.rows.length === 0) {
        return res.status(400).json({ error: 'Menu item not found or does not belong to your restaurant' });
      }
    }

    // Check if code is unique (if provided)
    if (code) {
      const existingDiscount = await query('SELECT id FROM discounts WHERE code = $1', [code]);
      if (existingDiscount.rows.length > 0) {
        return res.status(400).json({ error: 'Discount code already exists' });
      }
    }

    // Create discount
    const discountResult = await query(`
      INSERT INTO discounts (
        code, name, description, discount_type, discount_value, scope,
        restaurant_id, menu_item_id, minimum_order_amount, maximum_discount_amount,
        usage_limit, usage_limit_per_customer, start_date, end_date, created_by_admin
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, false)
      RETURNING *
    `, [
      code, name, description, discountType, discountValue, scope,
      restaurantId, scope === 'menu_item' ? menuItemId : null,
      minimumOrderAmount, maximumDiscountAmount,
      usageLimit, usageLimitPerCustomer, startDate, endDate
    ]);

    const discount = discountResult.rows[0];

    // Add time restrictions if provided
    if (timeRestrictions.length > 0) {
      for (const restriction of timeRestrictions) {
        await query(`
          INSERT INTO discount_time_restrictions (discount_id, day_of_week, start_time, end_time)
          VALUES ($1, $2, $3, $4)
        `, [discount.id, restriction.dayOfWeek, restriction.startTime, restriction.endTime]);
      }
    }

    res.status(201).json({
      message: 'Discount created successfully',
      discount: {
        id: discount.id,
        code: discount.code,
        name: discount.name,
        discountType: discount.discount_type,
        discountValue: parseFloat(discount.discount_value),
        scope: discount.scope,
        createdAt: discount.created_at
      }
    });

  } catch (error) {
    console.error('Create restaurant discount error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get restaurant discount analytics
router.get('/discounts/analytics', authenticateToken, requireRole(['restaurant']), async (req, res) => {
  try {
    const userId = req.user.id;
    const { period = '30d' } = req.query;

    // Get restaurant profile
    const restaurantProfile = await query(
      'SELECT id FROM restaurant_profiles WHERE user_id = $1',
      [userId]
    );

    if (restaurantProfile.rows.length === 0) {
      return res.status(400).json({ error: 'Restaurant profile not found' });
    }

    const restaurantId = restaurantProfile.rows[0].id;

    // Calculate date range based on period
    let startDate;
    const endDate = new Date();

    switch (period) {
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    // Overall analytics for restaurant discounts
    const overallResult = await query(`
      SELECT
        COUNT(du.id) as total_usage,
        SUM(du.discount_amount) as total_savings,
        AVG(du.discount_amount) as avg_discount_amount,
        COUNT(DISTINCT du.customer_id) as unique_customers,
        COUNT(DISTINCT du.discount_id) as active_discounts,
        AVG(du.original_amount) as avg_order_value
      FROM discount_usage du
      JOIN discounts d ON du.discount_id = d.id
      JOIN orders o ON du.order_id = o.id
      WHERE o.restaurant_id = $1
      AND du.created_at >= $2
      AND du.created_at <= $3
    `, [restaurantId, startDate, endDate]);

    // Performance by discount
    const discountPerformanceResult = await query(`
      SELECT
        d.id,
        d.name,
        d.discount_type,
        d.discount_value,
        COUNT(du.id) as usage_count,
        SUM(du.discount_amount) as total_savings,
        AVG(du.discount_amount) as avg_discount_amount,
        COUNT(DISTINCT du.customer_id) as unique_customers,
        (COUNT(du.id)::float / NULLIF(d.usage_limit, 0)) * 100 as usage_rate
      FROM discount_usage du
      JOIN discounts d ON du.discount_id = d.id
      JOIN orders o ON du.order_id = o.id
      WHERE o.restaurant_id = $1
      AND du.created_at >= $2
      AND du.created_at <= $3
      GROUP BY d.id, d.name, d.discount_type, d.discount_value, d.usage_limit
      ORDER BY usage_count DESC
    `, [restaurantId, startDate, endDate]);

    // Daily trends
    const trendsResult = await query(`
      SELECT
        DATE(du.created_at) as date,
        COUNT(du.id) as usage_count,
        SUM(du.discount_amount) as total_savings,
        COUNT(DISTINCT du.customer_id) as unique_customers,
        AVG(du.original_amount) as avg_order_value
      FROM discount_usage du
      JOIN discounts d ON du.discount_id = d.id
      JOIN orders o ON du.order_id = o.id
      WHERE o.restaurant_id = $1
      AND du.created_at >= $2
      AND du.created_at <= $3
      GROUP BY DATE(du.created_at)
      ORDER BY date DESC
      LIMIT 30
    `, [restaurantId, startDate, endDate]);

    // Conversion rate analysis
    const conversionResult = await query(`
      SELECT
        COUNT(DISTINCT o.id) as total_orders,
        COUNT(DISTINCT CASE WHEN du.id IS NOT NULL THEN o.id END) as orders_with_discount,
        (COUNT(DISTINCT CASE WHEN du.id IS NOT NULL THEN o.id END)::float /
         NULLIF(COUNT(DISTINCT o.id), 0)) * 100 as conversion_rate
      FROM orders o
      LEFT JOIN discount_usage du ON o.id = du.order_id
      WHERE o.restaurant_id = $1
      AND o.created_at >= $2
      AND o.created_at <= $3
    `, [restaurantId, startDate, endDate]);

    res.json({
      success: true,
      data: {
        period: period,
        dateRange: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString()
        },
        overview: overallResult.rows[0] || {},
        discountPerformance: discountPerformanceResult.rows,
        dailyTrends: trendsResult.rows,
        conversionRate: conversionResult.rows[0] || {}
      }
    });

  } catch (error) {
    console.error('Get restaurant discount analytics error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
