const express = require('express');
const { query } = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { validateRequest, schemas } = require('../middleware/validation');
const DiscountValidator = require('../utils/discountValidator');

const router = express.Router();

// ============================================================================
// PUBLIC ENDPOINTS (No Authentication Required)
// ============================================================================

// Get nearby restaurants (public access for guest browsing)
router.get('/public/restaurants', async (req, res) => {
  try {
    // Get default radius from admin settings
    const defaultRadiusResult = await query(
      "SELECT setting_value FROM admin_settings WHERE setting_key = 'restaurant_search_radius'"
    );
    const defaultRadius = defaultRadiusResult.rows.length > 0
      ? parseFloat(defaultRadiusResult.rows[0].setting_value)
      : 10;

    const { latitude, longitude, radius = defaultRadius } = req.query; // radius in km

    let sqlQuery;
    let queryParams;

    if (latitude && longitude) {
      // With location - calculate distance and sort by proximity
      sqlQuery = `
        SELECT
          rp.id, rp.restaurant_name, rp.description, rp.cuisine_type,
          rp.address, rp.latitude, rp.longitude, rp.rating, rp.total_orders,
          rp.is_open, rp.created_at,
          u.email,
          (
            6371 * acos(
              cos(radians($1)) * cos(radians(rp.latitude)) *
              cos(radians(rp.longitude) - radians($2)) +
              sin(radians($1)) * sin(radians(rp.latitude))
            )
          ) AS distance
        FROM restaurant_profiles rp
        JOIN users u ON rp.user_id = u.id
        WHERE u.user_type = 'restaurant' AND u.is_active = true AND rp.is_open = true
        AND (
          6371 * acos(
            cos(radians($1)) * cos(radians(rp.latitude)) *
            cos(radians(rp.longitude) - radians($2)) +
            sin(radians($1)) * sin(radians(rp.latitude))
          )
        ) <= $3
        ORDER BY distance ASC, rp.rating DESC, rp.total_orders DESC
      `;
      queryParams = [parseFloat(latitude), parseFloat(longitude), parseFloat(radius)];
    } else {
      // Without location - just get all restaurants
      sqlQuery = `
        SELECT
          rp.id, rp.restaurant_name, rp.description, rp.cuisine_type,
          rp.address, rp.latitude, rp.longitude, rp.rating, rp.total_orders,
          rp.is_open, rp.created_at,
          u.email,
          NULL as distance
        FROM restaurant_profiles rp
        JOIN users u ON rp.user_id = u.id
        WHERE u.user_type = 'restaurant' AND u.is_active = true AND rp.is_open = true
        ORDER BY rp.rating DESC, rp.total_orders DESC
      `;
      queryParams = [];
    }

    const result = await query(sqlQuery, queryParams);

    const restaurants = result.rows.map(row => ({
      id: row.id,
      name: row.restaurant_name,
      description: row.description,
      cuisineType: row.cuisine_type,
      address: row.address,
      location: {
        latitude: parseFloat(row.latitude),
        longitude: parseFloat(row.longitude)
      },
      rating: parseFloat(row.rating),
      totalOrders: row.total_orders,
      isOpen: row.is_open,
      distance: row.distance ? parseFloat(row.distance).toFixed(2) : null,
      createdAt: row.created_at
    }));

    res.json({ restaurants });

  } catch (error) {
    console.error('Get public restaurants error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get restaurant details with menu (public access for guest browsing)
router.get('/public/restaurants/:restaurantId', async (req, res) => {
  try {
    const { restaurantId } = req.params;

    // Get restaurant details
    const restaurantResult = await query(`
      SELECT
        rp.id, rp.restaurant_name, rp.description, rp.cuisine_type,
        rp.address, rp.latitude, rp.longitude, rp.rating, rp.total_orders,
        rp.is_open, rp.created_at,
        u.email
      FROM restaurant_profiles rp
      JOIN users u ON rp.user_id = u.id
      WHERE rp.id = $1 AND u.user_type = 'restaurant' AND u.is_active = true
    `, [restaurantId]);

    if (restaurantResult.rows.length === 0) {
      return res.status(404).json({ error: 'Restaurant not found' });
    }

    const restaurant = restaurantResult.rows[0];

    // Get categories and menu items
    const categoriesResult = await query(
      'SELECT * FROM categories WHERE restaurant_id = $1 AND is_active = true ORDER BY name',
      [restaurantId]
    );

    const menuItemsResult = await query(
      'SELECT * FROM menu_items WHERE restaurant_id = $1 AND is_available = true ORDER BY category_id, name',
      [restaurantId]
    );

    const categories = categoriesResult.rows;
    const menuItems = menuItemsResult.rows;

    // Group menu items by category
    const categoriesWithItems = categories.map(category => ({
      id: category.id,
      name: category.name,
      description: category.description,
      items: menuItems.filter(item => item.category_id === category.id).map(item => ({
        id: item.id,
        name: item.name,
        description: item.description,
        price: parseFloat(item.price),
        imageUrl: item.image_url,
        preparationTime: item.preparation_time,
        isAvailable: item.is_available,
        createdAt: item.created_at
      }))
    }));

    res.json({
      restaurant: {
        id: restaurant.id,
        name: restaurant.restaurant_name,
        description: restaurant.description,
        cuisineType: restaurant.cuisine_type,
        address: restaurant.address,
        location: {
          latitude: parseFloat(restaurant.latitude),
          longitude: parseFloat(restaurant.longitude)
        },
        rating: parseFloat(restaurant.rating),
        totalOrders: restaurant.total_orders,
        isOpen: restaurant.is_open,
        createdAt: restaurant.created_at
      },
      menu: categoriesWithItems
    });

  } catch (error) {
    console.error('Get public restaurant details error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Search restaurants and menu items (public access for guest browsing)
router.get('/public/search', async (req, res) => {
  try {
    const { q, latitude, longitude, cuisine } = req.query;

    if (!q || q.trim().length < 2) {
      return res.status(400).json({ error: 'Search query must be at least 2 characters' });
    }

    const searchTerm = `%${q.trim().toLowerCase()}%`;
    let queryParams = [searchTerm, searchTerm, searchTerm];
    let whereClause = `WHERE u.user_type = 'restaurant' AND u.is_active = true AND rp.is_open = true
      AND (LOWER(rp.restaurant_name) LIKE $1 OR LOWER(rp.description) LIKE $2 OR LOWER(rp.cuisine_type) LIKE $3)`;

    if (cuisine) {
      whereClause += ` AND LOWER(rp.cuisine_type) = $${queryParams.length + 1}`;
      queryParams.push(cuisine.toLowerCase());
    }

    let sqlQuery;
    if (latitude && longitude) {
      // With location - calculate distance
      sqlQuery = `
        SELECT
          rp.id, rp.restaurant_name, rp.description, rp.cuisine_type,
          rp.address, rp.latitude, rp.longitude, rp.rating, rp.total_orders,
          rp.is_open, rp.created_at,
          u.email,
          (
            6371 * acos(
              cos(radians($${queryParams.length + 1})) * cos(radians(rp.latitude)) *
              cos(radians(rp.longitude) - radians($${queryParams.length + 2})) +
              sin(radians($${queryParams.length + 1})) * sin(radians(rp.latitude))
            )
          ) AS distance
        FROM restaurant_profiles rp
        JOIN users u ON rp.user_id = u.id
        ${whereClause}
        ORDER BY distance ASC, rp.rating DESC
        LIMIT 20
      `;
      queryParams.push(parseFloat(latitude), parseFloat(longitude));
    } else {
      // Without location
      sqlQuery = `
        SELECT
          rp.id, rp.restaurant_name, rp.description, rp.cuisine_type,
          rp.address, rp.latitude, rp.longitude, rp.rating, rp.total_orders,
          rp.is_open, rp.created_at,
          u.email,
          NULL as distance
        FROM restaurant_profiles rp
        JOIN users u ON rp.user_id = u.id
        ${whereClause}
        ORDER BY rp.rating DESC, rp.total_orders DESC
        LIMIT 20
      `;
    }

    const result = await query(sqlQuery, queryParams);

    const restaurants = result.rows.map(row => ({
      id: row.id,
      name: row.restaurant_name,
      description: row.description,
      cuisineType: row.cuisine_type,
      address: row.address,
      location: {
        latitude: parseFloat(row.latitude),
        longitude: parseFloat(row.longitude)
      },
      rating: parseFloat(row.rating),
      totalOrders: row.total_orders,
      isOpen: row.is_open,
      distance: row.distance ? parseFloat(row.distance).toFixed(2) : null,
      createdAt: row.created_at
    }));

    res.json({ restaurants });

  } catch (error) {
    console.error('Public search error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// ============================================================================
// AUTHENTICATED ENDPOINTS (Require Authentication)
// ============================================================================

// Complete customer profile
router.post('/profile', 
  authenticateToken, 
  requireRole(['customer']), 
  validateRequest(schemas.customerProfile), 
  async (req, res) => {
    try {
      const { fullName, phone, address, latitude, longitude } = req.body;
      const userId = req.user.id;

      // Check if profile already exists
      const existingProfile = await query(
        'SELECT id FROM customer_profiles WHERE user_id = $1',
        [userId]
      );

      let result;
      if (existingProfile.rows.length > 0) {
        // Update existing profile
        result = await query(
          `UPDATE customer_profiles 
           SET full_name = $1, phone = $2, address = $3, latitude = $4, longitude = $5, 
               profile_completed = true, updated_at = CURRENT_TIMESTAMP
           WHERE user_id = $6 
           RETURNING *`,
          [fullName, phone, address, latitude, longitude, userId]
        );
      } else {
        // Create new profile
        result = await query(
          `INSERT INTO customer_profiles 
           (user_id, full_name, phone, address, latitude, longitude, profile_completed) 
           VALUES ($1, $2, $3, $4, $5, $6, true) 
           RETURNING *`,
          [userId, fullName, phone, address, latitude, longitude]
        );
      }

      const profile = result.rows[0];

      res.json({
        message: 'Profile completed successfully',
        profile: {
          id: profile.id,
          fullName: profile.full_name,
          phone: profile.phone,
          address: profile.address,
          latitude: profile.latitude,
          longitude: profile.longitude,
          profileCompleted: profile.profile_completed
        }
      });

    } catch (error) {
      console.error('Profile completion error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get customer profile
router.get('/profile', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const userId = req.user.id;

    const result = await query(
      'SELECT * FROM customer_profiles WHERE user_id = $1',
      [userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Profile not found' });
    }

    const profile = result.rows[0];

    res.json({
      profile: {
        id: profile.id,
        fullName: profile.full_name,
        phone: profile.phone,
        address: profile.address,
        latitude: profile.latitude,
        longitude: profile.longitude,
        profileCompleted: profile.profile_completed,
        createdAt: profile.created_at,
        updatedAt: profile.updated_at
      }
    });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update customer location
router.patch('/location', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const { latitude, longitude, address } = req.body;
    const userId = req.user.id;

    if (!latitude || !longitude) {
      return res.status(400).json({ error: 'Latitude and longitude are required' });
    }

    const result = await query(
      `UPDATE customer_profiles 
       SET latitude = $1, longitude = $2, address = COALESCE($3, address), updated_at = CURRENT_TIMESTAMP
       WHERE user_id = $4 
       RETURNING latitude, longitude, address`,
      [latitude, longitude, address, userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Profile not found' });
    }

    res.json({
      message: 'Location updated successfully',
      location: result.rows[0]
    });

  } catch (error) {
    console.error('Update location error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get nearby restaurants
router.get('/restaurants', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    // Get default radius from admin settings
    const defaultRadiusResult = await query(
      "SELECT setting_value FROM admin_settings WHERE setting_key = 'restaurant_search_radius'"
    );
    const defaultRadius = defaultRadiusResult.rows.length > 0
      ? parseFloat(defaultRadiusResult.rows[0].setting_value)
      : 10;

    const { latitude, longitude, radius = defaultRadius } = req.query; // radius in km
    
    let sqlQuery;
    let queryParams;
    
    if (latitude && longitude) {
      // With location - calculate distance and filter by radius
      sqlQuery = `
        SELECT 
          rp.id, rp.restaurant_name, rp.description, rp.cuisine_type,
          rp.address, rp.latitude, rp.longitude, rp.rating, rp.total_orders,
          rp.is_open, rp.created_at,
          u.email,
          CAST(
            6371 * acos(
              LEAST(1.0, GREATEST(-1.0,
                cos(radians($1)) * cos(radians(rp.latitude)) *
                cos(radians(rp.longitude) - radians($2)) +
                sin(radians($1)) * sin(radians(rp.latitude))
              ))
            ) AS NUMERIC(10,2)
          ) as distance
        FROM restaurant_profiles rp
        JOIN users u ON rp.user_id = u.id
        WHERE u.user_type = 'restaurant' AND u.is_active = true AND rp.is_open = true
        AND rp.latitude IS NOT NULL AND rp.longitude IS NOT NULL
        AND (
          6371 * acos(
            LEAST(1.0, GREATEST(-1.0,
              cos(radians($1)) * cos(radians(rp.latitude)) *
              cos(radians(rp.longitude) - radians($2)) +
              sin(radians($1)) * sin(radians(rp.latitude))
            ))
          )
        ) <= $3
        ORDER BY distance ASC, rp.rating DESC, rp.total_orders DESC
      `;
      queryParams = [parseFloat(latitude), parseFloat(longitude), parseFloat(radius)];
    } else {
      // Without location - just get all restaurants
      sqlQuery = `
        SELECT 
          rp.id, rp.restaurant_name, rp.description, rp.cuisine_type,
          rp.address, rp.latitude, rp.longitude, rp.rating, rp.total_orders,
          rp.is_open, rp.created_at,
          u.email,
          NULL as distance
        FROM restaurant_profiles rp
        JOIN users u ON rp.user_id = u.id
        WHERE u.user_type = 'restaurant' AND u.is_active = true AND rp.is_open = true
        ORDER BY rp.rating DESC, rp.total_orders DESC
      `;
      queryParams = [];
    }

    const result = await query(sqlQuery, queryParams);

    const restaurants = result.rows.map(row => ({
      id: row.id,
      name: row.restaurant_name,
      description: row.description,
      cuisineType: row.cuisine_type,
      address: row.address,
      location: {
        latitude: parseFloat(row.latitude),
        longitude: parseFloat(row.longitude)
      },
      rating: parseFloat(row.rating),
      totalOrders: row.total_orders,
      isOpen: row.is_open,
      distance: row.distance ? parseFloat(row.distance).toFixed(2) : null,
      createdAt: row.created_at
    }));

    res.json({ restaurants });

  } catch (error) {
    console.error('Get restaurants error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get restaurant details with menu
router.get('/restaurants/:restaurantId', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const { restaurantId } = req.params;

    // Get restaurant details
    const restaurantResult = await query(`
      SELECT 
        rp.id, rp.restaurant_name, rp.description, rp.cuisine_type,
        rp.address, rp.latitude, rp.longitude, rp.rating, rp.total_orders,
        rp.is_open, rp.created_at,
        u.email
      FROM restaurant_profiles rp
      JOIN users u ON rp.user_id = u.id
      WHERE rp.id = $1 AND u.user_type = 'restaurant' AND u.is_active = true
    `, [restaurantId]);

    if (restaurantResult.rows.length === 0) {
      return res.status(404).json({ error: 'Restaurant not found' });
    }

    const restaurant = restaurantResult.rows[0];

    // Get categories and menu items
    const categoriesResult = await query(
      'SELECT * FROM categories WHERE restaurant_id = $1 AND is_active = true ORDER BY name',
      [restaurantId]
    );

    const menuItemsResult = await query(
      `SELECT mi.*, mi.average_rating, mi.total_ratings
       FROM menu_items mi
       WHERE mi.restaurant_id = $1 AND mi.is_available = true
       ORDER BY mi.category_id, mi.name`,
      [restaurantId]
    );

    const categories = categoriesResult.rows;
    const menuItems = menuItemsResult.rows;

    // Group menu items by category
    const categoriesWithItems = categories.map(category => ({
      id: category.id,
      name: category.name,
      description: category.description,
      items: menuItems.filter(item => item.category_id === category.id).map(item => ({
        id: item.id,
        name: item.name,
        description: item.description,
        price: parseFloat(item.price),
        imageUrl: item.image_url,
        preparationTime: item.preparation_time,
        isAvailable: item.is_available,
        averageRating: parseFloat(item.average_rating) || 0,
        totalRatings: item.total_ratings || 0,
        createdAt: item.created_at
      }))
    }));

    // Add uncategorized items
    const uncategorizedItems = menuItems.filter(item => !item.category_id);
    if (uncategorizedItems.length > 0) {
      categoriesWithItems.push({
        id: null,
        name: 'Popular Items',
        description: 'Featured menu items',
        items: uncategorizedItems.map(item => ({
          id: item.id,
          name: item.name,
          description: item.description,
          price: parseFloat(item.price),
          imageUrl: item.image_url,
          preparationTime: item.preparation_time,
          isAvailable: item.is_available,
          averageRating: parseFloat(item.average_rating) || 0,
          totalRatings: item.total_ratings || 0,
          createdAt: item.created_at
        }))
      });
    }

    res.json({
      restaurant: {
        id: restaurant.id,
        name: restaurant.restaurant_name,
        description: restaurant.description,
        cuisineType: restaurant.cuisine_type,
        address: restaurant.address,
        location: {
          latitude: parseFloat(restaurant.latitude),
          longitude: parseFloat(restaurant.longitude)
        },
        rating: parseFloat(restaurant.rating),
        totalOrders: restaurant.total_orders,
        isOpen: restaurant.is_open,
        createdAt: restaurant.created_at
      },
      menu: categoriesWithItems
    });

  } catch (error) {
    console.error('Get restaurant details error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Search restaurants and menu items
router.get('/search', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const { q, latitude, longitude, cuisine } = req.query;
    
    if (!q || q.trim().length < 2) {
      return res.status(400).json({ error: 'Search query must be at least 2 characters' });
    }

    const searchTerm = `%${q.trim().toLowerCase()}%`;
    let queryParams = [searchTerm, searchTerm, searchTerm];
    let whereClause = `WHERE u.user_type = 'restaurant' AND u.is_active = true AND rp.is_open = true
      AND (LOWER(rp.restaurant_name) LIKE $1 OR LOWER(rp.description) LIKE $2 OR LOWER(rp.cuisine_type) LIKE $3)`;
    
    if (cuisine) {
      whereClause += ` AND LOWER(rp.cuisine_type) = $${queryParams.length + 1}`;
      queryParams.push(cuisine.toLowerCase());
    }

    const result = await query(`
      SELECT DISTINCT
        rp.id, rp.restaurant_name, rp.description, rp.cuisine_type,
        rp.address, rp.latitude, rp.longitude, rp.rating, rp.total_orders,
        rp.is_open, rp.created_at
      FROM restaurant_profiles rp
      JOIN users u ON rp.user_id = u.id
      LEFT JOIN menu_items mi ON rp.id = mi.restaurant_id AND mi.is_available = true
      ${whereClause}
      OR (mi.id IS NOT NULL AND (LOWER(mi.name) LIKE $1 OR LOWER(mi.description) LIKE $2))
      ORDER BY rp.rating DESC, rp.total_orders DESC
      LIMIT 20
    `, queryParams);

    const restaurants = result.rows.map(row => ({
      id: row.id,
      name: row.restaurant_name,
      description: row.description,
      cuisineType: row.cuisine_type,
      address: row.address,
      location: {
        latitude: parseFloat(row.latitude),
        longitude: parseFloat(row.longitude)
      },
      rating: parseFloat(row.rating),
      totalOrders: row.total_orders,
      isOpen: row.is_open,
      createdAt: row.created_at
    }));

    res.json({ restaurants, query: q });

  } catch (error) {
    console.error('Search error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create order from cart
router.post('/orders', 
  authenticateToken, 
  requireRole(['customer']), 
  async (req, res) => {
    try {
      const {
        restaurantId,
        items,
        deliveryAddress,
        deliveryLatitude,
        deliveryLongitude,
        specialInstructions,
        deliveryFee = 0.00,
        discountId,
        discountAmount = 0.00
      } = req.body;
      
      const userId = req.user.id;

      // Get customer profile
      const customerProfile = await query(
        'SELECT id FROM customer_profiles WHERE user_id = $1',
        [userId]
      );

      if (customerProfile.rows.length === 0) {
        return res.status(400).json({ error: 'Customer profile not found' });
      }

      const customerId = customerProfile.rows[0].id;

      // Validate restaurant exists
      const restaurant = await query(
        'SELECT id FROM restaurant_profiles WHERE id = $1',
        [restaurantId]
      );

      if (restaurant.rows.length === 0) {
        return res.status(400).json({ error: 'Restaurant not found' });
      }

      // Calculate total amount
      let totalAmount = 0;
      const validatedItems = [];

      for (const item of items) {
        const menuItem = await query(
          'SELECT id, name, price, is_available FROM menu_items WHERE id = $1 AND restaurant_id = $2',
          [item.menuItemId, restaurantId]
        );

        if (menuItem.rows.length === 0) {
          return res.status(400).json({ error: `Menu item ${item.menuItemId} not found` });
        }

        const menuItemData = menuItem.rows[0];
        if (!menuItemData.is_available) {
          return res.status(400).json({ error: `Menu item ${menuItemData.name} is not available` });
        }

        const itemTotal = parseFloat(menuItemData.price) * item.quantity;
        totalAmount += itemTotal;

        validatedItems.push({
          menuItemId: item.menuItemId,
          quantity: item.quantity,
          unitPrice: parseFloat(menuItemData.price),
          totalPrice: itemTotal,
          specialInstructions: item.specialInstructions || null
        });
      }

      const subtotalAmount = totalAmount;
      let finalDiscountAmount = 0;

      // Validate and apply discount if provided
      if (discountId && discountAmount > 0) {
        const validationResult = await DiscountValidator.validateDiscount(
          discountId,
          customerId,
          restaurantId,
          validatedItems,
          subtotalAmount
        );

        if (!validationResult.isValid) {
          return res.status(400).json({
            error: validationResult.error,
            errorCode: validationResult.errorCode
          });
        }

        finalDiscountAmount = validationResult.discountAmount;
      }

      totalAmount = subtotalAmount - finalDiscountAmount + parseFloat(deliveryFee);

      // Generate order number
      const orderNumber = 'ORD' + Date.now() + Math.floor(Math.random() * 1000);

      // Create order
      const orderResult = await query(
        `INSERT INTO orders
         (customer_id, restaurant_id, order_number, status, total_amount, subtotal_amount,
          delivery_fee, discount_id, discount_amount, delivery_address, delivery_latitude,
          delivery_longitude, special_instructions, estimated_delivery_time)
         VALUES ($1, $2, $3, 'pending', $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
         RETURNING *`,
        [
          customerId,
          restaurantId,
          orderNumber,
          totalAmount,
          subtotalAmount,
          deliveryFee,
          discountId || null,
          finalDiscountAmount,
          deliveryAddress,
          deliveryLatitude,
          deliveryLongitude,
          specialInstructions,
          new Date(Date.now() + 45 * 60 * 1000) // 45 minutes from now
        ]
      );

      const order = orderResult.rows[0];

      // Create order items
      for (const item of validatedItems) {
        await query(
          `INSERT INTO order_items 
           (order_id, menu_item_id, quantity, unit_price, total_price, special_instructions) 
           VALUES ($1, $2, $3, $4, $5, $6)`,
          [
            order.id, 
            item.menuItemId, 
            item.quantity, 
            item.unitPrice, 
            item.totalPrice, 
            item.specialInstructions
          ]
        );
      }

      // Record discount usage if discount was applied
      if (discountId && finalDiscountAmount > 0) {
        await query(
          `INSERT INTO discount_usage
           (discount_id, customer_id, order_id, discount_amount, original_amount, final_amount)
           VALUES ($1, $2, $3, $4, $5, $6)`,
          [
            discountId,
            customerId,
            order.id,
            finalDiscountAmount,
            subtotalAmount,
            subtotalAmount - finalDiscountAmount
          ]
        );
      }

      res.status(201).json({
        message: 'Order created successfully',
        order: {
          id: order.id,
          orderNumber: order.order_number,
          status: order.status,
          totalAmount: parseFloat(order.total_amount),
          subtotalAmount: parseFloat(order.subtotal_amount || order.total_amount),
          deliveryFee: parseFloat(order.delivery_fee),
          discountAmount: parseFloat(order.discount_amount || 0),
          estimatedDeliveryTime: order.estimated_delivery_time,
          createdAt: order.created_at
        }
      });

    } catch (error) {
      console.error('Create order error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get customer orders
router.get('/orders', 
  authenticateToken, 
  requireRole(['customer']), 
  async (req, res) => {
    try {
      const userId = req.user.id;
      const { status, limit = 20, offset = 0 } = req.query;

      // Get customer profile
      const customerProfile = await query(
        'SELECT id FROM customer_profiles WHERE user_id = $1',
        [userId]
      );

      if (customerProfile.rows.length === 0) {
        return res.status(400).json({ error: 'Customer profile not found' });
      }

      const customerId = customerProfile.rows[0].id;

      let whereClause = 'WHERE o.customer_id = $1';
      let queryParams = [customerId];
      let paramCount = 1;

      if (status) {
        paramCount++;
        whereClause += ` AND o.status = $${paramCount}`;
        queryParams.push(status);
      }

      const ordersResult = await query(
        `SELECT
           o.id, o.order_number, o.order_type, o.status, o.total_amount, o.delivery_fee,
           o.delivery_address, o.special_instructions, o.estimated_delivery_time,
           o.created_at, o.updated_at,
           r.restaurant_name, r.cuisine_type, r.phone as restaurant_phone,
           ml.name as market_name, ml.address as market_address, ml.phone as market_phone,
           d.full_name as driver_name, d.phone as driver_phone,
           CASE WHEN o.status = 'delivered' AND o.order_type = 'restaurant' THEN
             CASE WHEN EXISTS (
               SELECT 1 FROM food_ratings fr
               JOIN order_items oi ON fr.menu_item_id = oi.menu_item_id AND fr.order_id = oi.order_id
               WHERE oi.order_id = o.id AND fr.customer_id = o.customer_id
             ) THEN true ELSE false END
           ELSE false END as has_food_ratings,
           CASE WHEN o.status = 'delivered' AND o.driver_id IS NOT NULL THEN
             CASE WHEN EXISTS (
               SELECT 1 FROM driver_ratings dr
               WHERE dr.order_id = o.id AND dr.customer_id = o.customer_id AND dr.driver_id = o.driver_id
             ) THEN true ELSE false END
           ELSE false END as has_driver_rating
         FROM orders o
         LEFT JOIN restaurant_profiles r ON o.restaurant_id = r.id
         LEFT JOIN market_locations ml ON o.market_location_id = ml.id
         LEFT JOIN driver_profiles d ON o.driver_id = d.id
         ${whereClause}
         ORDER BY o.created_at DESC
         LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}`,
        [...queryParams, limit, offset]
      );

      const orders = [];
      for (const order of ordersResult.rows) {
        // Get order items based on order type
        let itemsResult;
        if (order.order_type === 'restaurant') {
          itemsResult = await query(
            `SELECT
               oi.quantity, oi.unit_price, oi.total_price, oi.special_instructions,
               mi.name, mi.description, mi.image_url
             FROM order_items oi
             JOIN menu_items mi ON oi.menu_item_id = mi.id
             WHERE oi.order_id = $1`,
            [order.id]
          );
        } else if (order.order_type === 'mart') {
          itemsResult = await query(
            `SELECT
               oi.quantity, oi.unit_price, oi.total_price, oi.special_instructions,
               mp.name, mp.description, mp.image_url, mp.unit, mp.brand
             FROM order_items oi
             JOIN mart_products mp ON oi.mart_product_id = mp.id
             WHERE oi.order_id = $1`,
            [order.id]
          );
        }

        orders.push({
          id: order.id,
          orderNumber: order.order_number,
          orderType: order.order_type,
          status: order.status,
          totalAmount: parseFloat(order.total_amount),
          deliveryFee: parseFloat(order.delivery_fee),
          deliveryAddress: order.delivery_address,
          specialInstructions: order.special_instructions,
          estimatedDeliveryTime: order.estimated_delivery_time,
          createdAt: order.created_at,
          updatedAt: order.updated_at,
          restaurant: order.order_type === 'restaurant' && order.restaurant_name ? {
            name: order.restaurant_name,
            cuisineType: order.cuisine_type,
            phone: order.restaurant_phone
          } : null,
          market: order.order_type === 'mart' && order.market_name ? {
            id: order.market_location_id,
            name: order.market_name,
            address: order.market_address,
            phone: order.market_phone
          } : null,
          driver: order.driver_name ? {
            name: order.driver_name,
            phone: order.driver_phone
          } : null,
          items: itemsResult ? itemsResult.rows.map(item => ({
            name: item.name,
            description: item.description,
            imageUrl: item.image_url,
            quantity: item.quantity,
            unitPrice: parseFloat(item.unit_price),
            totalPrice: parseFloat(item.total_price),
            specialInstructions: item.special_instructions || null,
            unit: item.unit || null,
            brand: item.brand || null
          })) : [],
          ratings: {
            hasFoodRatings: order.has_food_ratings || false,
            hasDriverRating: order.has_driver_rating || false,
            canRate: order.status === 'delivered'
          }
        });
      }

      res.json({ orders });

    } catch (error) {
      console.error('Get orders error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get specific order details
router.get('/orders/:orderId', 
  authenticateToken, 
  requireRole(['customer']), 
  async (req, res) => {
    try {
      const { orderId } = req.params;
      const userId = req.user.id;

      // Get customer profile
      const customerProfile = await query(
        'SELECT id FROM customer_profiles WHERE user_id = $1',
        [userId]
      );

      if (customerProfile.rows.length === 0) {
        return res.status(400).json({ error: 'Customer profile not found' });
      }

      const customerId = customerProfile.rows[0].id;

      // Get order details
      const orderResult = await query(
        `SELECT
           o.id, o.order_number, o.order_type, o.status, o.total_amount, o.delivery_fee,
           o.delivery_address, o.delivery_latitude, o.delivery_longitude,
           o.special_instructions, o.estimated_delivery_time,
           o.created_at, o.updated_at, o.market_location_id,
           r.restaurant_name, r.cuisine_type, r.phone as restaurant_phone,
           r.address as restaurant_address, r.latitude as restaurant_latitude,
           r.longitude as restaurant_longitude,
           ml.name as market_name, ml.address as market_address, ml.phone as market_phone,
           ml.latitude as market_latitude, ml.longitude as market_longitude,
           d.full_name as driver_name, d.phone as driver_phone,
           d.current_latitude as driver_latitude, d.current_longitude as driver_longitude
         FROM orders o
         LEFT JOIN restaurant_profiles r ON o.restaurant_id = r.id
         LEFT JOIN market_locations ml ON o.market_location_id = ml.id
         LEFT JOIN driver_profiles d ON o.driver_id = d.id
         WHERE o.id = $1 AND o.customer_id = $2`,
        [orderId, customerId]
      );

      if (orderResult.rows.length === 0) {
        return res.status(404).json({ error: 'Order not found' });
      }

      const order = orderResult.rows[0];

      // Get order items based on order type
      let itemsResult;
      if (order.order_type === 'restaurant') {
        itemsResult = await query(
          `SELECT
             oi.quantity, oi.unit_price, oi.total_price, oi.special_instructions,
             mi.name, mi.description, mi.image_url
           FROM order_items oi
           JOIN menu_items mi ON oi.menu_item_id = mi.id
           WHERE oi.order_id = $1`,
          [orderId]
        );
      } else if (order.order_type === 'mart') {
        itemsResult = await query(
          `SELECT
             oi.quantity, oi.unit_price, oi.total_price, oi.special_instructions,
             mp.name, mp.description, mp.image_url, mp.unit, mp.brand
           FROM order_items oi
           JOIN mart_products mp ON oi.mart_product_id = mp.id
           WHERE oi.order_id = $1`,
          [orderId]
        );
      }

      const orderDetails = {
        id: order.id,
        orderNumber: order.order_number,
        orderType: order.order_type,
        status: order.status,
        totalAmount: parseFloat(order.total_amount),
        deliveryFee: parseFloat(order.delivery_fee),
        deliveryAddress: order.delivery_address,
        deliveryLocation: {
          latitude: parseFloat(order.delivery_latitude),
          longitude: parseFloat(order.delivery_longitude)
        },
        specialInstructions: order.special_instructions,
        estimatedDeliveryTime: order.estimated_delivery_time,
        createdAt: order.created_at,
        updatedAt: order.updated_at,
        restaurant: order.order_type === 'restaurant' && order.restaurant_name ? {
          name: order.restaurant_name,
          cuisineType: order.cuisine_type,
          phone: order.restaurant_phone,
          address: order.restaurant_address,
          location: {
            latitude: parseFloat(order.restaurant_latitude),
            longitude: parseFloat(order.restaurant_longitude)
          }
        } : null,
        market: order.order_type === 'mart' && order.market_name ? {
          id: order.market_location_id || null,
          name: order.market_name,
          address: order.market_address,
          phone: order.market_phone,
          latitude: order.market_latitude ? parseFloat(order.market_latitude) : null,
          longitude: order.market_longitude ? parseFloat(order.market_longitude) : null
        } : null,
        driver: order.driver_name ? {
          name: order.driver_name,
          phone: order.driver_phone,
          currentLocation: order.driver_latitude && order.driver_longitude ? {
            latitude: parseFloat(order.driver_latitude),
            longitude: parseFloat(order.driver_longitude)
          } : null
        } : null,
        items: itemsResult ? itemsResult.rows.map(item => ({
          name: item.name,
          description: item.description,
          imageUrl: item.image_url,
          quantity: item.quantity,
          unitPrice: parseFloat(item.unit_price),
          totalPrice: parseFloat(item.total_price),
          specialInstructions: item.special_instructions || null,
          unit: item.unit || null,
          brand: item.brand || null
        })) : []
      };

      res.json({ order: orderDetails });

    } catch (error) {
      console.error('Get order details error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Cancel order (only if status is pending or confirmed)
router.put('/orders/:orderId/cancel', 
  authenticateToken, 
  requireRole(['customer']), 
  async (req, res) => {
    try {
      const { orderId } = req.params;
      const userId = req.user.id;

      // Get customer profile
      const customerProfile = await query(
        'SELECT id FROM customer_profiles WHERE user_id = $1',
        [userId]
      );

      if (customerProfile.rows.length === 0) {
        return res.status(400).json({ error: 'Customer profile not found' });
      }

      const customerId = customerProfile.rows[0].id;

      // Check if order exists and belongs to customer
      const orderResult = await query(
        'SELECT id, status FROM orders WHERE id = $1 AND customer_id = $2',
        [orderId, customerId]
      );

      if (orderResult.rows.length === 0) {
        return res.status(404).json({ error: 'Order not found' });
      }

      const order = orderResult.rows[0];

      // Check if order can be cancelled
      if (!['pending', 'confirmed'].includes(order.status)) {
        return res.status(400).json({ 
          error: 'Order cannot be cancelled. Current status: ' + order.status 
        });
      }

      // Update order status to cancelled
      await query(
        'UPDATE orders SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        ['cancelled', orderId]
      );

      res.json({ message: 'Order cancelled successfully' });

    } catch (error) {
      console.error('Cancel order error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get customer addresses
router.get('/addresses', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const userId = req.user.id;

    // Get customer profile
    const customerProfile = await query(
      'SELECT id FROM customer_profiles WHERE user_id = $1',
      [userId]
    );

    if (customerProfile.rows.length === 0) {
      return res.status(404).json({ error: 'Customer profile not found' });
    }

    const customerId = customerProfile.rows[0].id;

    // Get all addresses for this customer
    const addresses = await query(
      `SELECT id, label, address, latitude, longitude, is_default, created_at, updated_at
       FROM customer_addresses
       WHERE customer_id = $1
       ORDER BY is_default DESC, created_at ASC`,
      [customerId]
    );

    res.json({
      success: true,
      addresses: addresses.rows
    });
  } catch (error) {
    console.error('Get addresses error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Add new customer address
router.post('/addresses', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const { label, address, latitude, longitude, isDefault } = req.body;
    const userId = req.user.id;

    if (!label || !address || !latitude || !longitude) {
      return res.status(400).json({ error: 'Label, address, latitude, and longitude are required' });
    }

    // Get customer profile
    const customerProfile = await query(
      'SELECT id FROM customer_profiles WHERE user_id = $1',
      [userId]
    );

    if (customerProfile.rows.length === 0) {
      return res.status(404).json({ error: 'Customer profile not found' });
    }

    const customerId = customerProfile.rows[0].id;

    // If this is set as default, unset other defaults
    if (isDefault) {
      await query(
        'UPDATE customer_addresses SET is_default = FALSE WHERE customer_id = $1',
        [customerId]
      );
    }

    // Insert new address
    const result = await query(
      `INSERT INTO customer_addresses (customer_id, label, address, latitude, longitude, is_default)
       VALUES ($1, $2, $3, $4, $5, $6)
       RETURNING *`,
      [customerId, label, address, latitude, longitude, isDefault || false]
    );

    res.status(201).json({
      success: true,
      address: result.rows[0]
    });
  } catch (error) {
    console.error('Add address error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update customer address
router.put('/addresses/:addressId', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const { addressId } = req.params;
    const { label, address, latitude, longitude, isDefault } = req.body;
    const userId = req.user.id;

    // Get customer profile
    const customerProfile = await query(
      'SELECT id FROM customer_profiles WHERE user_id = $1',
      [userId]
    );

    if (customerProfile.rows.length === 0) {
      return res.status(404).json({ error: 'Customer profile not found' });
    }

    const customerId = customerProfile.rows[0].id;

    // Verify address belongs to this customer
    const addressCheck = await query(
      'SELECT id FROM customer_addresses WHERE id = $1 AND customer_id = $2',
      [addressId, customerId]
    );

    if (addressCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Address not found' });
    }

    // If this is set as default, unset other defaults
    if (isDefault) {
      await query(
        'UPDATE customer_addresses SET is_default = FALSE WHERE customer_id = $1 AND id != $2',
        [customerId, addressId]
      );
    }

    // Update address
    const result = await query(
      `UPDATE customer_addresses
       SET label = COALESCE($1, label),
           address = COALESCE($2, address),
           latitude = COALESCE($3, latitude),
           longitude = COALESCE($4, longitude),
           is_default = COALESCE($5, is_default),
           updated_at = CURRENT_TIMESTAMP
       WHERE id = $6 AND customer_id = $7
       RETURNING *`,
      [label, address, latitude, longitude, isDefault, addressId, customerId]
    );

    res.json({
      success: true,
      address: result.rows[0]
    });
  } catch (error) {
    console.error('Update address error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete customer address
router.delete('/addresses/:addressId', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const { addressId } = req.params;
    const userId = req.user.id;

    // Get customer profile
    const customerProfile = await query(
      'SELECT id FROM customer_profiles WHERE user_id = $1',
      [userId]
    );

    if (customerProfile.rows.length === 0) {
      return res.status(404).json({ error: 'Customer profile not found' });
    }

    const customerId = customerProfile.rows[0].id;

    // Verify address belongs to this customer
    const addressCheck = await query(
      'SELECT id, is_default FROM customer_addresses WHERE id = $1 AND customer_id = $2',
      [addressId, customerId]
    );

    if (addressCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Address not found' });
    }

    const wasDefault = addressCheck.rows[0].is_default;

    // Delete address
    await query(
      'DELETE FROM customer_addresses WHERE id = $1 AND customer_id = $2',
      [addressId, customerId]
    );

    // If deleted address was default, set another address as default
    if (wasDefault) {
      await query(
        `UPDATE customer_addresses
         SET is_default = TRUE
         WHERE customer_id = $1
         AND id = (SELECT id FROM customer_addresses WHERE customer_id = $1 LIMIT 1)`,
        [customerId]
      );
    }

    res.json({
      success: true,
      message: 'Address deleted successfully'
    });
  } catch (error) {
    console.error('Delete address error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// ============================================================================
// FAVORITES ENDPOINTS
// ============================================================================

// Get customer favorites
router.get('/favorites', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const userId = req.user.id;
    const { limit = 50, offset = 0 } = req.query;

    // Get customer profile
    const customerProfile = await query(
      'SELECT id FROM customer_profiles WHERE user_id = $1',
      [userId]
    );

    if (customerProfile.rows.length === 0) {
      return res.status(400).json({ error: 'Customer profile not found' });
    }

    const customerId = customerProfile.rows[0].id;

    // Get favorites with menu item and restaurant details
    const favoritesResult = await query(
      `SELECT
         cf.id, cf.menu_item_id, cf.restaurant_id, cf.created_at,
         mi.name, mi.description, mi.price, mi.image_url, mi.preparation_time,
         mi.is_available, mi.average_rating, mi.total_ratings,
         rp.restaurant_name, rp.cuisine_type
       FROM customer_favorites cf
       JOIN menu_items mi ON cf.menu_item_id = mi.id
       JOIN restaurant_profiles rp ON cf.restaurant_id = rp.id
       WHERE cf.customer_id = $1
       ORDER BY cf.created_at DESC
       LIMIT $2 OFFSET $3`,
      [customerId, limit, offset]
    );

    // Get total count
    const countResult = await query(
      'SELECT COUNT(*) as total FROM customer_favorites WHERE customer_id = $1',
      [customerId]
    );

    const favorites = favoritesResult.rows.map(row => ({
      id: row.id,
      menuItemId: row.menu_item_id,
      restaurantId: row.restaurant_id,
      name: row.name,
      description: row.description,
      price: parseFloat(row.price),
      imageUrl: row.image_url,
      preparationTime: row.preparation_time,
      isAvailable: row.is_available,
      averageRating: parseFloat(row.average_rating) || 0,
      totalRatings: row.total_ratings || 0,
      restaurantName: row.restaurant_name,
      cuisineType: row.cuisine_type,
      createdAt: row.created_at
    }));

    res.json({
      favorites,
      totalCount: parseInt(countResult.rows[0].total),
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

  } catch (error) {
    console.error('Get favorites error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Add item to favorites
router.post('/favorites', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const userId = req.user.id;
    const { menuItemId, restaurantId } = req.body;

    if (!menuItemId || !restaurantId) {
      return res.status(400).json({ error: 'Menu item ID and restaurant ID are required' });
    }

    // Get customer profile
    const customerProfile = await query(
      'SELECT id FROM customer_profiles WHERE user_id = $1',
      [userId]
    );

    if (customerProfile.rows.length === 0) {
      return res.status(400).json({ error: 'Customer profile not found' });
    }

    const customerId = customerProfile.rows[0].id;

    // Verify menu item exists and belongs to the restaurant
    const menuItemResult = await query(
      'SELECT id FROM menu_items WHERE id = $1 AND restaurant_id = $2',
      [menuItemId, restaurantId]
    );

    if (menuItemResult.rows.length === 0) {
      return res.status(404).json({ error: 'Menu item not found' });
    }

    // Check if already favorited
    const existingFavorite = await query(
      'SELECT id FROM customer_favorites WHERE customer_id = $1 AND menu_item_id = $2',
      [customerId, menuItemId]
    );

    if (existingFavorite.rows.length > 0) {
      return res.status(400).json({ error: 'Item is already in favorites' });
    }

    // Add to favorites
    const result = await query(
      `INSERT INTO customer_favorites (customer_id, menu_item_id, restaurant_id)
       VALUES ($1, $2, $3)
       RETURNING id, created_at`,
      [customerId, menuItemId, restaurantId]
    );

    res.status(201).json({
      success: true,
      message: 'Item added to favorites',
      favorite: {
        id: result.rows[0].id,
        menuItemId,
        restaurantId,
        createdAt: result.rows[0].created_at
      }
    });

  } catch (error) {
    console.error('Add to favorites error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Remove item from favorites
router.delete('/favorites/:menuItemId', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const userId = req.user.id;
    const { menuItemId } = req.params;

    // Get customer profile
    const customerProfile = await query(
      'SELECT id FROM customer_profiles WHERE user_id = $1',
      [userId]
    );

    if (customerProfile.rows.length === 0) {
      return res.status(400).json({ error: 'Customer profile not found' });
    }

    const customerId = customerProfile.rows[0].id;

    // Remove from favorites
    const result = await query(
      'DELETE FROM customer_favorites WHERE customer_id = $1 AND menu_item_id = $2 RETURNING id',
      [customerId, menuItemId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Favorite not found' });
    }

    res.json({
      success: true,
      message: 'Item removed from favorites'
    });

  } catch (error) {
    console.error('Remove from favorites error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Check if item is favorited
router.get('/favorites/check/:menuItemId', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const userId = req.user.id;
    const { menuItemId } = req.params;

    // Get customer profile
    const customerProfile = await query(
      'SELECT id FROM customer_profiles WHERE user_id = $1',
      [userId]
    );

    if (customerProfile.rows.length === 0) {
      return res.status(400).json({ error: 'Customer profile not found' });
    }

    const customerId = customerProfile.rows[0].id;

    // Check if favorited
    const result = await query(
      'SELECT id FROM customer_favorites WHERE customer_id = $1 AND menu_item_id = $2',
      [customerId, menuItemId]
    );

    res.json({
      isFavorited: result.rows.length > 0,
      favoriteId: result.rows.length > 0 ? result.rows[0].id : null
    });

  } catch (error) {
    console.error('Check favorite error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// ============================================================================
// DISCOUNT ENDPOINTS (Customer)
// ============================================================================

// Get available discounts for customer
router.get('/discounts/available', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const userId = req.user.id;
    const { restaurantId, menuItemIds } = req.query;

    // Get customer profile
    const customerProfile = await query(
      'SELECT id FROM customer_profiles WHERE user_id = $1',
      [userId]
    );

    if (customerProfile.rows.length === 0) {
      return res.status(400).json({ error: 'Customer profile not found' });
    }

    const customerId = customerProfile.rows[0].id;

    let whereConditions = [
      'd.is_active = true',
      'd.start_date <= NOW()',
      'd.end_date > NOW()'
    ];
    let queryParams = [];
    let paramCount = 0;

    // Build scope-based conditions
    let scopeConditions = ['d.scope = \'platform\''];

    if (restaurantId) {
      paramCount++;
      scopeConditions.push(`(d.scope = 'restaurant' AND d.restaurant_id = $${paramCount})`);
      queryParams.push(restaurantId);
    }

    if (menuItemIds) {
      const itemIds = Array.isArray(menuItemIds) ? menuItemIds : [menuItemIds];
      if (itemIds.length > 0) {
        paramCount++;
        scopeConditions.push(`(d.scope = 'menu_item' AND d.menu_item_id = ANY($${paramCount}))`);
        queryParams.push(itemIds);
      }
    }

    whereConditions.push(`(${scopeConditions.join(' OR ')})`);

    // Check time restrictions
    const currentDay = new Date().getDay(); // 0 = Sunday, 6 = Saturday
    const currentTime = new Date().toTimeString().slice(0, 8); // HH:MM:SS format

    const discountsResult = await query(`
      SELECT DISTINCT
        d.*,
        rp.restaurant_name,
        mi.name as menu_item_name,
        COALESCE(customer_usage.usage_count, 0) as customer_usage_count,
        COALESCE(total_usage.usage_count, 0) as total_usage_count
      FROM discounts d
      LEFT JOIN restaurant_profiles rp ON d.restaurant_id = rp.id
      LEFT JOIN menu_items mi ON d.menu_item_id = mi.id
      LEFT JOIN (
        SELECT discount_id, COUNT(*) as usage_count
        FROM discount_usage
        WHERE customer_id = $${paramCount + 1}
        GROUP BY discount_id
      ) customer_usage ON d.id = customer_usage.discount_id
      LEFT JOIN (
        SELECT discount_id, COUNT(*) as usage_count
        FROM discount_usage
        GROUP BY discount_id
      ) total_usage ON d.id = total_usage.discount_id
      LEFT JOIN discount_time_restrictions dtr ON d.id = dtr.discount_id
      WHERE ${whereConditions.join(' AND ')}
      AND (
        dtr.id IS NULL OR
        (dtr.day_of_week = $${paramCount + 2} AND dtr.start_time <= $${paramCount + 3} AND dtr.end_time >= $${paramCount + 3})
      )
      AND (d.usage_limit IS NULL OR COALESCE(total_usage.usage_count, 0) < d.usage_limit)
      AND (COALESCE(customer_usage.usage_count, 0) < d.usage_limit_per_customer)
      ORDER BY d.discount_value DESC, d.created_at DESC
    `, [...queryParams, customerId, currentDay, currentTime]);

    const discounts = discountsResult.rows.map(row => ({
      id: row.id,
      code: row.code,
      name: row.name,
      description: row.description,
      discountType: row.discount_type,
      discountValue: parseFloat(row.discount_value),
      scope: row.scope,
      restaurantId: row.restaurant_id,
      restaurantName: row.restaurant_name,
      menuItemId: row.menu_item_id,
      menuItemName: row.menu_item_name,
      minimumOrderAmount: parseFloat(row.minimum_order_amount),
      maximumDiscountAmount: row.maximum_discount_amount ? parseFloat(row.maximum_discount_amount) : null,
      usageLimit: row.usage_limit,
      usageLimitPerCustomer: row.usage_limit_per_customer,
      customerUsageCount: parseInt(row.customer_usage_count),
      totalUsageCount: parseInt(row.total_usage_count),
      startDate: row.start_date,
      endDate: row.end_date
    }));

    res.json({ discounts });

  } catch (error) {
    console.error('Get available discounts error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Validate discount code
router.post('/discounts/validate', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const userId = req.user.id;
    const { code, restaurantId, items, subtotal } = req.body;

    if (!code || !restaurantId || !items || subtotal === undefined) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Get customer profile
    const customerProfile = await query(
      'SELECT id FROM customer_profiles WHERE user_id = $1',
      [userId]
    );

    if (customerProfile.rows.length === 0) {
      return res.status(400).json({ error: 'Customer profile not found' });
    }

    const customerId = customerProfile.rows[0].id;

    // Find discount by code
    const discountResult = await query(`
      SELECT d.*, rp.restaurant_name, mi.name as menu_item_name
      FROM discounts d
      LEFT JOIN restaurant_profiles rp ON d.restaurant_id = rp.id
      LEFT JOIN menu_items mi ON d.menu_item_id = mi.id
      WHERE d.code = $1 AND d.is_active = true
      AND d.start_date <= NOW() AND d.end_date > NOW()
    `, [code]);

    if (discountResult.rows.length === 0) {
      return res.status(404).json({ error: 'Invalid or expired discount code' });
    }

    const discount = discountResult.rows[0];

    // Validate scope
    if (discount.scope === 'restaurant' && discount.restaurant_id !== parseInt(restaurantId)) {
      return res.status(400).json({ error: 'This discount is not valid for this restaurant' });
    }

    if (discount.scope === 'menu_item') {
      const hasValidItem = items.some(item => item.menuItemId === discount.menu_item_id);
      if (!hasValidItem) {
        return res.status(400).json({ error: 'This discount is not valid for the items in your cart' });
      }
    }

    // Check minimum order amount
    if (subtotal < parseFloat(discount.minimum_order_amount)) {
      return res.status(400).json({
        error: `Minimum order amount of $${discount.minimum_order_amount} required for this discount`,
        minimumAmount: parseFloat(discount.minimum_order_amount)
      });
    }

    // Check usage limits
    const usageCheck = await query(`
      SELECT
        COUNT(*) as total_usage,
        COUNT(CASE WHEN customer_id = $1 THEN 1 END) as customer_usage
      FROM discount_usage
      WHERE discount_id = $2
    `, [customerId, discount.id]);

    const { total_usage, customer_usage } = usageCheck.rows[0];

    if (discount.usage_limit && parseInt(total_usage) >= discount.usage_limit) {
      return res.status(400).json({ error: 'This discount has reached its usage limit' });
    }

    if (parseInt(customer_usage) >= discount.usage_limit_per_customer) {
      return res.status(400).json({ error: 'You have already used this discount the maximum number of times' });
    }

    // Calculate discount amount
    let discountAmount = 0;

    if (discount.discount_type === 'percentage') {
      if (discount.scope === 'menu_item') {
        // Apply percentage to specific menu items only
        const applicableItems = items.filter(item => item.menuItemId === discount.menu_item_id);
        const applicableSubtotal = applicableItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        discountAmount = applicableSubtotal * (parseFloat(discount.discount_value) / 100);
      } else {
        // Apply to entire subtotal
        discountAmount = subtotal * (parseFloat(discount.discount_value) / 100);
      }
    } else if (discount.discount_type === 'fixed_amount') {
      discountAmount = parseFloat(discount.discount_value);
    }

    // Apply maximum discount limit
    if (discount.maximum_discount_amount && discountAmount > parseFloat(discount.maximum_discount_amount)) {
      discountAmount = parseFloat(discount.maximum_discount_amount);
    }

    // Ensure discount doesn't exceed subtotal
    discountAmount = Math.min(discountAmount, subtotal);

    res.json({
      valid: true,
      discount: {
        id: discount.id,
        code: discount.code,
        name: discount.name,
        description: discount.description,
        discountType: discount.discount_type,
        discountValue: parseFloat(discount.discount_value),
        scope: discount.scope,
        discountAmount: Math.round(discountAmount * 100) / 100, // Round to 2 decimal places
        finalAmount: Math.round((subtotal - discountAmount) * 100) / 100
      }
    });

  } catch (error) {
    console.error('Validate discount error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get available discounts for customer (enhanced)
router.get('/discounts/available', authenticateToken, async (req, res) => {
  try {
    const customerId = req.user.id;
    const { restaurantId, orderItems, subtotalAmount } = req.query;

    if (!restaurantId) {
      return res.status(400).json({ error: 'Restaurant ID is required' });
    }

    // Parse order items if provided
    let parsedOrderItems = [];
    if (orderItems) {
      try {
        parsedOrderItems = JSON.parse(orderItems);
      } catch (e) {
        parsedOrderItems = [];
      }
    }

    const parsedSubtotal = parseFloat(subtotalAmount) || 0;

    const availableDiscounts = await DiscountValidator.getAvailableDiscounts(
      customerId,
      parseInt(restaurantId),
      parsedOrderItems,
      parsedSubtotal
    );

    res.json({
      success: true,
      data: {
        discounts: availableDiscounts
      }
    });

  } catch (error) {
    console.error('Get available discounts error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Validate discount code
router.post('/discounts/validate-code', authenticateToken, async (req, res) => {
  try {
    const customerId = req.user.id;
    const { code, restaurantId, orderItems, subtotalAmount } = req.body;

    if (!code || !restaurantId) {
      return res.status(400).json({ error: 'Discount code and restaurant ID are required' });
    }

    const validationResult = await DiscountValidator.validateDiscountCode(
      code,
      customerId,
      parseInt(restaurantId),
      orderItems || [],
      parseFloat(subtotalAmount) || 0
    );

    if (validationResult.isValid) {
      res.json({
        success: true,
        data: {
          discount: validationResult.discount,
          discountAmount: validationResult.discountAmount
        }
      });
    } else {
      res.status(400).json({
        success: false,
        error: validationResult.error,
        errorCode: validationResult.errorCode
      });
    }

  } catch (error) {
    console.error('Validate discount code error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
