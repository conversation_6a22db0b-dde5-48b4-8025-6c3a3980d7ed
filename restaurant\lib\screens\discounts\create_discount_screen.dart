import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/discount_provider.dart';
import '../../models/discount.dart';
import '../../shared/app_colors.dart';
import '../../shared/app_dimensions.dart';

class CreateDiscountScreen extends StatefulWidget {
  final Discount? discount;

  const CreateDiscountScreen({super.key, this.discount});

  @override
  State<CreateDiscountScreen> createState() => _CreateDiscountScreenState();
}

class _CreateDiscountScreenState extends State<CreateDiscountScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _codeController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _discountValueController = TextEditingController();
  final _minimumOrderController = TextEditingController();
  final _maximumDiscountController = TextEditingController();
  final _usageLimitController = TextEditingController();
  final _usageLimitPerCustomerController = TextEditingController();

  String _discountType = 'percentage';
  String _scope = 'restaurant';
  DateTime? _startDate;
  DateTime? _endDate;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.discount != null) {
      _populateFormWithDiscount(widget.discount!);
    } else {
      // Set default values for new discount
      _usageLimitPerCustomerController.text = '1';
      _minimumOrderController.text = '0';
    }
  }

  void _populateFormWithDiscount(Discount discount) {
    _nameController.text = discount.name;
    _codeController.text = discount.code ?? '';
    _descriptionController.text = discount.description ?? '';
    _discountValueController.text = discount.discountValue.toString();
    _minimumOrderController.text = discount.minimumOrderAmount.toString();
    _maximumDiscountController.text = discount.maximumDiscountAmount?.toString() ?? '';
    _usageLimitController.text = discount.usageLimit?.toString() ?? '';
    _usageLimitPerCustomerController.text = discount.usageLimitPerCustomer.toString();
    
    _discountType = _discountTypeToString(discount.discountType);
    _scope = _discountScopeToString(discount.scope);
    _startDate = discount.startDate;
    _endDate = discount.endDate;
  }

  String _discountTypeToString(DiscountType type) {
    switch (type) {
      case DiscountType.percentage:
        return 'percentage';
      case DiscountType.fixedAmount:
        return 'fixed_amount';
      case DiscountType.buyOneGetOne:
        return 'buy_one_get_one';
    }
  }

  String _discountScopeToString(DiscountScope scope) {
    switch (scope) {
      case DiscountScope.platform:
        return 'platform';
      case DiscountScope.restaurant:
        return 'restaurant';
      case DiscountScope.menuItem:
        return 'menu_item';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _codeController.dispose();
    _descriptionController.dispose();
    _discountValueController.dispose();
    _minimumOrderController.dispose();
    _maximumDiscountController.dispose();
    _usageLimitController.dispose();
    _usageLimitPerCustomerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          widget.discount != null ? 'Edit Discount' : 'Create Discount',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBasicInfoSection(),
              const SizedBox(height: 24),
              _buildDiscountConfigSection(),
              const SizedBox(height: 24),
              _buildValiditySection(),
              const SizedBox(height: 24),
              _buildLimitsSection(),
              const SizedBox(height: 32),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return _buildSection(
      title: 'Basic Information',
      children: [
        TextFormField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: 'Discount Name *',
            hintText: 'e.g., Weekend Special',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Discount name is required';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _codeController,
          decoration: const InputDecoration(
            labelText: 'Promo Code (Optional)',
            hintText: 'e.g., WEEKEND20',
            border: OutlineInputBorder(),
          ),
          textCapitalization: TextCapitalization.characters,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            labelText: 'Description (Optional)',
            hintText: 'Describe your discount offer',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildDiscountConfigSection() {
    return _buildSection(
      title: 'Discount Configuration',
      children: [
        DropdownButtonFormField<String>(
          value: _discountType,
          decoration: const InputDecoration(
            labelText: 'Discount Type *',
            border: OutlineInputBorder(),
          ),
          items: const [
            DropdownMenuItem(value: 'percentage', child: Text('Percentage Off')),
            DropdownMenuItem(value: 'fixed_amount', child: Text('Fixed Amount Off')),
            DropdownMenuItem(value: 'buy_one_get_one', child: Text('Buy One Get One Free')),
          ],
          onChanged: (value) {
            setState(() {
              _discountType = value!;
            });
          },
        ),
        const SizedBox(height: 16),
        if (_discountType != 'buy_one_get_one')
          TextFormField(
            controller: _discountValueController,
            decoration: InputDecoration(
              labelText: _discountType == 'percentage' ? 'Percentage (%) *' : 'Amount (\$) *',
              hintText: _discountType == 'percentage' ? 'e.g., 20' : 'e.g., 5.00',
              border: const OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Discount value is required';
              }
              final numValue = double.tryParse(value);
              if (numValue == null || numValue <= 0) {
                return 'Please enter a valid positive number';
              }
              if (_discountType == 'percentage' && numValue > 100) {
                return 'Percentage cannot exceed 100%';
              }
              return null;
            },
          ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _minimumOrderController,
                decoration: const InputDecoration(
                  labelText: 'Minimum Order (\$)',
                  hintText: '0.00',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final numValue = double.tryParse(value);
                    if (numValue == null || numValue < 0) {
                      return 'Invalid amount';
                    }
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _maximumDiscountController,
                decoration: const InputDecoration(
                  labelText: 'Max Discount (\$)',
                  hintText: 'No limit',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final numValue = double.tryParse(value);
                    if (numValue == null || numValue <= 0) {
                      return 'Invalid amount';
                    }
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildValiditySection() {
    return _buildSection(
      title: 'Validity Period',
      children: [
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => _selectDate(context, true),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Start Date *',
                    border: OutlineInputBorder(),
                  ),
                  child: Text(
                    _startDate != null
                        ? '${_startDate!.day}/${_startDate!.month}/${_startDate!.year}'
                        : 'Select start date',
                    style: TextStyle(
                      color: _startDate != null ? Colors.black87 : Colors.grey[600],
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: InkWell(
                onTap: () => _selectDate(context, false),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'End Date *',
                    border: OutlineInputBorder(),
                  ),
                  child: Text(
                    _endDate != null
                        ? '${_endDate!.day}/${_endDate!.month}/${_endDate!.year}'
                        : 'Select end date',
                    style: TextStyle(
                      color: _endDate != null ? Colors.black87 : Colors.grey[600],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLimitsSection() {
    return _buildSection(
      title: 'Usage Limits',
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _usageLimitController,
                decoration: const InputDecoration(
                  labelText: 'Total Usage Limit',
                  hintText: 'Unlimited',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final numValue = int.tryParse(value);
                    if (numValue == null || numValue <= 0) {
                      return 'Invalid number';
                    }
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _usageLimitPerCustomerController,
                decoration: const InputDecoration(
                  labelText: 'Per Customer Limit *',
                  hintText: '1',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Required';
                  }
                  final numValue = int.tryParse(value);
                  if (numValue == null || numValue <= 0) {
                    return 'Must be at least 1';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSection({required String title, required List<Widget> children}) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              side: const BorderSide(color: AppColors.primary),
            ),
            child: const Text(
              'Cancel',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.primary,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveDiscount,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    widget.discount != null ? 'Update Discount' : 'Create Discount',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate 
          ? (_startDate ?? DateTime.now())
          : (_endDate ?? DateTime.now().add(const Duration(days: 30))),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
          // If end date is before start date, reset it
          if (_endDate != null && _endDate!.isBefore(picked)) {
            _endDate = null;
          }
        } else {
          _endDate = picked;
        }
      });
    }
  }

  Future<void> _saveDiscount() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_startDate == null || _endDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select start and end dates'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final discountData = {
        'name': _nameController.text.trim(),
        'code': _codeController.text.trim().isEmpty ? null : _codeController.text.trim(),
        'description': _descriptionController.text.trim().isEmpty ? null : _descriptionController.text.trim(),
        'discount_type': _discountType,
        'discount_value': _discountType == 'buy_one_get_one' ? 0 : double.parse(_discountValueController.text),
        'scope': _scope,
        'minimum_order_amount': double.tryParse(_minimumOrderController.text) ?? 0,
        'maximum_discount_amount': _maximumDiscountController.text.trim().isEmpty 
            ? null 
            : double.parse(_maximumDiscountController.text),
        'usage_limit': _usageLimitController.text.trim().isEmpty 
            ? null 
            : int.parse(_usageLimitController.text),
        'usage_limit_per_customer': int.parse(_usageLimitPerCustomerController.text),
        'start_date': _startDate!.toIso8601String(),
        'end_date': _endDate!.toIso8601String(),
      };

      final provider = Provider.of<DiscountProvider>(context, listen: false);
      
      if (widget.discount != null) {
        await provider.updateDiscount(widget.discount!.id, discountData);
      } else {
        await provider.createDiscount(discountData);
      }

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.discount != null 
                  ? 'Discount updated successfully'
                  : 'Discount created successfully',
            ),
            backgroundColor: AppColors.primary,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
