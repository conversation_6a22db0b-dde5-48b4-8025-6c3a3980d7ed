import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/discount.dart';

class DiscountBadge extends StatelessWidget {
  final Discount discount;
  final double? fontSize;
  final EdgeInsets? padding;

  const DiscountBadge({
    super.key,
    required this.discount,
    this.fontSize = 10,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: _getDiscountColor(),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        discount.displayText,
        style: GoogleFonts.poppins(
          fontSize: fontSize,
          fontWeight: FontWeight.w700,
          color: Colors.white,
        ),
      ),
    );
  }

  Color _getDiscountColor() {
    switch (discount.discountType) {
      case DiscountType.percentage:
        return Colors.green[600]!;
      case DiscountType.fixedAmount:
        return Colors.blue[600]!;
      case DiscountType.buyOneGetOne:
        return Colors.orange[600]!;
    }
  }
}

class DiscountPriceDisplay extends StatelessWidget {
  final double originalPrice;
  final double discountedPrice;
  final Discount? discount;
  final TextStyle? originalPriceStyle;
  final TextStyle? discountedPriceStyle;

  const DiscountPriceDisplay({
    super.key,
    required this.originalPrice,
    required this.discountedPrice,
    this.discount,
    this.originalPriceStyle,
    this.discountedPriceStyle,
  });

  @override
  Widget build(BuildContext context) {
    final hasDiscount = discountedPrice < originalPrice;

    if (!hasDiscount) {
      return Text(
        '\$${originalPrice.toStringAsFixed(2)}',
        style: originalPriceStyle ?? TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.orange[600],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Discounted price
        Text(
          '\$${discountedPrice.toStringAsFixed(2)}',
          style: discountedPriceStyle ?? TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.green[600],
          ),
        ),
        // Original price (crossed out)
        Text(
          '\$${originalPrice.toStringAsFixed(2)}',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[500],
            decoration: TextDecoration.lineThrough,
          ),
        ),
        // Discount badge
        if (discount != null) ...[
          const SizedBox(height: 2),
          DiscountBadge(
            discount: discount!,
            fontSize: 8,
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
          ),
        ],
      ],
    );
  }
}

class RestaurantDiscountBanner extends StatelessWidget {
  final List<Discount> restaurantDiscounts;
  final VoidCallback? onTap;

  const RestaurantDiscountBanner({
    super.key,
    required this.restaurantDiscounts,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (restaurantDiscounts.isEmpty) return const SizedBox.shrink();

    final primaryDiscount = restaurantDiscounts.first;

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.orange[400]!,
            Colors.orange[600]!,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.local_offer,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        primaryDiscount.displayText,
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        primaryDiscount.name,
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: Colors.white.withOpacity(0.9),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (restaurantDiscounts.length > 1)
                        Text(
                          '+${restaurantDiscounts.length - 1} more offer${restaurantDiscounts.length > 2 ? 's' : ''}',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.white.withOpacity(0.8),
                          ),
                        ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
