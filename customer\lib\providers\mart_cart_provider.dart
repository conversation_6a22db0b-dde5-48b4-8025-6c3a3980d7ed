import 'package:flutter/material.dart';
import '../models/mart_cart.dart';
import '../services/api_service.dart';

class MartCartProvider with ChangeNotifier {
  MartCart? _cart;
  bool _isLoading = false;
  String? _error;
  List<dynamic> _suggestedProducts = [];
  bool _loadingSuggestions = false;

  // Getters
  MartCart? get cart => _cart;
  bool get hasItems => _cart != null && _cart!.items.isNotEmpty;
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<dynamic> get suggestedProducts => _suggestedProducts;
  bool get loadingSuggestions => _loadingSuggestions;

  int get itemCount =>
      _cart?.items.fold<int>(0, (sum, item) => sum + item.quantity) ?? 0;
  double get subtotal => _cart?.subtotal ?? 0.0;
  double get deliveryFee => _cart?.deliveryFee ?? 0.0;
  double get total => _cart?.total ?? 0.0;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Add item to mart cart
  void addToCart(
    Map<String, dynamic> product,
    int marketLocationId,
    String marketLocationName, {
    int quantity = 1,
  }) {
    try {
      _setError(null);

      // If cart is empty or from different market location, create new cart
      if (_cart == null || _cart!.marketLocationId != marketLocationId) {
        _cart = MartCart(
          marketLocationId: marketLocationId,
          marketLocationName: marketLocationName,
          items: [],
          deliveryFee: 3.0, // Default mart delivery fee
        );
      }

      // Check if item already exists in cart
      final existingItemIndex = _cart!.items.indexWhere(
        (item) => item.productId == product['id'],
      );

      if (existingItemIndex != -1) {
        // Update quantity of existing item
        final existingItem = _cart!.items[existingItemIndex];
        _cart!.items[existingItemIndex] = existingItem.copyWith(
          quantity: existingItem.quantity + quantity,
        );
      } else {
        // Add new item to cart
        final cartItem = MartCartItem.fromProduct(product, quantity: quantity);
        _cart!.items.add(cartItem);
      }

      _cart = _cart!.copyWith(items: List.from(_cart!.items));

      // Load suggestions when cart is updated
      _loadSuggestedProducts();

      notifyListeners();
    } catch (e) {
      _setError('Failed to add item to cart: $e');
    }
  }

  // Remove item from cart
  void removeFromCart(int productId) {
    if (_cart == null) return;

    _cart!.items.removeWhere((item) => item.productId == productId);

    if (_cart!.items.isEmpty) {
      _cart = null;
      _suggestedProducts = [];
    } else {
      _cart = _cart!.copyWith(items: List.from(_cart!.items));
      _loadSuggestedProducts();
    }

    notifyListeners();
  }

  // Update item quantity
  void updateQuantity(int productId, int newQuantity) {
    if (_cart == null) return;

    if (newQuantity <= 0) {
      removeFromCart(productId);
      return;
    }

    final itemIndex = _cart!.items.indexWhere(
      (item) => item.productId == productId,
    );
    if (itemIndex != -1) {
      _cart!.items[itemIndex] = _cart!.items[itemIndex].copyWith(
        quantity: newQuantity,
      );
      _cart = _cart!.copyWith(items: List.from(_cart!.items));
      notifyListeners();
    }
  }

  // Get item quantity in cart
  int getItemQuantity(int productId) {
    if (_cart == null) return 0;

    final item = _cart!.items.firstWhere(
      (item) => item.productId == productId,
      orElse: () => MartCartItem(
        productId: -1,
        name: '',
        price: 0,
        quantity: 0,
        unit: '',
      ),
    );

    return item.productId == -1 ? 0 : item.quantity;
  }

  // Clear cart
  void clearCart() {
    _cart = null;
    _suggestedProducts = [];
    _setError(null);
    notifyListeners();
  }

  // Load suggested products based on cart contents
  Future<void> _loadSuggestedProducts() async {
    if (_cart == null || _cart!.items.isEmpty) {
      _suggestedProducts = [];
      return;
    }

    try {
      _loadingSuggestions = true;
      notifyListeners();

      // Get categories of items in cart
      final cartCategories = _cart!.items
          .where((item) => item.categoryId != null)
          .map((item) => item.categoryId!)
          .toSet()
          .toList();

      if (cartCategories.isNotEmpty) {
        // Get products from same categories
        final suggestions = await ApiService.getMartProductsByCategory(
          cartCategories.first,
          limit: 6,
        );

        // Filter out items already in cart
        final cartProductIds = _cart!.items
            .map((item) => item.productId)
            .toSet();
        _suggestedProducts = suggestions
            .where((product) => !cartProductIds.contains(product['id']))
            .take(4)
            .toList();
      }
    } catch (e) {
      _suggestedProducts = [];
    } finally {
      _loadingSuggestions = false;
      notifyListeners();
    }
  }

  // Create mart order
  Future<Map<String, dynamic>> createOrder({
    required String deliveryAddress,
    required double deliveryLatitude,
    required double deliveryLongitude,
    String? specialInstructions,
  }) async {
    if (_cart == null || _cart!.items.isEmpty) {
      throw Exception('Cart is empty');
    }

    try {
      _setLoading(true);
      _setError(null);

      final orderData = await ApiService.createMartOrder(
        marketLocationId: _cart!.marketLocationId,
        items: _cart!.items.map((item) => item.toJson()).toList(),
        deliveryAddress: deliveryAddress,
        deliveryLatitude: deliveryLatitude,
        deliveryLongitude: deliveryLongitude,
        specialInstructions: specialInstructions,
        deliveryFee: _cart!.deliveryFee,
      );

      // Clear cart after successful order
      clearCart();

      return orderData;
    } catch (e) {
      _setError('Failed to create order: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Check if cart has items from different market location
  bool isFromDifferentMarketLocation(int marketLocationId) {
    return _cart != null && _cart!.marketLocationId != marketLocationId;
  }

  // Update delivery fee
  void updateDeliveryFee(double fee) {
    if (_cart != null) {
      _cart = _cart!.copyWith(deliveryFee: fee);
      notifyListeners();
    }
  }
}
