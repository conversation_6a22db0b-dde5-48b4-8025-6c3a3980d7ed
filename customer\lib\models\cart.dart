import 'restaurant.dart';
import 'discount.dart';

class CartItem {
  final int menuItemId;
  final String name;
  final String? description;
  final double price;
  final String? imageUrl;
  final int preparationTime;
  int quantity;
  String? specialInstructions;

  CartItem({
    required this.menuItemId,
    required this.name,
    this.description,
    required this.price,
    this.imageUrl,
    required this.preparationTime,
    this.quantity = 1,
    this.specialInstructions,
  });

  double get totalPrice => price * quantity;

  factory CartItem.fromMenuItem(
    MenuItem menuItem, {
    int quantity = 1,
    String? specialInstructions,
  }) {
    return CartItem(
      menuItemId: menuItem.id,
      name: menuItem.name,
      description: menuItem.description,
      price: menuItem.price,
      imageUrl: menuItem.imageUrl,
      preparationTime: menuItem.preparationTime,
      quantity: quantity,
      specialInstructions: specialInstructions,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'menuItemId': menuItemId,
      'quantity': quantity,
      'specialInstructions': specialInstructions,
    };
  }

  CartItem copyWith({int? quantity, String? specialInstructions}) {
    return CartItem(
      menuItemId: menuItemId,
      name: name,
      description: description,
      price: price,
      imageUrl: imageUrl,
      preparationTime: preparationTime,
      quantity: quantity ?? this.quantity,
      specialInstructions: specialInstructions ?? this.specialInstructions,
    );
  }
}

class Cart {
  final int restaurantId;
  final String restaurantName;
  final List<CartItem> items;
  final double deliveryFee;
  final Discount? appliedDiscount;
  final double discountAmount;

  Cart({
    required this.restaurantId,
    required this.restaurantName,
    this.items = const [],
    this.deliveryFee = 0.0,
    this.appliedDiscount,
    this.discountAmount = 0.0,
  });

  double get subtotal => items.fold(0.0, (sum, item) => sum + item.totalPrice);
  double get subtotalAfterDiscount => subtotal - discountAmount;
  double get total => subtotalAfterDiscount + deliveryFee;
  int get itemCount => items.fold(0, (sum, item) => sum + item.quantity);

  bool get hasDiscount => appliedDiscount != null && discountAmount > 0;

  bool get isEmpty => items.isEmpty;
  bool get isNotEmpty => items.isNotEmpty;

  Cart copyWith({
    int? restaurantId,
    String? restaurantName,
    List<CartItem>? items,
    double? deliveryFee,
    Discount? appliedDiscount,
    double? discountAmount,
  }) {
    return Cart(
      restaurantId: restaurantId ?? this.restaurantId,
      restaurantName: restaurantName ?? this.restaurantName,
      items: items ?? this.items,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      appliedDiscount: appliedDiscount ?? this.appliedDiscount,
      discountAmount: discountAmount ?? this.discountAmount,
    );
  }

  // Apply discount to cart
  Cart applyDiscount(Discount discount) {
    final calculatedDiscountAmount = discount.calculateDiscountAmount(
      subtotal,
      items,
    );
    return copyWith(
      appliedDiscount: discount,
      discountAmount: calculatedDiscountAmount,
    );
  }

  // Remove discount from cart
  Cart removeDiscount() {
    return copyWith(appliedDiscount: null, discountAmount: 0.0);
  }

  // Check if discount is still valid for current cart
  bool isDiscountValid() {
    if (appliedDiscount == null) return false;

    // Check minimum order amount
    if (subtotal < appliedDiscount!.minimumOrderAmount) return false;

    // Check if discount is still active
    if (!appliedDiscount!.isCurrentlyValid) return false;

    // Check time restrictions
    if (!appliedDiscount!.isValidForCurrentTime) return false;

    // Check scope validity
    if (appliedDiscount!.scope == DiscountScope.restaurant &&
        appliedDiscount!.restaurantId != restaurantId) {
      return false;
    }

    if (appliedDiscount!.scope == DiscountScope.menuItem &&
        !items.any((item) => item.menuItemId == appliedDiscount!.menuItemId)) {
      return false;
    }

    return true;
  }

  Map<String, dynamic> toJson() {
    return {
      'restaurantId': restaurantId,
      'items': items.map((item) => item.toJson()).toList(),
      'deliveryFee': deliveryFee,
    };
  }
}
