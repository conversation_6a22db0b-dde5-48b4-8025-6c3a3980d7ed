class MartCartItem {
  final int productId;
  final String name;
  final String? description;
  final double price;
  final String? imageUrl;
  final String unit;
  final String? brand;
  final int? categoryId;
  int quantity;

  MartCartItem({
    required this.productId,
    required this.name,
    this.description,
    required this.price,
    this.imageUrl,
    required this.unit,
    this.brand,
    this.categoryId,
    this.quantity = 1,
  });

  double get totalPrice => price * quantity;

  factory MartCartItem.fromProduct(
    Map<String, dynamic> product, {
    int quantity = 1,
  }) {
    return MartCartItem(
      productId: product['id'],
      name: product['name'],
      description: product['description'],
      price: double.tryParse(product['price'].toString()) ?? 0.0,
      imageUrl: product['image_url'],
      unit: product['unit'] ?? 'piece',
      brand: product['brand'],
      categoryId: product['category_id'],
      quantity: quantity,
    );
  }

  Map<String, dynamic> toJson() {
    return {'productId': productId, 'quantity': quantity};
  }

  MartCartItem copyWith({int? quantity, String? description}) {
    return MartCartItem(
      productId: productId,
      name: name,
      description: description ?? this.description,
      price: price,
      imageUrl: imageUrl,
      unit: unit,
      brand: brand,
      categoryId: categoryId,
      quantity: quantity ?? this.quantity,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MartCartItem && other.productId == productId;
  }

  @override
  int get hashCode => productId.hashCode;
}

class MartCart {
  final int marketLocationId;
  final String marketLocationName;
  final List<MartCartItem> items;
  final double deliveryFee;

  MartCart({
    required this.marketLocationId,
    required this.marketLocationName,
    this.items = const [],
    this.deliveryFee = 0.0,
  });

  double get subtotal {
    return items.fold(0.0, (sum, item) => sum + item.totalPrice);
  }

  double get total {
    return subtotal + deliveryFee;
  }

  int get totalItems {
    return items.fold(0, (sum, item) => sum + item.quantity);
  }

  bool get isEmpty => items.isEmpty;
  bool get isNotEmpty => items.isNotEmpty;

  MartCart copyWith({
    int? marketLocationId,
    String? marketLocationName,
    List<MartCartItem>? items,
    double? deliveryFee,
  }) {
    return MartCart(
      marketLocationId: marketLocationId ?? this.marketLocationId,
      marketLocationName: marketLocationName ?? this.marketLocationName,
      items: items ?? this.items,
      deliveryFee: deliveryFee ?? this.deliveryFee,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'marketLocationId': marketLocationId,
      'marketLocationName': marketLocationName,
      'items': items.map((item) => item.toJson()).toList(),
      'deliveryFee': deliveryFee,
      'subtotal': subtotal,
      'total': total,
    };
  }

  factory MartCart.fromJson(Map<String, dynamic> json) {
    return MartCart(
      marketLocationId: json['marketLocationId'],
      marketLocationName: json['marketLocationName'],
      items: (json['items'] as List)
          .map((item) => MartCartItem.fromProduct(item))
          .toList(),
      deliveryFee: (json['deliveryFee'] as num).toDouble(),
    );
  }

  @override
  String toString() {
    return 'MartCart(marketLocationId: $marketLocationId, marketLocationName: $marketLocationName, items: ${items.length}, total: \$${total.toStringAsFixed(2)})';
  }
}
