import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:provider/provider.dart';
import '../../models/order.dart';
import '../../utils/location_utils.dart';
import '../../providers/orders_provider.dart';

class DeliveryNavigationScreen extends StatefulWidget {
  final Order order;

  const DeliveryNavigationScreen({super.key, required this.order});

  @override
  State<DeliveryNavigationScreen> createState() =>
      _DeliveryNavigationScreenState();
}

class _DeliveryNavigationScreenState extends State<DeliveryNavigationScreen> {
  GoogleMapController? _mapController;
  Position? _currentPosition;
  StreamSubscription<Position>? _positionStream;
  final Set<Marker> _markers = {};
  final Set<Polyline> _polylines = {};
  bool _isLoading = true;
  String _currentStatus = '';

  @override
  void initState() {
    super.initState();
    _currentStatus = widget.order.status;
    _initializeLocation();
  }

  @override
  void dispose() {
    _positionStream?.cancel();
    super.dispose();
  }

  Future<void> _initializeLocation() async {
    try {
      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _showError('Location permissions are denied');
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _showError('Location permissions are permanently denied');
        return;
      }

      // Get current position
      _currentPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Start position stream for real-time updates
      _positionStream = Geolocator.getPositionStream(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10, // Update every 10 meters
        ),
      ).listen(_onPositionChanged);

      _setupMarkers();
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      _showError('Failed to get location: $e');
    }
  }

  void _onPositionChanged(Position position) {
    setState(() {
      _currentPosition = position;
    });
    _updateDriverMarker();
  }

  void _setupMarkers() {
    _markers.clear();

    // Add driver marker
    if (_currentPosition != null) {
      _markers.add(
        Marker(
          markerId: const MarkerId('driver'),
          position: LatLng(
            _currentPosition!.latitude,
            _currentPosition!.longitude,
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          infoWindow: const InfoWindow(title: 'Your Location'),
        ),
      );
    }

    // Add restaurant marker
    if (widget.order.restaurantLatitude != null &&
        widget.order.restaurantLongitude != null) {
      _markers.add(
        Marker(
          markerId: const MarkerId('restaurant'),
          position: LatLng(
            widget.order.restaurantLatitude!,
            widget.order.restaurantLongitude!,
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(
            BitmapDescriptor.hueOrange,
          ),
          infoWindow: InfoWindow(
            title: 'Pickup: ${widget.order.restaurantName}',
            snippet: widget.order.restaurantAddress,
          ),
        ),
      );
    }

    // Add delivery marker
    if (widget.order.deliveryLatitude != null &&
        widget.order.deliveryLongitude != null) {
      _markers.add(
        Marker(
          markerId: const MarkerId('delivery'),
          position: LatLng(
            widget.order.deliveryLatitude!,
            widget.order.deliveryLongitude!,
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
          infoWindow: InfoWindow(
            title: 'Delivery: ${widget.order.customerName}',
            snippet: widget.order.deliveryAddress,
          ),
        ),
      );
    }
  }

  void _updateDriverMarker() {
    if (_currentPosition == null) return;

    _markers.removeWhere((marker) => marker.markerId.value == 'driver');
    _markers.add(
      Marker(
        markerId: const MarkerId('driver'),
        position: LatLng(
          _currentPosition!.latitude,
          _currentPosition!.longitude,
        ),
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        infoWindow: const InfoWindow(title: 'Your Location'),
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Order #${widget.order.orderNumber}'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _callCustomer,
            icon: const Icon(Icons.phone),
            tooltip: 'Call Customer',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Status and navigation info
                _buildNavigationHeader(),

                // Map
                Expanded(
                  child: GoogleMap(
                    initialCameraPosition: CameraPosition(
                      target: _currentPosition != null
                          ? LatLng(
                              _currentPosition!.latitude,
                              _currentPosition!.longitude,
                            )
                          : const LatLng(37.7749, -122.4194),
                      zoom: 14.0,
                    ),
                    markers: _markers,
                    polylines: _polylines,
                    onMapCreated: (GoogleMapController controller) {
                      _mapController = controller;
                      _fitMarkersInView();
                    },
                    myLocationEnabled: true,
                    myLocationButtonEnabled: true,
                    zoomControlsEnabled: true,
                  ),
                ),

                // Bottom action panel
                _buildActionPanel(),
              ],
            ),
    );
  }

  Widget _buildNavigationHeader() {
    final isGoingToPickupLocation =
        LocationUtils.shouldNavigateToPickupLocation(_currentStatus);
    final targetLocation = isGoingToPickupLocation
        ? (widget.order.isMartOrder ? 'Market' : 'Restaurant')
        : 'Customer';
    final targetName = isGoingToPickupLocation
        ? (widget.order.pickupLocationName ?? 'Unknown Location')
        : widget.order.customerName;
    final targetAddress = isGoingToPickupLocation
        ? (widget.order.pickupLocationAddress ?? 'Unknown Address')
        : widget.order.deliveryAddress;

    double? distance;
    String? direction;
    int? estimatedTime;

    if (_currentPosition != null) {
      final targetLat = isGoingToPickupLocation
          ? widget.order.pickupLocationLatitude
          : widget.order.deliveryLatitude;
      final targetLng = isGoingToPickupLocation
          ? widget.order.pickupLocationLongitude
          : widget.order.deliveryLongitude;

      if (targetLat != null && targetLng != null) {
        distance = LocationUtils.calculateDistance(
          _currentPosition!.latitude,
          _currentPosition!.longitude,
          targetLat,
          targetLng,
        );

        final bearing = LocationUtils.calculateBearing(
          _currentPosition!.latitude,
          _currentPosition!.longitude,
          targetLat,
          targetLng,
        );

        direction = LocationUtils.getDirection(bearing);
        estimatedTime = LocationUtils.calculateEstimatedTravelMinutes(distance);
      }
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: isGoingToPickupLocation
                      ? Colors.orange[100]
                      : Colors.green[100],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'Going to $targetLocation',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: isGoingToPickupLocation
                        ? Colors.orange[700]
                        : Colors.green[700],
                  ),
                ),
              ),
              const Spacer(),
              if (distance != null && estimatedTime != null)
                Text(
                  '${LocationUtils.formatDistance(distance)} • ${estimatedTime}min',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            targetName,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 4),
          Text(
            targetAddress,
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
          if (direction != null) ...[
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.navigation, size: 16, color: Colors.blue[600]),
                const SizedBox(width: 4),
                Text(
                  'Head $direction',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (distance != null && distance <= 0.05) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green[100],
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      'In Range',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.green[700],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionPanel() {
    final isGoingToPickupLocation =
        LocationUtils.shouldNavigateToPickupLocation(_currentStatus);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Primary action button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _openNavigation,
              icon: const Icon(Icons.navigation, size: 20),
              label: Text(
                isGoingToPickupLocation
                    ? (widget.order.isMartOrder
                          ? 'Navigate to Market'
                          : 'Navigate to Restaurant')
                    : 'Navigate to Customer',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: isGoingToPickupLocation
                    ? Colors.orange[600]
                    : Colors.green[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),

          const SizedBox(height: 12),

          // Secondary actions
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _callCustomer,
                  icon: const Icon(Icons.phone, size: 18),
                  label: const Text('Call Customer'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.blue[600],
                    side: BorderSide(color: Colors.blue[600]!),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _updateOrderStatus,
                  icon: const Icon(Icons.check_circle, size: 18),
                  label: Text(_getStatusButtonText()),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.green[600],
                    side: BorderSide(color: Colors.green[600]!),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Order summary
          _buildOrderSummary(),
        ],
      ),
    );
  }

  Widget _buildOrderSummary() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.receipt, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 8),
              Text(
                'Order Summary',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${widget.order.items.length} items',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
              Text(
                '\$${widget.order.totalAmount.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          if (widget.order.specialInstructions != null) ...[
            const SizedBox(height: 8),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(Icons.note, size: 14, color: Colors.orange[600]),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    widget.order.specialInstructions!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.orange[700],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  String _getStatusButtonText() {
    switch (_currentStatus.toLowerCase()) {
      case 'assigned':
        return 'Mark as Picked Up';
      case 'picked_up':
        return 'Mark as Delivered';
      default:
        return 'Update Status';
    }
  }

  void _openNavigation() async {
    final coords = LocationUtils.getNavigationCoordinates(
      _currentStatus,
      widget.order.pickupLocationLatitude,
      widget.order.pickupLocationLongitude,
      widget.order.deliveryLatitude,
      widget.order.deliveryLongitude,
    );

    if (coords != null) {
      final url =
          'https://www.google.com/maps/dir/?api=1&destination=${coords['latitude']},${coords['longitude']}';
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      }
    }
  }

  void _callCustomer() async {
    final phoneUrl = 'tel:${widget.order.customerPhone}';
    if (await canLaunchUrl(Uri.parse(phoneUrl))) {
      await launchUrl(Uri.parse(phoneUrl));
    }
  }

  void _updateOrderStatus() async {
    if (_currentPosition == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Getting your location...'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    String newStatus;
    String message;

    switch (_currentStatus.toLowerCase()) {
      case 'assigned':
        newStatus = 'picked_up';
        message = 'Order picked up from restaurant';
        break;
      case 'picked_up':
        newStatus = 'delivered';
        message = 'Order delivered to customer';
        break;
      default:
        return;
    }

    try {
      final ordersProvider = Provider.of<OrdersProvider>(
        context,
        listen: false,
      );
      await ordersProvider.updateOrderStatusWithLocation(
        widget.order.id,
        newStatus,
        _currentPosition!.latitude,
        _currentPosition!.longitude,
      );

      setState(() {
        _currentStatus = newStatus;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(message), backgroundColor: Colors.green[600]),
        );

        if (newStatus == 'delivered') {
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = e.toString().replaceFirst('Exception: ', '');

        // Show more user-friendly error messages
        if (errorMessage.contains('must be at the restaurant')) {
          errorMessage =
              'You need to be at the restaurant to mark as picked up';
        } else if (errorMessage.contains('must be at the customer')) {
          errorMessage =
              'You need to be at the customer location to mark as delivered';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'OK',
              textColor: Colors.white,
              onPressed: () {},
            ),
          ),
        );
      }
    }
  }

  void _fitMarkersInView() {
    if (_mapController != null && _markers.length > 1) {
      final bounds = _calculateBounds();
      _mapController!.animateCamera(
        CameraUpdate.newLatLngBounds(bounds, 100.0),
      );
    }
  }

  LatLngBounds _calculateBounds() {
    double minLat = double.infinity;
    double maxLat = -double.infinity;
    double minLng = double.infinity;
    double maxLng = -double.infinity;

    for (final marker in _markers) {
      minLat = minLat < marker.position.latitude
          ? minLat
          : marker.position.latitude;
      maxLat = maxLat > marker.position.latitude
          ? maxLat
          : marker.position.latitude;
      minLng = minLng < marker.position.longitude
          ? minLng
          : marker.position.longitude;
      maxLng = maxLng > marker.position.longitude
          ? maxLng
          : marker.position.longitude;
    }

    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }
}
