enum DiscountType {
  percentage,
  fixedAmount,
  buyOneGetOne,
}

enum DiscountScope {
  platform,
  restaurant,
  menuItem,
}

class Discount {
  final int id;
  final String? code;
  final String name;
  final String? description;
  final DiscountType discountType;
  final double discountValue;
  final DiscountScope scope;
  final int? restaurantId;
  final int? menuItemId;
  final double minimumOrderAmount;
  final double? maximumDiscountAmount;
  final int? usageLimit;
  final int usageLimitPerCustomer;
  final DateTime startDate;
  final DateTime endDate;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Analytics fields
  final int? totalUsageCount;
  final int? customerUsageCount;
  final double? totalSavings;
  final String? restaurantName;
  final String? menuItemName;

  const Discount({
    required this.id,
    this.code,
    required this.name,
    this.description,
    required this.discountType,
    required this.discountValue,
    required this.scope,
    this.restaurantId,
    this.menuItemId,
    required this.minimumOrderAmount,
    this.maximumDiscountAmount,
    this.usageLimit,
    required this.usageLimitPerCustomer,
    required this.startDate,
    required this.endDate,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.totalUsageCount,
    this.customerUsageCount,
    this.totalSavings,
    this.restaurantName,
    this.menuItemName,
  });

  factory Discount.fromJson(Map<String, dynamic> json) {
    return Discount(
      id: json['id'],
      code: json['code'],
      name: json['name'],
      description: json['description'],
      discountType: _parseDiscountType(json['discount_type']),
      discountValue: double.parse(json['discount_value'].toString()),
      scope: _parseDiscountScope(json['scope']),
      restaurantId: json['restaurant_id'],
      menuItemId: json['menu_item_id'],
      minimumOrderAmount: double.parse(json['minimum_order_amount'].toString()),
      maximumDiscountAmount: json['maximum_discount_amount'] != null
          ? double.parse(json['maximum_discount_amount'].toString())
          : null,
      usageLimit: json['usage_limit'],
      usageLimitPerCustomer: json['usage_limit_per_customer'] ?? 1,
      startDate: DateTime.parse(json['start_date']),
      endDate: DateTime.parse(json['end_date']),
      isActive: json['is_active'] ?? true,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      totalUsageCount: json['total_usage'],
      customerUsageCount: json['customer_usage'],
      totalSavings: json['total_savings'] != null
          ? double.parse(json['total_savings'].toString())
          : null,
      restaurantName: json['restaurant_name'],
      menuItemName: json['menu_item_name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'description': description,
      'discount_type': _discountTypeToString(discountType),
      'discount_value': discountValue,
      'scope': _discountScopeToString(scope),
      'restaurant_id': restaurantId,
      'menu_item_id': menuItemId,
      'minimum_order_amount': minimumOrderAmount,
      'maximum_discount_amount': maximumDiscountAmount,
      'usage_limit': usageLimit,
      'usage_limit_per_customer': usageLimitPerCustomer,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  static DiscountType _parseDiscountType(String type) {
    switch (type) {
      case 'percentage':
        return DiscountType.percentage;
      case 'fixed_amount':
        return DiscountType.fixedAmount;
      case 'buy_one_get_one':
        return DiscountType.buyOneGetOne;
      default:
        return DiscountType.percentage;
    }
  }

  static DiscountScope _parseDiscountScope(String scope) {
    switch (scope) {
      case 'platform':
        return DiscountScope.platform;
      case 'restaurant':
        return DiscountScope.restaurant;
      case 'menu_item':
        return DiscountScope.menuItem;
      default:
        return DiscountScope.restaurant;
    }
  }

  static String _discountTypeToString(DiscountType type) {
    switch (type) {
      case DiscountType.percentage:
        return 'percentage';
      case DiscountType.fixedAmount:
        return 'fixed_amount';
      case DiscountType.buyOneGetOne:
        return 'buy_one_get_one';
    }
  }

  static String _discountScopeToString(DiscountScope scope) {
    switch (scope) {
      case DiscountScope.platform:
        return 'platform';
      case DiscountScope.restaurant:
        return 'restaurant';
      case DiscountScope.menuItem:
        return 'menu_item';
    }
  }

  String get displayText {
    switch (discountType) {
      case DiscountType.percentage:
        return '${discountValue.toInt()}% OFF';
      case DiscountType.fixedAmount:
        return '\$${discountValue.toStringAsFixed(2)} OFF';
      case DiscountType.buyOneGetOne:
        return 'Buy 1 Get 1 FREE';
    }
  }

  String get scopeDisplayText {
    switch (scope) {
      case DiscountScope.platform:
        return 'Platform-wide';
      case DiscountScope.restaurant:
        return 'Restaurant-wide';
      case DiscountScope.menuItem:
        return menuItemName ?? 'Menu Item';
    }
  }

  bool get isExpired {
    return DateTime.now().isAfter(endDate);
  }

  bool get isScheduled {
    return DateTime.now().isBefore(startDate);
  }

  bool get isCurrentlyActive {
    final now = DateTime.now();
    return isActive && now.isAfter(startDate) && now.isBefore(endDate);
  }

  bool get hasUsageLimit {
    return usageLimit != null && usageLimit! > 0;
  }

  bool get isUsageLimitReached {
    if (!hasUsageLimit) return false;
    return (totalUsageCount ?? 0) >= usageLimit!;
  }

  double calculateDiscountAmount(double orderAmount) {
    if (orderAmount < minimumOrderAmount) return 0.0;

    double discountAmount = 0.0;

    switch (discountType) {
      case DiscountType.percentage:
        discountAmount = orderAmount * (discountValue / 100);
        break;
      case DiscountType.fixedAmount:
        discountAmount = discountValue;
        break;
      case DiscountType.buyOneGetOne:
        // For BOGO, this would need more complex logic based on items
        // For now, return 0 as it requires item-level calculation
        discountAmount = 0.0;
        break;
    }

    // Apply maximum discount limit if set
    if (maximumDiscountAmount != null && discountAmount > maximumDiscountAmount!) {
      discountAmount = maximumDiscountAmount!;
    }

    // Ensure discount doesn't exceed order amount
    return discountAmount > orderAmount ? orderAmount : discountAmount;
  }

  Discount copyWith({
    int? id,
    String? code,
    String? name,
    String? description,
    DiscountType? discountType,
    double? discountValue,
    DiscountScope? scope,
    int? restaurantId,
    int? menuItemId,
    double? minimumOrderAmount,
    double? maximumDiscountAmount,
    int? usageLimit,
    int? usageLimitPerCustomer,
    DateTime? startDate,
    DateTime? endDate,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? totalUsageCount,
    int? customerUsageCount,
    double? totalSavings,
    String? restaurantName,
    String? menuItemName,
  }) {
    return Discount(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      discountType: discountType ?? this.discountType,
      discountValue: discountValue ?? this.discountValue,
      scope: scope ?? this.scope,
      restaurantId: restaurantId ?? this.restaurantId,
      menuItemId: menuItemId ?? this.menuItemId,
      minimumOrderAmount: minimumOrderAmount ?? this.minimumOrderAmount,
      maximumDiscountAmount: maximumDiscountAmount ?? this.maximumDiscountAmount,
      usageLimit: usageLimit ?? this.usageLimit,
      usageLimitPerCustomer: usageLimitPerCustomer ?? this.usageLimitPerCustomer,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      totalUsageCount: totalUsageCount ?? this.totalUsageCount,
      customerUsageCount: customerUsageCount ?? this.customerUsageCount,
      totalSavings: totalSavings ?? this.totalSavings,
      restaurantName: restaurantName ?? this.restaurantName,
      menuItemName: menuItemName ?? this.menuItemName,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Discount && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Discount(id: $id, name: $name, type: $discountType, value: $discountValue)';
  }
}
