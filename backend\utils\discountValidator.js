const { query } = require('../config/database');

/**
 * Comprehensive discount validation utility
 */
class DiscountValidator {
  
  /**
   * Validate a discount for order application
   * @param {number} discountId - The discount ID
   * @param {number} customerId - The customer ID
   * @param {number} restaurantId - The restaurant ID
   * @param {Array} orderItems - Array of order items
   * @param {number} subtotalAmount - Order subtotal amount
   * @param {string} discountCode - Optional discount code
   * @returns {Object} Validation result
   */
  static async validateDiscount(discountId, customerId, restaurantId, orderItems, subtotalAmount, discountCode = null) {
    try {
      // Get discount with usage statistics (simplified query without restaurant/menu item joins)
      const discountResult = await query(`
        SELECT d.*,
               COALESCE(usage_stats.total_usage, 0) as total_usage,
               COALESCE(customer_usage.customer_usage, 0) as customer_usage
        FROM discounts d
        LEFT JOIN (
          SELECT discount_id, COUNT(*) as total_usage
          FROM discount_usage
          GROUP BY discount_id
        ) usage_stats ON d.id = usage_stats.discount_id
        LEFT JOIN (
          SELECT discount_id, COUNT(*) as customer_usage
          FROM discount_usage
          WHERE customer_id = $1
          GROUP BY discount_id
        ) customer_usage ON d.id = customer_usage.discount_id
        WHERE d.id = $2
      `, [customerId, discountId]);

      if (discountResult.rows.length === 0) {
        return {
          isValid: false,
          error: 'Discount not found',
          errorCode: 'DISCOUNT_NOT_FOUND'
        };
      }

      const discount = discountResult.rows[0];

      // Basic validation checks
      const basicValidation = this.validateBasicRules(discount, discountCode);
      if (!basicValidation.isValid) {
        return basicValidation;
      }

      // Time-based validation
      const timeValidation = this.validateTimeRestrictions(discount);
      if (!timeValidation.isValid) {
        return timeValidation;
      }

      // Usage limit validation
      const usageValidation = this.validateUsageLimits(discount);
      if (!usageValidation.isValid) {
        return usageValidation;
      }

      // Scope validation
      const scopeValidation = this.validateScope(discount, restaurantId, orderItems);
      if (!scopeValidation.isValid) {
        return scopeValidation;
      }

      // Order amount validation
      const amountValidation = this.validateOrderAmount(discount, subtotalAmount);
      if (!amountValidation.isValid) {
        return amountValidation;
      }

      // Calculate discount amount
      const discountAmount = this.calculateDiscountAmount(discount, orderItems, subtotalAmount);

      return {
        isValid: true,
        discount: discount,
        discountAmount: discountAmount,
        maxDiscountAmount: discount.maximum_discount_amount
      };

    } catch (error) {
      console.error('Discount validation error:', error);
      return {
        isValid: false,
        error: 'Internal validation error',
        errorCode: 'VALIDATION_ERROR'
      };
    }
  }

  /**
   * Validate basic discount rules
   */
  static validateBasicRules(discount, discountCode) {
    // Check if discount is active
    if (!discount.is_active) {
      return {
        isValid: false,
        error: 'This discount is currently inactive',
        errorCode: 'DISCOUNT_INACTIVE'
      };
    }

    // Check discount code if provided
    if (discountCode && discount.code && discount.code !== discountCode) {
      return {
        isValid: false,
        error: 'Invalid discount code',
        errorCode: 'INVALID_CODE'
      };
    }

    return { isValid: true };
  }

  /**
   * Validate time-based restrictions
   */
  static validateTimeRestrictions(discount) {
    const now = new Date();
    const startDate = new Date(discount.start_date);
    const endDate = new Date(discount.end_date);

    // Check if discount is within valid date range
    if (now < startDate) {
      return {
        isValid: false,
        error: `This discount is not yet active. It will be available from ${startDate.toLocaleDateString()}`,
        errorCode: 'DISCOUNT_NOT_STARTED'
      };
    }

    if (now > endDate) {
      return {
        isValid: false,
        error: 'This discount has expired',
        errorCode: 'DISCOUNT_EXPIRED'
      };
    }

    // TODO: Add day-of-week and time-of-day restrictions
    // This would require additional database tables for time restrictions

    return { isValid: true };
  }

  /**
   * Validate usage limits
   */
  static validateUsageLimits(discount) {
    // Check total usage limit
    if (discount.usage_limit && parseInt(discount.total_usage) >= discount.usage_limit) {
      return {
        isValid: false,
        error: 'This discount has reached its usage limit',
        errorCode: 'USAGE_LIMIT_EXCEEDED'
      };
    }

    // Check per-customer usage limit
    if (parseInt(discount.customer_usage) >= discount.usage_limit_per_customer) {
      return {
        isValid: false,
        error: `You have already used this discount the maximum number of times (${discount.usage_limit_per_customer})`,
        errorCode: 'CUSTOMER_LIMIT_EXCEEDED'
      };
    }

    return { isValid: true };
  }

  /**
   * Validate discount scope
   */
  static validateScope(discount, restaurantId, orderItems) {
    switch (discount.scope) {
      case 'platform':
        // Platform-wide discounts are valid for all restaurants
        return { isValid: true };

      case 'restaurant':
        if (discount.restaurant_id !== parseInt(restaurantId)) {
          return {
            isValid: false,
            error: 'This discount is only valid for a specific restaurant',
            errorCode: 'INVALID_RESTAURANT'
          };
        }
        return { isValid: true };

      case 'menu_item':
        const hasValidItem = orderItems.some(item => item.menuItemId === discount.menu_item_id);
        if (!hasValidItem) {
          return {
            isValid: false,
            error: 'This discount is only valid for specific menu items',
            errorCode: 'INVALID_MENU_ITEM'
          };
        }
        return { isValid: true };

      default:
        return {
          isValid: false,
          error: 'Invalid discount scope',
          errorCode: 'INVALID_SCOPE'
        };
    }
  }

  /**
   * Validate order amount requirements
   */
  static validateOrderAmount(discount, subtotalAmount) {
    const minimumAmount = parseFloat(discount.minimum_order_amount);
    
    if (subtotalAmount < minimumAmount) {
      return {
        isValid: false,
        error: `Minimum order amount of $${minimumAmount.toFixed(2)} required for this discount`,
        errorCode: 'MINIMUM_ORDER_NOT_MET'
      };
    }

    return { isValid: true };
  }

  /**
   * Calculate discount amount based on discount type and rules
   */
  static calculateDiscountAmount(discount, orderItems, subtotalAmount) {
    let discountAmount = 0;

    switch (discount.discount_type) {
      case 'percentage':
        discountAmount = subtotalAmount * (parseFloat(discount.discount_value) / 100);
        break;

      case 'fixed_amount':
        discountAmount = parseFloat(discount.discount_value);
        break;

      case 'buy_one_get_one':
        // For BOGO, calculate based on applicable items
        if (discount.scope === 'menu_item') {
          const applicableItems = orderItems.filter(item => item.menuItemId === discount.menu_item_id);
          const totalQuantity = applicableItems.reduce((sum, item) => sum + item.quantity, 0);
          const freeItems = Math.floor(totalQuantity / 2);
          const itemPrice = applicableItems.length > 0 ? applicableItems[0].unitPrice : 0;
          discountAmount = freeItems * itemPrice;
        }
        break;

      default:
        discountAmount = 0;
    }

    // Apply maximum discount limit if set
    if (discount.maximum_discount_amount && discountAmount > parseFloat(discount.maximum_discount_amount)) {
      discountAmount = parseFloat(discount.maximum_discount_amount);
    }

    // Ensure discount doesn't exceed order amount
    if (discountAmount > subtotalAmount) {
      discountAmount = subtotalAmount;
    }

    return Math.max(0, discountAmount);
  }

  /**
   * Validate discount code
   */
  static async validateDiscountCode(code, customerId, restaurantId, orderItems, subtotalAmount) {
    try {
      const discountResult = await query(`
        SELECT id FROM discounts 
        WHERE code = $1 AND is_active = true
        AND start_date <= NOW() AND end_date > NOW()
      `, [code.toUpperCase()]);

      if (discountResult.rows.length === 0) {
        return {
          isValid: false,
          error: 'Invalid or expired discount code',
          errorCode: 'INVALID_CODE'
        };
      }

      const discountId = discountResult.rows[0].id;
      return await this.validateDiscount(discountId, customerId, restaurantId, orderItems, subtotalAmount, code);

    } catch (error) {
      console.error('Discount code validation error:', error);
      return {
        isValid: false,
        error: 'Error validating discount code',
        errorCode: 'VALIDATION_ERROR'
      };
    }
  }

  /**
   * Check for discount conflicts
   */
  static async checkDiscountConflicts(discountId, customerId) {
    // For now, we'll implement a simple rule: only one discount per order
    // In the future, this could be expanded to handle complex conflict rules
    
    return {
      hasConflicts: false,
      conflicts: []
    };
  }

  /**
   * Get available discounts for a customer and restaurant
   */
  static async getAvailableDiscounts(customerId, restaurantId, orderItems = [], subtotalAmount = 0) {
    try {
      const discountsResult = await query(`
        SELECT d.*,
               COALESCE(usage_stats.total_usage, 0) as total_usage,
               COALESCE(customer_usage.customer_usage, 0) as customer_usage
        FROM discounts d
        LEFT JOIN (
          SELECT discount_id, COUNT(*) as total_usage
          FROM discount_usage
          GROUP BY discount_id
        ) usage_stats ON d.id = usage_stats.discount_id
        LEFT JOIN (
          SELECT discount_id, COUNT(*) as customer_usage
          FROM discount_usage
          WHERE customer_id = $1
          GROUP BY discount_id
        ) customer_usage ON d.id = customer_usage.discount_id
        WHERE d.is_active = true
        AND d.start_date <= NOW() 
        AND d.end_date > NOW()
        AND (d.scope = 'platform' OR d.restaurant_id = $2)
        ORDER BY d.created_at DESC
      `, [customerId, restaurantId]);

      const availableDiscounts = [];

      for (const discount of discountsResult.rows) {
        const validation = await this.validateDiscount(
          discount.id, 
          customerId, 
          restaurantId, 
          orderItems, 
          subtotalAmount
        );

        if (validation.isValid) {
          availableDiscounts.push({
            ...discount,
            calculated_discount_amount: validation.discountAmount
          });
        }
      }

      return availableDiscounts;

    } catch (error) {
      console.error('Get available discounts error:', error);
      return [];
    }
  }
}

module.exports = DiscountValidator;
