const { pool } = require('../config/database');

const discountMigrations = [
  // Main discounts table
  `
    CREATE TABLE IF NOT EXISTS discounts (
      id SERIAL PRIMARY KEY,
      code VARCHAR(50) UNIQUE,
      name VARCHAR(255) NOT NULL,
      description TEXT,
      discount_type VARCHAR(20) NOT NULL CHECK (discount_type IN ('percentage', 'fixed_amount', 'buy_one_get_one')),
      discount_value DECIMAL(10, 2) NOT NULL,
      scope VARCHAR(20) NOT NULL CHECK (scope IN ('platform', 'restaurant', 'menu_item')),
      restaurant_id INTEGER REFERENCES restaurant_profiles(id) ON DELETE CASCADE,
      menu_item_id INTEGER REFERENCES menu_items(id) ON DELETE CASCADE,
      minimum_order_amount DECIMAL(10, 2) DEFAULT 0.00,
      maximum_discount_amount DECIMAL(10, 2),
      usage_limit INTEGER,
      usage_limit_per_customer INTEGER DEFAULT 1,
      start_date TIMESTAMP NOT NULL,
      end_date TIMESTAMP NOT NULL,
      is_active BOOLEAN DEFAULT true,
      created_by_admin BOOLEAN DEFAULT false,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      CONSTRAINT valid_restaurant_scope CHECK (
        (scope = 'restaurant' AND restaurant_id IS NOT NULL AND menu_item_id IS NULL) OR
        (scope = 'menu_item' AND menu_item_id IS NOT NULL AND restaurant_id IS NOT NULL) OR
        (scope = 'platform' AND restaurant_id IS NULL AND menu_item_id IS NULL)
      ),
      CONSTRAINT valid_dates CHECK (end_date > start_date),
      CONSTRAINT valid_discount_value CHECK (
        (discount_type = 'percentage' AND discount_value > 0 AND discount_value <= 100) OR
        (discount_type = 'fixed_amount' AND discount_value > 0) OR
        (discount_type = 'buy_one_get_one' AND discount_value = 1)
      )
    );
  `,

  // Discount usage tracking table
  `
    CREATE TABLE IF NOT EXISTS discount_usage (
      id SERIAL PRIMARY KEY,
      discount_id INTEGER REFERENCES discounts(id) ON DELETE CASCADE,
      customer_id INTEGER REFERENCES customer_profiles(id) ON DELETE CASCADE,
      order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
      discount_amount DECIMAL(10, 2) NOT NULL,
      original_amount DECIMAL(10, 2) NOT NULL,
      final_amount DECIMAL(10, 2) NOT NULL,
      used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(discount_id, order_id)
    );
  `,

  // Discount time restrictions table (for specific days/hours)
  `
    CREATE TABLE IF NOT EXISTS discount_time_restrictions (
      id SERIAL PRIMARY KEY,
      discount_id INTEGER REFERENCES discounts(id) ON DELETE CASCADE,
      day_of_week INTEGER CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0 = Sunday, 6 = Saturday
      start_time TIME,
      end_time TIME,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,

  // Add discount columns to orders table
  `
    ALTER TABLE orders 
    ADD COLUMN IF NOT EXISTS discount_id INTEGER REFERENCES discounts(id) ON DELETE SET NULL,
    ADD COLUMN IF NOT EXISTS discount_amount DECIMAL(10, 2) DEFAULT 0.00,
    ADD COLUMN IF NOT EXISTS subtotal_amount DECIMAL(10, 2);
  `,

  // Update existing orders to set subtotal_amount
  `
    UPDATE orders 
    SET subtotal_amount = total_amount - delivery_fee 
    WHERE subtotal_amount IS NULL;
  `,

  // Create indexes for better performance
  `
    CREATE INDEX IF NOT EXISTS idx_discounts_code ON discounts(code);
  `,

  `
    CREATE INDEX IF NOT EXISTS idx_discounts_restaurant_id ON discounts(restaurant_id);
  `,

  `
    CREATE INDEX IF NOT EXISTS idx_discounts_menu_item_id ON discounts(menu_item_id);
  `,

  `
    CREATE INDEX IF NOT EXISTS idx_discounts_active_dates ON discounts(is_active, start_date, end_date);
  `,

  `
    CREATE INDEX IF NOT EXISTS idx_discount_usage_customer_id ON discount_usage(customer_id);
  `,

  `
    CREATE INDEX IF NOT EXISTS idx_discount_usage_discount_id ON discount_usage(discount_id);
  `,

  `
    CREATE INDEX IF NOT EXISTS idx_discount_time_restrictions_discount_id ON discount_time_restrictions(discount_id);
  `,

  // Create function to update discount usage count
  `
    CREATE OR REPLACE FUNCTION update_discount_usage_count()
    RETURNS TRIGGER AS $$
    BEGIN
      -- This function can be used to automatically update usage statistics
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
  `,

  // Create trigger for discount usage tracking
  `
    CREATE TRIGGER discount_usage_trigger
    AFTER INSERT ON discount_usage
    FOR EACH ROW
    EXECUTE FUNCTION update_discount_usage_count();
  `,
];

async function runDiscountMigrations() {
  const client = await pool.connect();
  
  try {
    console.log('🔄 Running discount system migrations...');
    
    for (let i = 0; i < discountMigrations.length; i++) {
      const migration = discountMigrations[i];
      console.log(`Running migration ${i + 1}/${discountMigrations.length}...`);
      await client.query(migration);
    }
    
    console.log('✅ Discount system migrations completed successfully!');
  } catch (error) {
    console.error('❌ Error running discount migrations:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run migrations if this file is executed directly
if (require.main === module) {
  runDiscountMigrations()
    .then(() => {
      console.log('Discount migrations completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { runDiscountMigrations };
