require('dotenv').config();
const { query } = require('../config/database');

async function verifyMartData() {
  try {
    console.log('🔍 Verifying mart data...\n');

    // Check market locations
    const locations = await query('SELECT * FROM market_locations ORDER BY id');
    console.log('📍 MARKET LOCATIONS:');
    console.log(`   Total: ${locations.rows.length}`);
    locations.rows.forEach(location => {
      console.log(`   • ${location.name} (${location.latitude}, ${location.longitude})`);
      console.log(`     ${location.address}`);
      console.log(`     Phone: ${location.phone}`);
    });

    // Check categories
    const categories = await query('SELECT * FROM mart_categories ORDER BY sort_order');
    console.log('\n🏷️ CATEGORIES:');
    console.log(`   Total: ${categories.rows.length}`);
    categories.rows.forEach(category => {
      console.log(`   ${category.sort_order}. ${category.name} (ID: ${category.id})`);
    });

    // Check products by category
    const products = await query(`
      SELECT p.*, c.name as category_name 
      FROM mart_products p 
      LEFT JOIN mart_categories c ON p.category_id = c.id 
      ORDER BY c.sort_order, p.name
    `);
    
    console.log('\n🛒 PRODUCTS:');
    console.log(`   Total: ${products.rows.length}`);
    
    const productsByCategory = {};
    products.rows.forEach(product => {
      const categoryName = product.category_name || 'Uncategorized';
      if (!productsByCategory[categoryName]) {
        productsByCategory[categoryName] = [];
      }
      productsByCategory[categoryName].push(product);
    });

    Object.entries(productsByCategory).forEach(([categoryName, categoryProducts]) => {
      console.log(`\n   📦 ${categoryName} (${categoryProducts.length} items):`);
      categoryProducts.forEach(product => {
        const hasImage = product.image_url ? '🖼️' : '❌';
        console.log(`     ${hasImage} ${product.name} - $${product.price}/${product.unit} (Stock: ${product.stock_quantity})`);
        if (product.brand) console.log(`        Brand: ${product.brand}`);
      });
    });

    // Check products with images
    const productsWithImages = await query('SELECT COUNT(*) as count FROM mart_products WHERE image_url IS NOT NULL AND image_url != \'\'');
    const productsWithoutImages = await query('SELECT COUNT(*) as count FROM mart_products WHERE image_url IS NULL OR image_url = \'\'');
    
    console.log('\n📊 IMAGE STATISTICS:');
    console.log(`   Products with images: ${productsWithImages.rows[0].count}`);
    console.log(`   Products without images: ${productsWithoutImages.rows[0].count}`);

    // Check price ranges
    const priceStats = await query(`
      SELECT 
        MIN(price) as min_price,
        MAX(price) as max_price,
        AVG(price) as avg_price,
        COUNT(*) as total_products
      FROM mart_products
    `);
    
    console.log('\n💰 PRICE STATISTICS:');
    const stats = priceStats.rows[0];
    console.log(`   Price range: $${parseFloat(stats.min_price).toFixed(2)} - $${parseFloat(stats.max_price).toFixed(2)}`);
    console.log(`   Average price: $${parseFloat(stats.avg_price).toFixed(2)}`);

    // Check stock levels
    const stockStats = await query(`
      SELECT 
        SUM(stock_quantity) as total_stock,
        COUNT(CASE WHEN stock_quantity = 0 THEN 1 END) as out_of_stock,
        COUNT(CASE WHEN stock_quantity < 10 THEN 1 END) as low_stock
      FROM mart_products
    `);
    
    console.log('\n📦 STOCK STATISTICS:');
    const stock = stockStats.rows[0];
    console.log(`   Total items in stock: ${stock.total_stock}`);
    console.log(`   Out of stock items: ${stock.out_of_stock}`);
    console.log(`   Low stock items (< 10): ${stock.low_stock}`);

    console.log('\n✅ Mart data verification completed!');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error verifying mart data:', error);
    process.exit(1);
  }
}

verifyMartData();
