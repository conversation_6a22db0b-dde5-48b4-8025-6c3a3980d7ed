import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import '../services/location_tracking_service.dart';
import '../models/order.dart';

class DeliveryTrackingProvider extends ChangeNotifier {
  final LocationTrackingService _locationService = LocationTrackingService();

  Order? _activeOrder;
  Position? _currentPosition;
  Position? _restaurantPosition;
  Position? _deliveryPosition;
  bool _isTracking = false;
  String? _error;

  // Delivery status
  DeliveryStatus _deliveryStatus = DeliveryStatus.pending;
  double? _distanceToRestaurant;
  double? _distanceToDelivery;
  double? _estimatedTimeToDelivery;

  // Getters
  Order? get activeOrder => _activeOrder;
  Position? get currentPosition => _currentPosition;
  Position? get restaurantPosition => _restaurantPosition;
  Position? get deliveryPosition => _deliveryPosition;
  bool get isTracking => _isTracking;
  String? get error => _error;
  DeliveryStatus get deliveryStatus => _deliveryStatus;
  double? get distanceToRestaurant => _distanceToRestaurant;
  double? get distanceToDelivery => _distanceToDelivery;
  double? get estimatedTimeToDelivery => _estimatedTimeToDelivery;

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _setTracking(bool tracking) {
    _isTracking = tracking;
    notifyListeners();
  }

  void _setDeliveryStatus(DeliveryStatus status) {
    _deliveryStatus = status;
    notifyListeners();
  }

  /// Start tracking delivery for an order
  Future<bool> startDeliveryTracking(Order order) async {
    try {
      _setError(null);
      _activeOrder = order;

      // Set delivery position from order
      if (order.deliveryLatitude != null && order.deliveryLongitude != null) {
        _deliveryPosition = Position(
          latitude: order.deliveryLatitude!,
          longitude: order.deliveryLongitude!,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          altitudeAccuracy: 0,
          heading: 0,
          headingAccuracy: 0,
          speed: 0,
          speedAccuracy: 0,
        );
      }

      // Set pickup location position if available (restaurant or market)
      double? pickupLat, pickupLng;
      if (order.isRestaurantOrder && order.restaurant != null) {
        pickupLat = order.restaurant!.latitude;
        pickupLng = order.restaurant!.longitude;
      } else if (order.isMartOrder && order.market != null) {
        pickupLat = order.market!.latitude;
        pickupLng = order.market!.longitude;
      }

      if (pickupLat != null && pickupLng != null) {
        _restaurantPosition = Position(
          latitude: pickupLat,
          longitude: pickupLng,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          altitudeAccuracy: 0,
          heading: 0,
          headingAccuracy: 0,
          speed: 0,
          speedAccuracy: 0,
        );
      }

      // Add location callback
      _locationService.addLocationCallback(_onLocationUpdate);

      // Start location tracking
      final success = await _locationService.startTracking(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10, // Update every 10 meters
      );

      if (success) {
        _setTracking(true);
        _updateDeliveryStatus();
        return true;
      } else {
        _setError('Failed to start location tracking');
        return false;
      }
    } catch (e) {
      _setError('Error starting delivery tracking: $e');
      return false;
    }
  }

  /// Stop delivery tracking
  Future<void> stopDeliveryTracking() async {
    try {
      _locationService.removeLocationCallback(_onLocationUpdate);
      await _locationService.stopTracking();

      _setTracking(false);
      _activeOrder = null;
      _currentPosition = null;
      _restaurantPosition = null;
      _deliveryPosition = null;
      _distanceToRestaurant = null;
      _distanceToDelivery = null;
      _estimatedTimeToDelivery = null;
      _setDeliveryStatus(DeliveryStatus.pending);
      _setError(null);
    } catch (e) {
      _setError('Error stopping delivery tracking: $e');
    }
  }

  /// Handle location updates
  void _onLocationUpdate(Position position) {
    _currentPosition = position;
    _calculateDistances();
    _updateDeliveryStatus();
    notifyListeners();
  }

  /// Calculate distances to restaurant and delivery location
  void _calculateDistances() {
    if (_currentPosition == null) return;

    // Calculate distance to restaurant
    if (_restaurantPosition != null) {
      _distanceToRestaurant = _locationService.calculateDistance(
        _currentPosition!.latitude,
        _currentPosition!.longitude,
        _restaurantPosition!.latitude,
        _restaurantPosition!.longitude,
      );
    }

    // Calculate distance to delivery location
    if (_deliveryPosition != null) {
      _distanceToDelivery = _locationService.calculateDistance(
        _currentPosition!.latitude,
        _currentPosition!.longitude,
        _deliveryPosition!.latitude,
        _deliveryPosition!.longitude,
      );

      // Estimate delivery time (assuming average speed of 30 km/h)
      if (_distanceToDelivery != null) {
        const averageSpeedKmh = 30.0;
        const averageSpeedMs = averageSpeedKmh * 1000 / 3600; // m/s
        _estimatedTimeToDelivery =
            _distanceToDelivery! / averageSpeedMs / 60; // minutes
      }
    }
  }

  /// Update delivery status based on current position and order status
  void _updateDeliveryStatus() {
    if (_activeOrder == null) return;

    switch (_activeOrder!.status) {
      case 'pending':
      case 'confirmed':
        _setDeliveryStatus(DeliveryStatus.orderConfirmed);
        break;
      case 'preparing':
        _setDeliveryStatus(DeliveryStatus.preparing);
        break;
      case 'ready':
        _setDeliveryStatus(DeliveryStatus.readyForPickup);
        break;
      case 'picked_up':
        _setDeliveryStatus(DeliveryStatus.onTheWay);
        break;
      case 'delivered':
        _setDeliveryStatus(DeliveryStatus.delivered);
        break;
      case 'cancelled':
        _setDeliveryStatus(DeliveryStatus.cancelled);
        break;
      default:
        _setDeliveryStatus(DeliveryStatus.pending);
    }
  }

  /// Get current location without starting tracking
  Future<Position?> getCurrentLocation() async {
    try {
      _setError(null);
      final position = await _locationService.getCurrentLocation();
      if (position != null) {
        _currentPosition = position;
        notifyListeners();
      }
      return position;
    } catch (e) {
      _setError('Failed to get current location: $e');
      return null;
    }
  }

  /// Check if location permissions are available
  Future<bool> hasLocationPermission() async {
    return await _locationService.hasLocationPermission();
  }

  /// Request location permissions
  Future<bool> requestLocationPermission() async {
    return await _locationService.requestLocationPermission();
  }

  /// Open location settings
  Future<bool> openLocationSettings() async {
    return await _locationService.openLocationSettings();
  }

  /// Get formatted distance string
  String getFormattedDistance(double? distance) {
    if (distance == null) return 'Unknown';

    if (distance < 1000) {
      return '${distance.round()}m';
    } else {
      return '${(distance / 1000).toStringAsFixed(1)}km';
    }
  }

  /// Get formatted time string
  String getFormattedTime(double? timeInMinutes) {
    if (timeInMinutes == null) return 'Unknown';

    if (timeInMinutes < 60) {
      return '${timeInMinutes.round()} min';
    } else {
      final hours = (timeInMinutes / 60).floor();
      final minutes = (timeInMinutes % 60).round();
      return '${hours}h ${minutes}min';
    }
  }

  /// Get delivery status description
  String getDeliveryStatusDescription() {
    switch (_deliveryStatus) {
      case DeliveryStatus.pending:
        return 'Order Pending';
      case DeliveryStatus.orderConfirmed:
        return 'Order Confirmed';
      case DeliveryStatus.preparing:
        return 'Preparing Your Order';
      case DeliveryStatus.readyForPickup:
        return 'Ready for Pickup';
      case DeliveryStatus.onTheWay:
        return 'On the Way';
      case DeliveryStatus.delivered:
        return 'Delivered';
      case DeliveryStatus.cancelled:
        return 'Cancelled';
    }
  }

  @override
  void dispose() {
    stopDeliveryTracking();
    super.dispose();
  }
}

enum DeliveryStatus {
  pending,
  orderConfirmed,
  preparing,
  readyForPickup,
  onTheWay,
  delivered,
  cancelled,
}
