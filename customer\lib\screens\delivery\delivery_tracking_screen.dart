import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../../providers/delivery_tracking_provider.dart';
import '../../models/order.dart';

class DeliveryTrackingScreen extends StatefulWidget {
  final Order order;

  const DeliveryTrackingScreen({super.key, required this.order});

  @override
  State<DeliveryTrackingScreen> createState() => _DeliveryTrackingScreenState();
}

class _DeliveryTrackingScreenState extends State<DeliveryTrackingScreen> {
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final trackingProvider = Provider.of<DeliveryTrackingProvider>(
        context,
        listen: false,
      );
      trackingProvider.startDeliveryTracking(widget.order);
    });
  }

  @override
  void dispose() {
    final trackingProvider = Provider.of<DeliveryTrackingProvider>(
      context,
      listen: false,
    );
    trackingProvider.stopDeliveryTracking();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Track Order #${widget.order.orderNumber}'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: Consumer<DeliveryTrackingProvider>(
        builder: (context, trackingProvider, child) {
          return Column(
            children: [
              // Delivery Status Card
              _buildDeliveryStatusCard(trackingProvider),

              // Map
              Expanded(child: _buildMap(trackingProvider)),

              // Order Details
              _buildOrderDetailsCard(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildDeliveryStatusCard(DeliveryTrackingProvider provider) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.delivery_dining,
                  color: Colors.orange,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      provider.getDeliveryStatusDescription(),
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade800,
                      ),
                    ),
                    if (provider.estimatedTimeToDelivery != null)
                      Text(
                        'ETA: ${provider.getFormattedTime(provider.estimatedTimeToDelivery)}',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),

          if (provider.distanceToDelivery != null) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.location_on, color: Colors.grey.shade600, size: 16),
                const SizedBox(width: 4),
                Text(
                  'Distance: ${provider.getFormattedDistance(provider.distanceToDelivery)}',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ],

          if (provider.error != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      provider.error!,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.red.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMap(DeliveryTrackingProvider provider) {
    _updateMarkers(provider);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: GoogleMap(
          onMapCreated: (GoogleMapController controller) {
            _mapController = controller;
            _fitMarkersInView(provider);
          },
          initialCameraPosition: CameraPosition(
            target: provider.deliveryPosition != null
                ? LatLng(
                    provider.deliveryPosition!.latitude,
                    provider.deliveryPosition!.longitude,
                  )
                : const LatLng(37.7749, -122.4194), // Default to San Francisco
            zoom: 14.0,
          ),
          markers: _markers,
          myLocationEnabled: true,
          myLocationButtonEnabled: false,
          zoomControlsEnabled: false,
          mapToolbarEnabled: false,
          compassEnabled: true,
          buildingsEnabled: true,
          trafficEnabled: true,
        ),
      ),
    );
  }

  Widget _buildOrderDetailsCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Details',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(Icons.restaurant, color: Colors.grey.shade600, size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  widget.order.pickupLocationName ?? 'Unknown Location',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.location_on, color: Colors.grey.shade600, size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  widget.order.deliveryAddress,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.attach_money, color: Colors.grey.shade600, size: 16),
              const SizedBox(width: 8),
              Text(
                'Total: \$${widget.order.totalAmount.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.green.shade700,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _updateMarkers(DeliveryTrackingProvider provider) {
    _markers.clear();

    // Add restaurant marker
    if (provider.restaurantPosition != null) {
      _markers.add(
        Marker(
          markerId: const MarkerId('restaurant'),
          position: LatLng(
            provider.restaurantPosition!.latitude,
            provider.restaurantPosition!.longitude,
          ),
          infoWindow: InfoWindow(
            title: widget.order.pickupLocationName ?? 'Unknown Location',
            snippet: widget.order.isMartOrder ? 'Market' : 'Restaurant',
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        ),
      );
    }

    // Add delivery location marker
    if (provider.deliveryPosition != null) {
      _markers.add(
        Marker(
          markerId: const MarkerId('delivery'),
          position: LatLng(
            provider.deliveryPosition!.latitude,
            provider.deliveryPosition!.longitude,
          ),
          infoWindow: const InfoWindow(
            title: 'Delivery Location',
            snippet: 'Your address',
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(
            BitmapDescriptor.hueGreen,
          ),
        ),
      );
    }

    // Add current location marker (if tracking)
    if (provider.currentPosition != null && provider.isTracking) {
      _markers.add(
        Marker(
          markerId: const MarkerId('current'),
          position: LatLng(
            provider.currentPosition!.latitude,
            provider.currentPosition!.longitude,
          ),
          infoWindow: const InfoWindow(
            title: 'Your Location',
            snippet: 'Current position',
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(
            BitmapDescriptor.hueOrange,
          ),
        ),
      );
    }
  }

  void _fitMarkersInView(DeliveryTrackingProvider provider) {
    if (_mapController == null) return;

    final List<LatLng> positions = [];

    if (provider.restaurantPosition != null) {
      positions.add(
        LatLng(
          provider.restaurantPosition!.latitude,
          provider.restaurantPosition!.longitude,
        ),
      );
    }

    if (provider.deliveryPosition != null) {
      positions.add(
        LatLng(
          provider.deliveryPosition!.latitude,
          provider.deliveryPosition!.longitude,
        ),
      );
    }

    if (provider.currentPosition != null) {
      positions.add(
        LatLng(
          provider.currentPosition!.latitude,
          provider.currentPosition!.longitude,
        ),
      );
    }

    if (positions.isNotEmpty) {
      double minLat = positions.first.latitude;
      double maxLat = positions.first.latitude;
      double minLng = positions.first.longitude;
      double maxLng = positions.first.longitude;

      for (final pos in positions) {
        minLat = minLat < pos.latitude ? minLat : pos.latitude;
        maxLat = maxLat > pos.latitude ? maxLat : pos.latitude;
        minLng = minLng < pos.longitude ? minLng : pos.longitude;
        maxLng = maxLng > pos.longitude ? maxLng : pos.longitude;
      }

      _mapController!.animateCamera(
        CameraUpdate.newLatLngBounds(
          LatLngBounds(
            southwest: LatLng(minLat, minLng),
            northeast: LatLng(maxLat, maxLng),
          ),
          100.0, // padding
        ),
      );
    }
  }
}
