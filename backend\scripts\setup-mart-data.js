require('dotenv').config();
const { query } = require('../config/database');

async function setupMartData() {
  try {
    console.log('🚀 Setting up mart data...');

    // Clear existing data
    console.log('🧹 Cleaning existing mart data...');
    await query('DELETE FROM mart_products');
    await query('DELETE FROM mart_categories');
    await query('DELETE FROM market_locations');

    // Reset sequences
    await query('ALTER SEQUENCE mart_categories_id_seq RESTART WITH 1');
    await query('ALTER SEQUENCE mart_products_id_seq RESTART WITH 1');
    await query('ALTER SEQUENCE market_locations_id_seq RESTART WITH 1');

    // Create realistic market locations in the same area as restaurants
    console.log('📍 Creating market locations...');
    const locations = [
      {
        name: 'Central Market Hub',
        address: '100 Main Street, Downtown Halabja',
        latitude: 35.17400000,
        longitude: 45.97850000,
        phone: '+964750123456',
        operating_hours: {
          monday: '06:00-23:00',
          tuesday: '06:00-23:00',
          wednesday: '06:00-23:00',
          thursday: '06:00-23:00',
          friday: '06:00-23:00',
          saturday: '06:00-23:00',
          sunday: '08:00-22:00'
        }
      },
      {
        name: 'City Center Mart',
        address: '250 Oak Avenue, City Center',
        latitude: 35.17500000,
        longitude: 45.97950000,
        phone: '+964750123457',
        operating_hours: {
          monday: '07:00-22:00',
          tuesday: '07:00-22:00',
          wednesday: '07:00-22:00',
          thursday: '07:00-22:00',
          friday: '07:00-22:00',
          saturday: '07:00-22:00',
          sunday: '09:00-21:00'
        }
      },
      {
        name: 'Uptown Fresh Market',
        address: '500 Pine Street, Uptown',
        latitude: 35.17300000,
        longitude: 45.97750000,
        phone: '+964750123458',
        operating_hours: {
          monday: '06:30-22:30',
          tuesday: '06:30-22:30',
          wednesday: '06:30-22:30',
          thursday: '06:30-22:30',
          friday: '06:30-22:30',
          saturday: '06:30-22:30',
          sunday: '08:00-21:00'
        }
      },
      {
        name: 'Westside Grocery',
        address: '150 Elm Street, Westside',
        latitude: 35.17600000,
        longitude: 45.97650000,
        phone: '+964750123459',
        operating_hours: {
          monday: '05:30-23:30',
          tuesday: '05:30-23:30',
          wednesday: '05:30-23:30',
          thursday: '05:30-23:30',
          friday: '05:30-23:30',
          saturday: '05:30-23:30',
          sunday: '07:00-22:00'
        }
      }
    ];

    for (const location of locations) {
      await query(
        `INSERT INTO market_locations (name, address, latitude, longitude, phone, operating_hours, is_active) 
         VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [location.name, location.address, location.latitude, location.longitude, 
         location.phone, JSON.stringify(location.operating_hours), true]
      );
    }

    // Create comprehensive categories
    console.log('🏷️ Creating categories...');
    const categories = [
      { name: 'Fresh Fruits', description: 'Fresh seasonal fruits and organic produce', sort_order: 1 },
      { name: 'Vegetables', description: 'Fresh vegetables and leafy greens', sort_order: 2 },
      { name: 'Dairy & Eggs', description: 'Milk, cheese, yogurt, and fresh eggs', sort_order: 3 },
      { name: 'Meat & Poultry', description: 'Fresh meat, chicken, and seafood', sort_order: 4 },
      { name: 'Bakery', description: 'Fresh bread, pastries, and baked goods', sort_order: 5 },
      { name: 'Beverages', description: 'Soft drinks, juices, water, and hot beverages', sort_order: 6 },
      { name: 'Snacks & Sweets', description: 'Chips, chocolates, candies, and snacks', sort_order: 7 },
      { name: 'Pantry Staples', description: 'Rice, pasta, oils, spices, and cooking essentials', sort_order: 8 },
      { name: 'Personal Care', description: 'Toiletries, hygiene products, and cosmetics', sort_order: 9 },
      { name: 'Household Items', description: 'Cleaning supplies, detergents, and home essentials', sort_order: 10 },
      { name: 'Baby Care', description: 'Baby food, diapers, and infant care products', sort_order: 11 },
      { name: 'Frozen Foods', description: 'Frozen vegetables, meals, and ice cream', sort_order: 12 }
    ];

    const categoryIds = {};
    for (const category of categories) {
      const result = await query(
        `INSERT INTO mart_categories (name, description, sort_order, is_active) 
         VALUES ($1, $2, $3, $4) RETURNING id`,
        [category.name, category.description, category.sort_order, true]
      );
      categoryIds[category.name] = result.rows[0].id;
    }

    console.log('🛒 Creating products with images...');
    
    // Products with realistic images from Unsplash
    const products = [
      // Fresh Fruits
      { category: 'Fresh Fruits', name: 'Red Apples', description: 'Fresh crispy red apples', price: 2.99, unit: 'kg', stock: 50, brand: 'Fresh Farm', image: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=400' },
      { category: 'Fresh Fruits', name: 'Bananas', description: 'Ripe yellow bananas', price: 1.99, unit: 'kg', stock: 75, brand: 'Tropical', image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400' },
      { category: 'Fresh Fruits', name: 'Orange Juice Oranges', description: 'Sweet juicy oranges', price: 3.49, unit: 'kg', stock: 40, brand: 'Citrus Fresh', image: 'https://images.unsplash.com/photo-1547514701-42782101795e?w=400' },
      { category: 'Fresh Fruits', name: 'Strawberries', description: 'Fresh sweet strawberries', price: 5.99, unit: 'pack', stock: 25, brand: 'Berry Best', image: 'https://images.unsplash.com/photo-1464965911861-746a04b4bca6?w=400' },
      { category: 'Fresh Fruits', name: 'Grapes', description: 'Seedless green grapes', price: 4.99, unit: 'kg', stock: 30, brand: 'Vine Fresh', image: 'https://images.unsplash.com/photo-1537640538966-79f369143f8f?w=400' },
      
      // Vegetables
      { category: 'Vegetables', name: 'Tomatoes', description: 'Fresh red tomatoes', price: 2.49, unit: 'kg', stock: 60, brand: 'Garden Fresh', image: 'https://images.unsplash.com/photo-1546470427-e26264be0b0d?w=400' },
      { category: 'Vegetables', name: 'Cucumbers', description: 'Crisp green cucumbers', price: 1.99, unit: 'kg', stock: 45, brand: 'Green Valley', image: 'https://images.unsplash.com/photo-1449300079323-02e209d9d3a6?w=400' },
      { category: 'Vegetables', name: 'Carrots', description: 'Fresh orange carrots', price: 1.79, unit: 'kg', stock: 55, brand: 'Root & Co', image: 'https://images.unsplash.com/photo-1445282768818-728615cc910a?w=400' },
      { category: 'Vegetables', name: 'Lettuce', description: 'Fresh green lettuce', price: 1.49, unit: 'piece', stock: 35, brand: 'Leafy Greens', image: 'https://images.unsplash.com/photo-1622206151226-18ca2c9ab4a1?w=400' },
      { category: 'Vegetables', name: 'Bell Peppers', description: 'Colorful bell peppers', price: 3.99, unit: 'kg', stock: 40, brand: 'Rainbow Farms', image: 'https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?w=400' },
      
      // Dairy & Eggs
      { category: 'Dairy & Eggs', name: 'Whole Milk', description: 'Fresh whole milk', price: 3.99, unit: 'l', stock: 80, brand: 'Dairy Best', image: 'https://images.unsplash.com/photo-1550583724-b2692b85b150?w=400' },
      { category: 'Dairy & Eggs', name: 'Greek Yogurt', description: 'Creamy Greek yogurt', price: 4.99, unit: 'pack', stock: 45, brand: 'Mediterranean', image: 'https://images.unsplash.com/photo-1488477181946-6428a0291777?w=400' },
      { category: 'Dairy & Eggs', name: 'Cheddar Cheese', description: 'Aged cheddar cheese', price: 6.99, unit: 'pack', stock: 30, brand: 'Cheese Master', image: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?w=400' },
      { category: 'Dairy & Eggs', name: 'Fresh Eggs', description: 'Farm fresh eggs', price: 4.49, unit: 'dozen', stock: 60, brand: 'Happy Hens', image: 'https://images.unsplash.com/photo-1582722872445-44dc5f7e3c8f?w=400' },
      
      // Beverages
      { category: 'Beverages', name: 'Coca Cola', description: 'Classic Coca Cola', price: 1.99, unit: 'bottle', stock: 100, brand: 'Coca Cola', image: 'https://images.unsplash.com/photo-1561758033-d89a9ad46330?w=400' },
      { category: 'Beverages', name: 'Orange Juice', description: '100% pure orange juice', price: 3.99, unit: 'bottle', stock: 50, brand: 'Pure Squeeze', image: 'https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?w=400' },
      { category: 'Beverages', name: 'Mineral Water', description: 'Natural mineral water', price: 0.99, unit: 'bottle', stock: 200, brand: 'Crystal Clear', image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400' },
      { category: 'Beverages', name: 'Green Tea', description: 'Premium green tea bags', price: 5.99, unit: 'box', stock: 25, brand: 'Tea Garden', image: 'https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=400' },
      
      // Snacks & Sweets
      { category: 'Snacks & Sweets', name: 'Potato Chips', description: 'Crispy potato chips', price: 2.99, unit: 'bag', stock: 75, brand: 'Crispy Crunch', image: 'https://images.unsplash.com/photo-1566478989037-eec170784d0b?w=400' },
      { category: 'Snacks & Sweets', name: 'Chocolate Bar', description: 'Milk chocolate bar', price: 1.99, unit: 'piece', stock: 90, brand: 'Sweet Dreams', image: 'https://images.unsplash.com/photo-1511381939415-e44015466834?w=400' },
      { category: 'Snacks & Sweets', name: 'Mixed Nuts', description: 'Premium mixed nuts', price: 7.99, unit: 'pack', stock: 35, brand: 'Nutty Delight', image: 'https://images.unsplash.com/photo-1508747703725-719777637510?w=400' },
      
      // Pantry Staples
      { category: 'Pantry Staples', name: 'Basmati Rice', description: 'Premium basmati rice', price: 8.99, unit: 'kg', stock: 40, brand: 'Golden Grain', image: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400' },
      { category: 'Pantry Staples', name: 'Olive Oil', description: 'Extra virgin olive oil', price: 12.99, unit: 'bottle', stock: 25, brand: 'Mediterranean Gold', image: 'https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?w=400' },
      { category: 'Pantry Staples', name: 'Pasta', description: 'Italian pasta', price: 2.49, unit: 'pack', stock: 60, brand: 'Pasta Prima', image: 'https://images.unsplash.com/photo-1551892374-ecf8754cf8b0?w=400' },
      
      // Personal Care
      { category: 'Personal Care', name: 'Shampoo', description: 'Moisturizing shampoo', price: 6.99, unit: 'bottle', stock: 40, brand: 'Hair Care Pro', image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400' },
      { category: 'Personal Care', name: 'Toothpaste', description: 'Fluoride toothpaste', price: 3.99, unit: 'tube', stock: 55, brand: 'Bright Smile', image: 'https://images.unsplash.com/photo-1607613009820-a29f7bb81c04?w=400' },
      { category: 'Personal Care', name: 'Body Soap', description: 'Moisturizing body soap', price: 2.99, unit: 'bar', stock: 70, brand: 'Gentle Touch', image: 'https://images.unsplash.com/photo-1584305574647-0cc949a2bb9f?w=400' },
      
      // Household Items
      { category: 'Household Items', name: 'Dish Soap', description: 'Grease-cutting dish soap', price: 3.49, unit: 'bottle', stock: 45, brand: 'Clean Master', image: 'https://images.unsplash.com/photo-1563453392212-326f5e854473?w=400' },
      { category: 'Household Items', name: 'Laundry Detergent', description: 'Concentrated laundry detergent', price: 9.99, unit: 'bottle', stock: 30, brand: 'Fresh Clean', image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400' },
      { category: 'Household Items', name: 'Toilet Paper', description: 'Soft toilet paper', price: 8.99, unit: 'pack', stock: 50, brand: 'Comfort Plus', image: 'https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400' },
      
      // Bakery
      { category: 'Bakery', name: 'White Bread', description: 'Fresh white bread loaf', price: 2.49, unit: 'loaf', stock: 40, brand: 'Baker\'s Best', image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400' },
      { category: 'Bakery', name: 'Croissants', description: 'Buttery croissants', price: 4.99, unit: 'pack', stock: 25, brand: 'French Bakery', image: 'https://images.unsplash.com/photo-1555507036-ab794f4ade0a?w=400' },
      
      // Frozen Foods
      { category: 'Frozen Foods', name: 'Ice Cream', description: 'Vanilla ice cream', price: 5.99, unit: 'tub', stock: 35, brand: 'Creamy Delight', image: 'https://images.unsplash.com/photo-1567206563064-6f60f40a2b57?w=400' },
      { category: 'Frozen Foods', name: 'Frozen Pizza', description: 'Margherita frozen pizza', price: 7.99, unit: 'piece', stock: 20, brand: 'Pizza Express', image: 'https://images.unsplash.com/photo-1513104890138-7c749659a591?w=400' }
    ];

    for (const product of products) {
      await query(
        `INSERT INTO mart_products (category_id, name, description, price, image_url, is_available, stock_quantity, unit, brand) 
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
        [
          categoryIds[product.category],
          product.name,
          product.description,
          product.price,
          product.image,
          true,
          product.stock,
          product.unit,
          product.brand
        ]
      );
    }

    console.log('✅ Mart data setup completed successfully!');
    console.log(`📍 Created ${locations.length} market locations`);
    console.log(`🏷️ Created ${categories.length} categories`);
    console.log(`🛒 Created ${products.length} products with images`);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error setting up mart data:', error);
    process.exit(1);
  }
}

setupMartData();
