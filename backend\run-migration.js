#!/usr/bin/env node

/**
 * Simple migration runner for the discount system
 */

const fs = require('fs');
const path = require('path');
const { query } = require('./config/database');

async function runMigration() {
  try {
    console.log('🚀 Running discount system migration...\n');

    // Read the migration file
    const migrationPath = path.join(__dirname, 'migrations', '001_create_discounts_table.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Execute the migration
    await query(migrationSQL);

    console.log('✅ Migration completed successfully!');
    console.log('📊 Discount system tables created:');
    console.log('   - discounts');
    console.log('   - discount_usage');
    console.log('   - discount_time_restrictions');
    console.log('   - Updated orders table with discount columns');
    console.log('');
    console.log('🎉 Sample discounts have been created for testing!');
    console.log('');
    console.log('🔄 Please restart your backend server and refresh the admin panel.');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Make sure PostgreSQL is running and the database exists');
    } else if (error.code === '42P01') {
      console.log('💡 Some referenced tables might not exist. Check your database schema.');
    }
    
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

runMigration();
