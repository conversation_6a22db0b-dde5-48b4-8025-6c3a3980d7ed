import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:provider/provider.dart';
import 'package:customer/main.dart' as app;
import 'package:customer/providers/cart_provider.dart';
import 'package:customer/providers/auth_provider.dart';
import 'package:customer/models/discount.dart';
import 'package:customer/models/cart.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Discount System Integration Tests', () {
    testWidgets('Complete discount flow - browse, apply, and checkout', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Login flow
      await _performLogin(tester);

      // Navigate to restaurant
      await _navigateToRestaurant(tester);

      // Add items to cart
      await _addItemsToCart(tester);

      // Navigate to cart
      await _navigateToCart(tester);

      // Apply discount
      await _applyDiscount(tester);

      // Verify discount is applied
      await _verifyDiscountApplied(tester);

      // Proceed to checkout
      await _proceedToCheckout(tester);

      // Verify order with discount
      await _verifyOrderWithDiscount(tester);
    });

    testWidgets('Discount code validation flow', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      await _performLogin(tester);
      await _navigateToRestaurant(tester);
      await _addItemsToCart(tester);
      await _navigateToCart(tester);

      // Test invalid discount code
      await _testInvalidDiscountCode(tester);

      // Test valid discount code
      await _testValidDiscountCode(tester);

      // Test expired discount code
      await _testExpiredDiscountCode(tester);

      // Test minimum order requirement
      await _testMinimumOrderRequirement(tester);
    });

    testWidgets('Discount removal and reapplication', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      await _performLogin(tester);
      await _navigateToRestaurant(tester);
      await _addItemsToCart(tester);
      await _navigateToCart(tester);

      // Apply discount
      await _applyDiscount(tester);
      await _verifyDiscountApplied(tester);

      // Remove discount
      await _removeDiscount(tester);
      await _verifyDiscountRemoved(tester);

      // Reapply different discount
      await _applyDifferentDiscount(tester);
      await _verifyDifferentDiscountApplied(tester);
    });

    testWidgets('Restaurant discount banner interaction', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      await _performLogin(tester);
      await _navigateToRestaurant(tester);

      // Verify discount banner is visible
      expect(find.byType(RestaurantDiscountBanner), findsOneWidget);

      // Tap discount banner
      await tester.tap(find.byType(RestaurantDiscountBanner));
      await tester.pumpAndSettle();

      // Verify discount bottom sheet opens
      expect(find.byType(DiscountBottomSheet), findsOneWidget);

      // Select a discount from the sheet
      await tester.tap(find.text('Apply').first);
      await tester.pumpAndSettle();

      // Verify discount is applied to cart
      final cartProvider = tester.widget<ChangeNotifierProvider<CartProvider>>(
        find.byType(ChangeNotifierProvider<CartProvider>)
      ).create(null) as CartProvider;
      
      expect(cartProvider.hasDiscount, isTrue);
    });

    testWidgets('Edge cases and error handling', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      await _performLogin(tester);
      await _navigateToRestaurant(tester);
      await _addItemsToCart(tester);
      await _navigateToCart(tester);

      // Test network error handling
      await _testNetworkErrorHandling(tester);

      // Test concurrent discount application
      await _testConcurrentDiscountApplication(tester);

      // Test cart modification after discount application
      await _testCartModificationAfterDiscount(tester);
    });
  });

  group('Discount UI Component Tests', () {
    testWidgets('DiscountCard displays correctly', (WidgetTester tester) async {
      final discount = Discount(
        id: 1,
        name: 'Test Discount',
        discountType: DiscountType.percentage,
        discountValue: 20,
        scope: DiscountScope.platform,
        minimumOrderAmount: 25,
        usageLimitPerCustomer: 1,
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 30)),
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DiscountCard(
              discount: discount,
              onTap: () {},
            ),
          ),
        ),
      );

      expect(find.text('Test Discount'), findsOneWidget);
      expect(find.text('20% OFF'), findsOneWidget);
      expect(find.text('Apply'), findsOneWidget);
    });

    testWidgets('DiscountCodeInput handles input correctly', (WidgetTester tester) async {
      String? appliedCode;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DiscountCodeInput(
              onApply: (code) {
                appliedCode = code;
              },
            ),
          ),
        ),
      );

      // Enter discount code
      await tester.enterText(find.byType(TextField), 'TEST20');
      await tester.tap(find.text('Apply'));
      await tester.pumpAndSettle();

      expect(appliedCode, equals('TEST20'));
    });
  });

  group('Performance Tests', () {
    testWidgets('Discount calculation performance', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      await _performLogin(tester);

      // Create a cart with many items
      final cartProvider = Provider.of<CartProvider>(
        tester.element(find.byType(MaterialApp)),
        listen: false,
      );

      // Add 50 items to cart
      for (int i = 0; i < 50; i++) {
        cartProvider.addItem(
          menuItemId: i,
          name: 'Item $i',
          price: 10.0 + i,
          quantity: 1,
        );
      }

      // Measure discount calculation time
      final stopwatch = Stopwatch()..start();
      
      final discount = Discount(
        id: 1,
        name: 'Performance Test Discount',
        discountType: DiscountType.percentage,
        discountValue: 15,
        scope: DiscountScope.platform,
        minimumOrderAmount: 0,
        usageLimitPerCustomer: 1,
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 30)),
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      cartProvider.applyDiscountDirectly(discount);
      
      stopwatch.stop();

      // Discount calculation should complete within 100ms
      expect(stopwatch.elapsedMilliseconds, lessThan(100));
    });
  });
}

// Helper functions for integration tests

Future<void> _performLogin(WidgetTester tester) async {
  // Navigate to login screen if not already logged in
  if (find.text('Login').evaluate().isNotEmpty) {
    await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
    await tester.enterText(find.byKey(const Key('password_field')), 'password123');
    await tester.tap(find.byKey(const Key('login_button')));
    await tester.pumpAndSettle();
  }
}

Future<void> _navigateToRestaurant(WidgetTester tester) async {
  // Find and tap on a restaurant
  await tester.tap(find.byType(RestaurantCard).first);
  await tester.pumpAndSettle();
}

Future<void> _addItemsToCart(WidgetTester tester) async {
  // Add multiple items to cart
  final addButtons = find.byIcon(Icons.add);
  for (int i = 0; i < 3 && i < addButtons.evaluate().length; i++) {
    await tester.tap(addButtons.at(i));
    await tester.pumpAndSettle();
  }
}

Future<void> _navigateToCart(WidgetTester tester) async {
  // Navigate to cart screen
  await tester.tap(find.byIcon(Icons.shopping_cart));
  await tester.pumpAndSettle();
}

Future<void> _applyDiscount(WidgetTester tester) async {
  // Tap on "View Offers" button
  await tester.tap(find.text('View Offers'));
  await tester.pumpAndSettle();

  // Select first available discount
  await tester.tap(find.text('Apply').first);
  await tester.pumpAndSettle();
}

Future<void> _verifyDiscountApplied(WidgetTester tester) async {
  // Verify discount section shows applied discount
  expect(find.text('Discount Applied'), findsOneWidget);
  expect(find.textContaining('You saved'), findsOneWidget);
}

Future<void> _proceedToCheckout(WidgetTester tester) async {
  // Tap checkout button
  await tester.tap(find.text('Proceed to Checkout'));
  await tester.pumpAndSettle();
}

Future<void> _verifyOrderWithDiscount(WidgetTester tester) async {
  // Verify order summary includes discount
  expect(find.textContaining('Discount'), findsOneWidget);
  expect(find.textContaining('-\$'), findsOneWidget);
}

Future<void> _testInvalidDiscountCode(WidgetTester tester) async {
  await tester.enterText(find.byType(TextField), 'INVALID');
  await tester.tap(find.text('Apply'));
  await tester.pumpAndSettle();

  expect(find.text('Invalid or expired discount code'), findsOneWidget);
}

Future<void> _testValidDiscountCode(WidgetTester tester) async {
  await tester.enterText(find.byType(TextField), 'VALID20');
  await tester.tap(find.text('Apply'));
  await tester.pumpAndSettle();

  expect(find.text('Discount applied successfully!'), findsOneWidget);
}

Future<void> _testExpiredDiscountCode(WidgetTester tester) async {
  await tester.enterText(find.byType(TextField), 'EXPIRED');
  await tester.tap(find.text('Apply'));
  await tester.pumpAndSettle();

  expect(find.text('This discount has expired'), findsOneWidget);
}

Future<void> _testMinimumOrderRequirement(WidgetTester tester) async {
  await tester.enterText(find.byType(TextField), 'MIN50');
  await tester.tap(find.text('Apply'));
  await tester.pumpAndSettle();

  expect(find.textContaining('Minimum order amount'), findsOneWidget);
}

Future<void> _removeDiscount(WidgetTester tester) async {
  await tester.tap(find.text('Remove'));
  await tester.pumpAndSettle();
}

Future<void> _verifyDiscountRemoved(WidgetTester tester) async {
  expect(find.text('Discount Applied'), findsNothing);
}

Future<void> _applyDifferentDiscount(WidgetTester tester) async {
  await tester.tap(find.text('View Offers'));
  await tester.pumpAndSettle();
  
  // Select a different discount
  await tester.tap(find.text('Apply').at(1));
  await tester.pumpAndSettle();
}

Future<void> _verifyDifferentDiscountApplied(WidgetTester tester) async {
  expect(find.text('Discount Applied'), findsOneWidget);
}

Future<void> _testNetworkErrorHandling(WidgetTester tester) async {
  // Simulate network error by entering a code that triggers network failure
  await tester.enterText(find.byType(TextField), 'NETWORK_ERROR');
  await tester.tap(find.text('Apply'));
  await tester.pumpAndSettle();

  expect(find.text('Network error occurred'), findsOneWidget);
}

Future<void> _testConcurrentDiscountApplication(WidgetTester tester) async {
  // Rapidly tap apply button multiple times
  for (int i = 0; i < 5; i++) {
    await tester.tap(find.text('Apply').first);
    await tester.pump(const Duration(milliseconds: 100));
  }
  await tester.pumpAndSettle();

  // Should only apply one discount
  expect(find.text('Discount Applied'), findsOneWidget);
}

Future<void> _testCartModificationAfterDiscount(WidgetTester tester) async {
  // Apply discount first
  await _applyDiscount(tester);
  await _verifyDiscountApplied(tester);

  // Modify cart by adding more items
  await tester.tap(find.byIcon(Icons.add).first);
  await tester.pumpAndSettle();

  // Verify discount is recalculated or removed if no longer valid
  // This depends on business logic implementation
}
