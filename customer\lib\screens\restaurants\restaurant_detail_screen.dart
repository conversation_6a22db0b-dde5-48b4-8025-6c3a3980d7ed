import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../providers/restaurant_provider.dart';
import '../../providers/cart_provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/favorites_provider.dart';
import '../../models/restaurant.dart';
import '../../models/discount.dart';
import '../../widgets/rating_widgets.dart';
import '../../widgets/discount_badge.dart';
import '../../widgets/discount_bottom_sheet.dart';
import '../cart/cart_screen.dart';
import 'restaurant_location_map_screen.dart';

class RestaurantDetailScreen extends StatefulWidget {
  final Restaurant restaurant;
  final bool isGuestMode;

  const RestaurantDetailScreen({
    Key? key,
    required this.restaurant,
    this.isGuestMode = false,
  }) : super(key: key);

  @override
  State<RestaurantDetailScreen> createState() => _RestaurantDetailScreenState();
}

class _RestaurantDetailScreenState extends State<RestaurantDetailScreen> {
  List<Discount> _availableDiscounts = [];
  bool _isLoadingDiscounts = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<RestaurantProvider>(
        context,
        listen: false,
      ).loadRestaurantDetails(
        widget.restaurant.id,
        isGuest: widget.isGuestMode,
      );
      _loadAvailableDiscounts();
    });
  }

  Future<void> _loadAvailableDiscounts() async {
    if (widget.isGuestMode) return;

    setState(() {
      _isLoadingDiscounts = true;
    });

    try {
      final cartProvider = Provider.of<CartProvider>(context, listen: false);
      final discounts = await cartProvider.getAvailableDiscounts();

      // Filter discounts for this restaurant
      final restaurantDiscounts = discounts
          .where(
            (discount) =>
                discount.scope == DiscountScope.platform ||
                (discount.scope == DiscountScope.restaurant &&
                    discount.restaurantId == widget.restaurant.id) ||
                (discount.scope == DiscountScope.menuItem &&
                    discount.restaurantId == widget.restaurant.id),
          )
          .toList();

      if (mounted) {
        setState(() {
          _availableDiscounts = restaurantDiscounts;
        });
      }
    } catch (e) {
      print('Failed to load discounts: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingDiscounts = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Consumer<RestaurantProvider>(
        builder: (context, provider, child) {
          return CustomScrollView(
            slivers: [
              // App Bar with Restaurant Info
              SliverAppBar(
                expandedHeight: 200,
                pinned: true,
                backgroundColor: Colors.orange[600],
                foregroundColor: Colors.white,
                actions: [
                  if (widget.isGuestMode)
                    TextButton(
                      onPressed: () {
                        Navigator.pushNamed(context, '/login');
                      },
                      child: const Text(
                        'Login',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  Consumer<CartProvider>(
                    builder: (context, cartProvider, child) {
                      return Stack(
                        children: [
                          IconButton(
                            onPressed: () {
                              if (widget.isGuestMode) {
                                _showLoginRequiredDialog(context, 'view_cart');
                                return;
                              }
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) => const CartScreen(),
                                ),
                              );
                            },
                            icon: const Icon(Icons.shopping_cart),
                          ),
                          if (cartProvider.itemCount > 0)
                            Positioned(
                              right: 8,
                              top: 8,
                              child: Container(
                                padding: const EdgeInsets.all(2),
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                constraints: const BoxConstraints(
                                  minWidth: 16,
                                  minHeight: 16,
                                ),
                                child: Text(
                                  '${cartProvider.itemCount}',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                        ],
                      );
                    },
                  ),
                ],
                flexibleSpace: FlexibleSpaceBar(
                  title: Text(
                    widget.restaurant.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          offset: Offset(0, 1),
                          blurRadius: 3,
                          color: Colors.black26,
                        ),
                      ],
                    ),
                  ),
                  background: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [Colors.orange[400]!, Colors.orange[600]!],
                      ),
                    ),
                    child: Stack(
                      children: [
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: Container(
                            height: 60,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Colors.transparent,
                                  Colors.black.withValues(alpha: 0.7),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Restaurant Details
              SliverToBoxAdapter(
                child: Container(
                  color: Colors.white,
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.restaurant.cuisineType,
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.orange[600],
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  widget.restaurant.description,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: widget.restaurant.isOpen
                                  ? Colors.green[100]
                                  : Colors.red[100],
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              widget.restaurant.isOpen ? 'Open Now' : 'Closed',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: widget.restaurant.isOpen
                                    ? Colors.green[700]
                                    : Colors.red[700],
                              ),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      Row(
                        children: [
                          _buildInfoChip(
                            Icons.star,
                            '${widget.restaurant.rating.toStringAsFixed(1)} Rating',
                            Colors.amber[600]!,
                          ),
                          const SizedBox(width: 12),
                          _buildInfoChip(
                            Icons.shopping_bag_outlined,
                            '${widget.restaurant.totalOrders} Orders',
                            Colors.blue[600]!,
                          ),
                          if (widget.restaurant.distance != null) ...[
                            const SizedBox(width: 12),
                            _buildInfoChip(
                              Icons.location_on_outlined,
                              '${widget.restaurant.distance} km',
                              Colors.green[600]!,
                            ),
                          ],
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Location section with map
                      _buildLocationSection(),
                    ],
                  ),
                ),
              ),

              // Discount Banner
              if (_availableDiscounts.isNotEmpty && !widget.isGuestMode)
                SliverToBoxAdapter(
                  child: RestaurantDiscountBanner(
                    restaurantDiscounts: _availableDiscounts
                        .where(
                          (d) =>
                              d.scope == DiscountScope.restaurant ||
                              d.scope == DiscountScope.platform,
                        )
                        .toList(),
                    onTap: () {
                      showDiscountBottomSheet(context, _availableDiscounts);
                    },
                  ),
                ),

              // Menu Content
              if (provider.isLoading)
                const SliverToBoxAdapter(
                  child: Center(
                    child: Padding(
                      padding: EdgeInsets.all(32),
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.orange,
                        ),
                      ),
                    ),
                  ),
                )
              else if (provider.error != null)
                SliverToBoxAdapter(
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.all(32),
                      child: Column(
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Error loading menu',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            provider.error!,
                            textAlign: TextAlign.center,
                            style: TextStyle(color: Colors.grey[500]),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              provider.loadRestaurantDetails(
                                widget.restaurant.id,
                                isGuest: widget.isGuestMode,
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange[600],
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              else if (provider.selectedRestaurant?.menu.isEmpty ?? true)
                SliverToBoxAdapter(
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.all(32),
                      child: Column(
                        children: [
                          Icon(
                            Icons.restaurant_menu,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No menu available',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'This restaurant hasn\'t added their menu yet',
                            textAlign: TextAlign.center,
                            style: TextStyle(color: Colors.grey[500]),
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              else
                // Menu Categories
                SliverList(
                  delegate: SliverChildBuilderDelegate((context, index) {
                    final category = provider.selectedRestaurant!.menu[index];
                    return MenuCategorySection(
                      category: category,
                      restaurant: widget.restaurant,
                      isGuestMode: widget.isGuestMode,
                    );
                  }, childCount: provider.selectedRestaurant?.menu.length ?? 0),
                ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.location_on, size: 20, color: Colors.orange[600]),
            const SizedBox(width: 8),
            const Text(
              'Restaurant Location',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Address
        Row(
          children: [
            Icon(Icons.location_on_outlined, size: 16, color: Colors.grey[500]),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                widget.restaurant.address,
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // Map preview and actions
        Container(
          height: 150,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: GoogleMap(
              initialCameraPosition: CameraPosition(
                target: LatLng(
                  widget.restaurant.location.latitude,
                  widget.restaurant.location.longitude,
                ),
                zoom: 15.0,
              ),
              markers: {
                Marker(
                  markerId: const MarkerId('restaurant'),
                  position: LatLng(
                    widget.restaurant.location.latitude,
                    widget.restaurant.location.longitude,
                  ),
                  infoWindow: InfoWindow(
                    title: widget.restaurant.name,
                    snippet: widget.restaurant.address,
                  ),
                  icon: BitmapDescriptor.defaultMarkerWithHue(
                    BitmapDescriptor.hueOrange,
                  ),
                ),
              },
              onTap: (_) => _showFullScreenMap(),
              zoomControlsEnabled: false,
              scrollGesturesEnabled: false,
              rotateGesturesEnabled: false,
              tiltGesturesEnabled: false,
            ),
          ),
        ),

        const SizedBox(height: 12),

        // Action buttons
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _openInMaps,
                icon: const Icon(Icons.directions, size: 18),
                label: const Text('Get Directions'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.orange[600],
                  side: BorderSide(color: Colors.orange[600]!),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _showFullScreenMap,
                icon: const Icon(Icons.map, size: 18),
                label: const Text('View Map'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange[600],
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _openInMaps() async {
    final url =
        'https://www.google.com/maps/dir/?api=1&destination=${widget.restaurant.location.latitude},${widget.restaurant.location.longitude}';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    }
  }

  void _showFullScreenMap() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) =>
            RestaurantLocationMapScreen(restaurant: widget.restaurant),
      ),
    );
  }

  void _showLoginRequiredDialog(BuildContext context, String action) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Login Required'),
          content: const Text(
            'You need to create an account or login to add items to your cart and place orders.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Set pending action and navigate to login
                final authProvider = Provider.of<AuthProvider>(
                  context,
                  listen: false,
                );
                authProvider.setPendingAction(action);
                Navigator.pushNamed(context, '/login');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange[600],
                foregroundColor: Colors.white,
              ),
              child: const Text('Login'),
            ),
          ],
        );
      },
    );
  }
}

class MenuCategorySection extends StatelessWidget {
  final MenuCategory category;
  final Restaurant restaurant;
  final bool isGuestMode;

  const MenuCategorySection({
    Key? key,
    required this.category,
    required this.restaurant,
    this.isGuestMode = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Category Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  category.name,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (category.description != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    category.description!,
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                ],
                const SizedBox(height: 4),
                Text(
                  '${category.items.length} items',
                  style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                ),
              ],
            ),
          ),

          // Menu Items
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: category.items.length,
            separatorBuilder: (context, index) =>
                Divider(height: 1, color: Colors.grey[200]),
            itemBuilder: (context, index) {
              final item = category.items[index];
              return MenuItemCard(
                item: item,
                restaurant: restaurant,
                isGuestMode: isGuestMode,
              );
            },
          ),
        ],
      ),
    );
  }
}

class MenuItemCard extends StatelessWidget {
  final MenuItem item;
  final Restaurant restaurant;
  final bool isGuestMode;

  const MenuItemCard({
    Key? key,
    required this.item,
    required this.restaurant,
    this.isGuestMode = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Item Image
          if (item.imageUrl != null && item.imageUrl!.isNotEmpty)
            Container(
              width: 80,
              height: 80,
              margin: const EdgeInsets.only(right: 16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.grey[200],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  item.imageUrl!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey[200],
                      child: Icon(
                        Icons.restaurant,
                        color: Colors.grey[400],
                        size: 32,
                      ),
                    );
                  },
                ),
              ),
            ),

          // Item Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        item.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    // Favorite toggle button
                    if (!isGuestMode)
                      Consumer<FavoritesProvider>(
                        builder: (context, favoritesProvider, child) {
                          final isFavorited = favoritesProvider.isFavorited(
                            item.id,
                          );
                          return IconButton(
                            onPressed: () async {
                              await favoritesProvider.toggleFavorite(
                                item,
                                restaurant.id,
                                restaurant.name,
                                restaurant.cuisineType,
                              );
                            },
                            icon: Icon(
                              isFavorited
                                  ? Icons.favorite
                                  : Icons.favorite_border,
                              color: isFavorited
                                  ? Colors.red[600]
                                  : Colors.grey[400],
                              size: 20,
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 32,
                              minHeight: 32,
                            ),
                            padding: EdgeInsets.zero,
                            tooltip: isFavorited
                                ? 'Remove from favorites'
                                : 'Add to favorites',
                          );
                        },
                      ),
                    if (!item.isAvailable)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red[100],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Unavailable',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: Colors.red[700],
                          ),
                        ),
                      ),
                  ],
                ),

                if (item.description != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    item.description!,
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],

                const SizedBox(height: 8),

                Row(
                  children: [
                    Text(
                      '\$${item.price.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange[600],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Icon(Icons.access_time, size: 14, color: Colors.grey[500]),
                    const SizedBox(width: 4),
                    Text(
                      '${item.preparationTime} min',
                      style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                    ),
                  ],
                ),

                // Rating display
                if (item.totalRatings > 0) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      DisplayStarRating(
                        rating: item.averageRating,
                        size: 14,
                        showRatingText: true,
                        textStyle: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '(${item.totalRatings})',
                        style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),

          // Add to Cart Button
          Container(
            margin: const EdgeInsets.only(left: 16),
            child: ElevatedButton(
              onPressed: item.isAvailable ? () => _addToCart(context) : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: const Text(
                'Add',
                style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _addToCart(BuildContext context) {
    // Check if user is in guest mode
    if (isGuestMode) {
      _showLoginRequiredDialog(context, 'add_to_cart');
      return;
    }

    final cartProvider = Provider.of<CartProvider>(context, listen: false);

    // Check if cart has items from a different restaurant
    if (cartProvider.isFromDifferentRestaurant(restaurant.id)) {
      _showDifferentRestaurantDialog(context, cartProvider);
      return;
    }

    // Add item to cart
    cartProvider.addToCart(item, restaurant.id, restaurant.name);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${item.name} added to cart!'),
        backgroundColor: Colors.green[600],
        duration: const Duration(seconds: 2),
        action: SnackBarAction(
          label: 'View Cart',
          textColor: Colors.white,
          onPressed: () {
            Navigator.of(
              context,
            ).push(MaterialPageRoute(builder: (context) => const CartScreen()));
          },
        ),
      ),
    );
  }

  void _showDifferentRestaurantDialog(
    BuildContext context,
    CartProvider cartProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Different Restaurant'),
        content: const Text(
          'Your cart contains items from a different restaurant. '
          'Would you like to clear your cart and add this item?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              cartProvider.clearCart();
              cartProvider.addToCart(item, restaurant.id, restaurant.name);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Cart cleared and ${item.name} added!'),
                  backgroundColor: Colors.green[600],
                ),
              );
            },
            child: const Text('Clear Cart & Add'),
          ),
        ],
      ),
    );
  }

  void _showLoginRequiredDialog(BuildContext context, String action) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Login Required'),
          content: const Text(
            'You need to create an account or login to add items to your cart and place orders.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Set pending action and navigate to login
                final authProvider = Provider.of<AuthProvider>(
                  context,
                  listen: false,
                );
                authProvider.setPendingAction(action);
                Navigator.pushNamed(context, '/login');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange[600],
                foregroundColor: Colors.white,
              ),
              child: const Text('Login'),
            ),
          ],
        );
      },
    );
  }
}
