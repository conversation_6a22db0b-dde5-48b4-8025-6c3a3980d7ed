import 'restaurant.dart';

class FavoriteItem {
  final int id;
  final int menuItemId;
  final int restaurantId;
  final String name;
  final String? description;
  final double price;
  final String? imageUrl;
  final int preparationTime;
  final bool isAvailable;
  final double averageRating;
  final int totalRatings;
  final String restaurantName;
  final String cuisineType;
  final DateTime createdAt;

  FavoriteItem({
    required this.id,
    required this.menuItemId,
    required this.restaurantId,
    required this.name,
    this.description,
    required this.price,
    this.imageUrl,
    required this.preparationTime,
    required this.isAvailable,
    this.averageRating = 0.0,
    this.totalRatings = 0,
    required this.restaurantName,
    required this.cuisineType,
    required this.createdAt,
  });

  factory FavoriteItem.fromJson(Map<String, dynamic> json) {
    return FavoriteItem(
      id: json['id'],
      menuItemId: json['menuItemId'],
      restaurantId: json['restaurantId'],
      name: json['name'],
      description: json['description'],
      price: (json['price'] as num).toDouble(),
      imageUrl: json['imageUrl'],
      preparationTime: json['preparationTime'],
      isAvailable: json['isAvailable'] ?? true,
      averageRating: (json['averageRating'] ?? 0).toDouble(),
      totalRatings: json['totalRatings'] ?? 0,
      restaurantName: json['restaurantName'],
      cuisineType: json['cuisineType'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'menuItemId': menuItemId,
      'restaurantId': restaurantId,
      'name': name,
      'description': description,
      'price': price,
      'imageUrl': imageUrl,
      'preparationTime': preparationTime,
      'isAvailable': isAvailable,
      'averageRating': averageRating,
      'totalRatings': totalRatings,
      'restaurantName': restaurantName,
      'cuisineType': cuisineType,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // Convert to MenuItem for cart operations
  MenuItem toMenuItem() {
    return MenuItem(
      id: menuItemId,
      name: name,
      description: description,
      price: price,
      imageUrl: imageUrl,
      preparationTime: preparationTime,
      isAvailable: isAvailable,
      averageRating: averageRating,
      totalRatings: totalRatings,
      createdAt: createdAt,
    );
  }

  // Create from MenuItem and restaurant info
  factory FavoriteItem.fromMenuItem({
    required int favoriteId,
    required MenuItem menuItem,
    required int restaurantId,
    required String restaurantName,
    required String cuisineType,
    required DateTime createdAt,
  }) {
    return FavoriteItem(
      id: favoriteId,
      menuItemId: menuItem.id,
      restaurantId: restaurantId,
      name: menuItem.name,
      description: menuItem.description,
      price: menuItem.price,
      imageUrl: menuItem.imageUrl,
      preparationTime: menuItem.preparationTime,
      isAvailable: menuItem.isAvailable,
      averageRating: menuItem.averageRating,
      totalRatings: menuItem.totalRatings,
      restaurantName: restaurantName,
      cuisineType: cuisineType,
      createdAt: createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FavoriteItem && other.menuItemId == menuItemId;
  }

  @override
  int get hashCode => menuItemId.hashCode;
}

class FavoritesList {
  final List<FavoriteItem> items;
  final int totalCount;

  FavoritesList({
    this.items = const [],
    this.totalCount = 0,
  });

  bool get isEmpty => items.isEmpty;
  bool get isNotEmpty => items.isNotEmpty;

  // Check if a menu item is favorited
  bool isFavorited(int menuItemId) {
    return items.any((item) => item.menuItemId == menuItemId);
  }

  // Get favorite item by menu item ID
  FavoriteItem? getFavoriteByMenuItemId(int menuItemId) {
    try {
      return items.firstWhere((item) => item.menuItemId == menuItemId);
    } catch (e) {
      return null;
    }
  }

  // Group favorites by restaurant
  Map<String, List<FavoriteItem>> groupByRestaurant() {
    final Map<String, List<FavoriteItem>> grouped = {};
    
    for (final item in items) {
      if (!grouped.containsKey(item.restaurantName)) {
        grouped[item.restaurantName] = [];
      }
      grouped[item.restaurantName]!.add(item);
    }
    
    return grouped;
  }

  factory FavoritesList.fromJson(Map<String, dynamic> json) {
    return FavoritesList(
      items: (json['favorites'] as List)
          .map((item) => FavoriteItem.fromJson(item))
          .toList(),
      totalCount: json['totalCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'favorites': items.map((item) => item.toJson()).toList(),
      'totalCount': totalCount,
    };
  }

  FavoritesList copyWith({
    List<FavoriteItem>? items,
    int? totalCount,
  }) {
    return FavoritesList(
      items: items ?? this.items,
      totalCount: totalCount ?? this.totalCount,
    );
  }
}
