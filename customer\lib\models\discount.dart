import 'package:flutter/material.dart';
import 'cart.dart';

class Discount {
  final int id;
  final String? code;
  final String name;
  final String? description;
  final DiscountType discountType;
  final double discountValue;
  final DiscountScope scope;
  final int? restaurantId;
  final String? restaurantName;
  final int? menuItemId;
  final String? menuItemName;
  final double minimumOrderAmount;
  final double? maximumDiscountAmount;
  final int? usageLimit;
  final int usageLimitPerCustomer;
  final int? customerUsageCount;
  final int? totalUsageCount;
  final DateTime startDate;
  final DateTime endDate;
  final bool isActive;
  final List<TimeRestriction> timeRestrictions;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Discount({
    required this.id,
    this.code,
    required this.name,
    this.description,
    required this.discountType,
    required this.discountValue,
    required this.scope,
    this.restaurantId,
    this.restaurantName,
    this.menuItemId,
    this.menuItemName,
    this.minimumOrderAmount = 0.0,
    this.maximumDiscountAmount,
    this.usageLimit,
    this.usageLimitPerCustomer = 1,
    this.customerUsageCount,
    this.totalUsageCount,
    required this.startDate,
    required this.endDate,
    this.isActive = true,
    this.timeRestrictions = const [],
    required this.createdAt,
    this.updatedAt,
  });

  factory Discount.fromJson(Map<String, dynamic> json) {
    return Discount(
      id: json['id'] ?? 0,
      code: json['code'],
      name: json['name'] ?? 'Unnamed Discount',
      description: json['description'],
      discountType: DiscountType.fromString(
        json['discountType'] ?? 'percentage',
      ),
      discountValue: ((json['discountValue'] ?? 0) as num).toDouble(),
      scope: DiscountScope.fromString(json['scope'] ?? 'platform'),
      restaurantId: json['restaurantId'],
      restaurantName: json['restaurantName'],
      menuItemId: json['menuItemId'],
      menuItemName: json['menuItemName'],
      minimumOrderAmount: ((json['minimumOrderAmount'] ?? 0) as num).toDouble(),
      maximumDiscountAmount: json['maximumDiscountAmount']?.toDouble(),
      usageLimit: json['usageLimit'],
      usageLimitPerCustomer: json['usageLimitPerCustomer'] ?? 1,
      customerUsageCount: json['customerUsageCount'],
      totalUsageCount: json['totalUsageCount'],
      startDate: _parseDateTime(json['startDate']) ?? DateTime.now(),
      endDate:
          _parseDateTime(json['endDate']) ??
          DateTime.now().add(const Duration(days: 30)),
      isActive: json['isActive'] ?? true,
      timeRestrictions:
          (json['timeRestrictions'] as List<dynamic>?)
              ?.map((tr) => TimeRestriction.fromJson(tr))
              .toList() ??
          [],
      createdAt: _parseDateTime(json['createdAt']) ?? DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? _parseDateTime(json['updatedAt'])
          : null,
    );
  }

  static DateTime? _parseDateTime(dynamic dateString) {
    if (dateString == null) return null;
    try {
      return DateTime.parse(dateString.toString());
    } catch (e) {
      // In production, this would use a proper logging framework
      // For now, we'll silently handle the error
      return null;
    }
  }

  // Factory method for creating Discount from validation response (limited fields)
  factory Discount._fromValidationResponse(Map<String, dynamic> json) {
    final now = DateTime.now();
    return Discount(
      id: json['id'] ?? 0,
      code: json['code'],
      name: json['name'] ?? 'Discount',
      description: json['description'],
      discountType: DiscountType.fromString(
        json['discountType'] ?? 'percentage',
      ),
      discountValue: ((json['discountValue'] ?? 0) as num).toDouble(),
      scope: DiscountScope.fromString(json['scope'] ?? 'platform'),
      restaurantId: json['restaurantId'],
      restaurantName: json['restaurantName'],
      menuItemId: json['menuItemId'],
      menuItemName: json['menuItemName'],
      minimumOrderAmount: ((json['minimumOrderAmount'] ?? 0) as num).toDouble(),
      maximumDiscountAmount: json['maximumDiscountAmount']?.toDouble(),
      usageLimit: json['usageLimit'],
      usageLimitPerCustomer: json['usageLimitPerCustomer'] ?? 1,
      customerUsageCount: json['customerUsageCount'],
      totalUsageCount: json['totalUsageCount'],
      startDate:
          now, // Default to now since validation response doesn't include these
      endDate: now.add(const Duration(days: 30)), // Default end date
      isActive: json['isActive'] ?? true,
      timeRestrictions: const [], // Empty for validation response
      createdAt: now, // Default to now
      updatedAt: null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'description': description,
      'discountType': discountType.value,
      'discountValue': discountValue,
      'scope': scope.value,
      'restaurantId': restaurantId,
      'restaurantName': restaurantName,
      'menuItemId': menuItemId,
      'menuItemName': menuItemName,
      'minimumOrderAmount': minimumOrderAmount,
      'maximumDiscountAmount': maximumDiscountAmount,
      'usageLimit': usageLimit,
      'usageLimitPerCustomer': usageLimitPerCustomer,
      'customerUsageCount': customerUsageCount,
      'totalUsageCount': totalUsageCount,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'isActive': isActive,
      'timeRestrictions': timeRestrictions.map((tr) => tr.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  // Check if discount is currently valid
  bool get isCurrentlyValid {
    final now = DateTime.now();
    return isActive &&
        now.isAfter(startDate) &&
        now.isBefore(endDate) &&
        (usageLimit == null || (totalUsageCount ?? 0) < usageLimit!) &&
        (customerUsageCount ?? 0) < usageLimitPerCustomer;
  }

  // Check if discount is valid for current time restrictions
  bool get isValidForCurrentTime {
    if (timeRestrictions.isEmpty) return true;

    final now = DateTime.now();
    final currentDay = now.weekday % 7; // Convert to 0-6 format (0 = Sunday)
    final currentTime = TimeOfDay.fromDateTime(now);

    return timeRestrictions.any(
      (restriction) =>
          restriction.dayOfWeek == currentDay &&
          _isTimeInRange(
            currentTime,
            restriction.startTime,
            restriction.endTime,
          ),
    );
  }

  bool _isTimeInRange(TimeOfDay current, TimeOfDay start, TimeOfDay end) {
    final currentMinutes = current.hour * 60 + current.minute;
    final startMinutes = start.hour * 60 + start.minute;
    final endMinutes = end.hour * 60 + end.minute;

    if (startMinutes <= endMinutes) {
      // Same day range
      return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
    } else {
      // Crosses midnight
      return currentMinutes >= startMinutes || currentMinutes <= endMinutes;
    }
  }

  // Calculate discount amount for given subtotal and items
  double calculateDiscountAmount(double subtotal, List<CartItem>? items) {
    double discountAmount = 0.0;

    if (discountType == DiscountType.percentage) {
      if (scope == DiscountScope.menuItem &&
          items != null &&
          menuItemId != null) {
        // Apply percentage to specific menu items only
        final applicableItems = items.where(
          (item) => item.menuItemId == menuItemId,
        );
        final applicableSubtotal = applicableItems.fold(
          0.0,
          (sum, item) => sum + item.totalPrice,
        );
        discountAmount = applicableSubtotal * (discountValue / 100);
      } else {
        // Apply to entire subtotal
        discountAmount = subtotal * (discountValue / 100);
      }
    } else if (discountType == DiscountType.fixedAmount) {
      discountAmount = discountValue;
    } else if (discountType == DiscountType.buyOneGetOne &&
        items != null &&
        menuItemId != null) {
      // BOGO logic - for every 2 items, discount the price of 1
      final applicableItems = items.where(
        (item) => item.menuItemId == menuItemId,
      );
      final totalQuantity = applicableItems.fold(
        0,
        (sum, item) => sum + item.quantity,
      );
      final freeItems = totalQuantity ~/ 2; // Integer division
      if (applicableItems.isNotEmpty) {
        final itemPrice = applicableItems.first.price;
        discountAmount = freeItems * itemPrice;
      }
    }

    // Apply maximum discount limit
    if (maximumDiscountAmount != null &&
        discountAmount > maximumDiscountAmount!) {
      discountAmount = maximumDiscountAmount!;
    }

    // Ensure discount doesn't exceed subtotal
    return discountAmount > subtotal ? subtotal : discountAmount;
  }

  // Get display text for discount
  String get displayText {
    try {
      switch (discountType) {
        case DiscountType.percentage:
          return '${discountValue.toInt()}% OFF';
        case DiscountType.fixedAmount:
          return '\$${discountValue.toStringAsFixed(2)} OFF';
        case DiscountType.buyOneGetOne:
          return 'Buy 1 Get 1 FREE';
      }
    } catch (e) {
      return 'Discount Available';
    }
  }

  // Get scope display text
  String get scopeDisplayText {
    switch (scope) {
      case DiscountScope.platform:
        return 'Platform-wide';
      case DiscountScope.restaurant:
        return restaurantName ?? 'Restaurant';
      case DiscountScope.menuItem:
        return menuItemName ?? 'Menu Item';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Discount && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum DiscountType {
  percentage('percentage'),
  fixedAmount('fixed_amount'),
  buyOneGetOne('buy_one_get_one');

  const DiscountType(this.value);
  final String value;

  static DiscountType fromString(String value) {
    return DiscountType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => DiscountType.percentage,
    );
  }
}

enum DiscountScope {
  platform('platform'),
  restaurant('restaurant'),
  menuItem('menu_item');

  const DiscountScope(this.value);
  final String value;

  static DiscountScope fromString(String value) {
    return DiscountScope.values.firstWhere(
      (scope) => scope.value == value,
      orElse: () => DiscountScope.platform,
    );
  }
}

class TimeRestriction {
  final int dayOfWeek; // 0 = Sunday, 6 = Saturday
  final TimeOfDay startTime;
  final TimeOfDay endTime;

  TimeRestriction({
    required this.dayOfWeek,
    required this.startTime,
    required this.endTime,
  });

  factory TimeRestriction.fromJson(Map<String, dynamic> json) {
    return TimeRestriction(
      dayOfWeek: json['dayOfWeek'],
      startTime: _parseTimeOfDay(json['startTime']),
      endTime: _parseTimeOfDay(json['endTime']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dayOfWeek': dayOfWeek,
      'startTime': _formatTimeOfDay(startTime),
      'endTime': _formatTimeOfDay(endTime),
    };
  }

  static TimeOfDay _parseTimeOfDay(String timeString) {
    final parts = timeString.split(':');
    return TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]));
  }

  static String _formatTimeOfDay(TimeOfDay time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  String get dayName {
    const days = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ];
    return days[dayOfWeek];
  }
}

class DiscountValidationResult {
  final bool isValid;
  final String? errorMessage;
  final Discount? discount;
  final double? discountAmount;
  final double? finalAmount;
  final double? minimumAmount;

  DiscountValidationResult({
    required this.isValid,
    this.errorMessage,
    this.discount,
    this.discountAmount,
    this.finalAmount,
    this.minimumAmount,
  });

  factory DiscountValidationResult.fromJson(Map<String, dynamic> json) {
    return DiscountValidationResult(
      isValid: json['valid'] ?? false,
      errorMessage: json['error'],
      discount: json['discount'] != null
          ? Discount._fromValidationResponse(json['discount'])
          : null,
      discountAmount: json['discount']?['discountAmount']?.toDouble(),
      finalAmount: json['discount']?['finalAmount']?.toDouble(),
      minimumAmount: json['minimumAmount']?.toDouble(),
    );
  }
}
