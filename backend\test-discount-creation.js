#!/usr/bin/env node

/**
 * Test script to create a sample discount directly in the database
 */

const { query } = require('./config/database');

async function createTestDiscount() {
  try {
    console.log('🧪 Creating test discount...\n');

    // Create a simple test discount
    const discountData = {
      name: 'Test Platform Discount',
      description: 'A test discount for debugging',
      discount_type: 'percentage',
      discount_value: 20,
      scope: 'platform',
      minimum_order_amount: 25,
      usage_limit_per_customer: 1,
      start_date: new Date(),
      end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      is_active: true
    };

    const result = await query(`
      INSERT INTO discounts (
        name, description, discount_type, discount_value, scope,
        minimum_order_amount, usage_limit_per_customer,
        start_date, end_date, is_active, created_at, updated_at
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW()
      ) RETURNING *
    `, [
      discountData.name,
      discountData.description,
      discountData.discount_type,
      discountData.discount_value,
      discountData.scope,
      discountData.minimum_order_amount,
      discountData.usage_limit_per_customer,
      discountData.start_date,
      discountData.end_date,
      discountData.is_active
    ]);

    console.log('✅ Test discount created successfully!');
    console.log('📋 Discount details:');
    console.log(`   ID: ${result.rows[0].id}`);
    console.log(`   Name: ${result.rows[0].name}`);
    console.log(`   Type: ${result.rows[0].discount_type}`);
    console.log(`   Value: ${result.rows[0].discount_value}%`);
    console.log(`   Scope: ${result.rows[0].scope}`);
    console.log(`   Active: ${result.rows[0].is_active}`);

    // Now test retrieving it
    console.log('\n🔍 Testing retrieval...');
    const retrieveResult = await query(`
      SELECT d.*, 
             COALESCE(usage_stats.total_usage, 0) as total_usage,
             COALESCE(usage_stats.total_savings, 0) as total_savings
      FROM discounts d
      LEFT JOIN (
        SELECT discount_id, COUNT(*) as total_usage, SUM(discount_amount) as total_savings
        FROM discount_usage
        GROUP BY discount_id
      ) usage_stats ON d.id = usage_stats.discount_id
      WHERE d.id = $1
    `, [result.rows[0].id]);

    if (retrieveResult.rows.length > 0) {
      console.log('✅ Discount retrieved successfully!');
      console.log(`   Usage count: ${retrieveResult.rows[0].total_usage}`);
      console.log(`   Total savings: $${retrieveResult.rows[0].total_savings || 0}`);
    } else {
      console.log('❌ Failed to retrieve discount');
    }

    // Test the admin query format
    console.log('\n🔍 Testing admin query format...');
    const adminQueryResult = await query(`
      SELECT
        d.*,
        COALESCE(usage_stats.total_usage, 0) as total_usage,
        COALESCE(usage_stats.total_savings, 0) as total_savings
      FROM discounts d
      LEFT JOIN (
        SELECT
          discount_id,
          COUNT(*) as total_usage,
          SUM(discount_amount) as total_savings
        FROM discount_usage
        GROUP BY discount_id
      ) usage_stats ON d.id = usage_stats.discount_id
      ORDER BY d.created_at DESC
      LIMIT 10
    `);

    console.log(`✅ Admin query successful! Found ${adminQueryResult.rows.length} discounts`);
    adminQueryResult.rows.forEach((discount, index) => {
      console.log(`   ${index + 1}. ${discount.name} (${discount.discount_type})`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
    
    if (error.code === '42P01') {
      console.log('💡 Table does not exist. Run database migrations first.');
    } else if (error.code === '23505') {
      console.log('💡 Discount with similar data already exists.');
    }
  } finally {
    process.exit(0);
  }
}

createTestDiscount();
