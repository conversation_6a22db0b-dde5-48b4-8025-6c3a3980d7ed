const { pool } = require('../config/database');

const migrations = [
  // Users table for customers, drivers, and restaurants
  `
    CREATE TABLE IF NOT EXISTS users (
      id SERIAL PRIMARY KEY,
      email VARCHAR(255) UNIQUE NOT NULL,
      password_hash VARCHAR(255) NOT NULL,
      user_type VARCHAR(20) NOT NULL CHECK (user_type IN ('customer', 'driver', 'restaurant', 'admin')),
      is_verified BOOLEAN DEFAULT false,
      is_active BOOLEAN DEFAULT true,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,
  
  // Customer profiles
  `
    CREATE TABLE IF NOT EXISTS customer_profiles (
      id SERIAL PRIMARY KEY,
      user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
      full_name VARCHAR(100) NOT NULL,
      phone VARCHAR(20) NOT NULL,
      address TEXT NOT NULL,
      latitude DECIMAL(10, 8),
      longitude DECIMAL(11, 8),
      profile_completed BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,

  // Customer addresses - for multiple saved addresses
  `
    CREATE TABLE IF NOT EXISTS customer_addresses (
      id SERIAL PRIMARY KEY,
      customer_id INTEGER REFERENCES customer_profiles(id) ON DELETE CASCADE,
      label VARCHAR(50) NOT NULL DEFAULT 'Home',
      address TEXT NOT NULL,
      latitude DECIMAL(10, 8) NOT NULL,
      longitude DECIMAL(11, 8) NOT NULL,
      is_default BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,
  
  // Restaurant profiles
  `
    CREATE TABLE IF NOT EXISTS restaurant_profiles (
      id SERIAL PRIMARY KEY,
      user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
      restaurant_name VARCHAR(100) NOT NULL,
      description TEXT,
      phone VARCHAR(20) NOT NULL,
      address TEXT NOT NULL,
      latitude DECIMAL(10, 8),
      longitude DECIMAL(11, 8),
      cuisine_type VARCHAR(50) NOT NULL,
      is_open BOOLEAN DEFAULT FALSE,
      rating DECIMAL(3, 2) DEFAULT 0.00,
      total_orders INTEGER DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,
  
  // Driver profiles
  `
    CREATE TABLE IF NOT EXISTS driver_profiles (
      id SERIAL PRIMARY KEY,
      user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
      full_name VARCHAR(255),
      phone VARCHAR(20),
      license_number VARCHAR(100),
      vehicle_type VARCHAR(50),
      vehicle_plate VARCHAR(20),
      is_available BOOLEAN DEFAULT false,
      current_latitude DECIMAL(10, 8),
      current_longitude DECIMAL(11, 8),
      rating DECIMAL(3, 2) DEFAULT 0.00,
      total_deliveries INTEGER DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,
  
  // Restaurant profiles
  `
    CREATE TABLE IF NOT EXISTS restaurant_profiles (
      id SERIAL PRIMARY KEY,
      user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
      restaurant_name VARCHAR(255),
      description TEXT,
      phone VARCHAR(20),
      address TEXT,
      latitude DECIMAL(10, 8),
      longitude DECIMAL(11, 8),
      cuisine_type VARCHAR(100),
      opening_hours JSONB,
      is_open BOOLEAN DEFAULT false,
      rating DECIMAL(3, 2) DEFAULT 0.00,
      total_orders INTEGER DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,
  
  // Categories for menu items
  `
    CREATE TABLE IF NOT EXISTS categories (
      id SERIAL PRIMARY KEY,
      restaurant_id INTEGER REFERENCES restaurant_profiles(id) ON DELETE CASCADE,
      name VARCHAR(255) NOT NULL,
      description TEXT,
      is_active BOOLEAN DEFAULT true,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,
  
  // Menu items
  `
    CREATE TABLE IF NOT EXISTS menu_items (
      id SERIAL PRIMARY KEY,
      restaurant_id INTEGER REFERENCES restaurant_profiles(id) ON DELETE CASCADE,
      category_id INTEGER REFERENCES categories(id) ON DELETE SET NULL,
      name VARCHAR(255) NOT NULL,
      description TEXT,
      price DECIMAL(10, 2) NOT NULL,
      image_url TEXT,
      is_available BOOLEAN DEFAULT true,
      preparation_time INTEGER DEFAULT 15,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,
  
  // Market locations for mart orders
  `
    CREATE TABLE IF NOT EXISTS market_locations (
      id SERIAL PRIMARY KEY,
      name VARCHAR(255) NOT NULL,
      address TEXT NOT NULL,
      latitude DECIMAL(10, 8) NOT NULL,
      longitude DECIMAL(11, 8) NOT NULL,
      phone VARCHAR(20),
      operating_hours JSONB,
      is_active BOOLEAN DEFAULT true,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,

  // Mart categories
  `
    CREATE TABLE IF NOT EXISTS mart_categories (
      id SERIAL PRIMARY KEY,
      name VARCHAR(255) NOT NULL,
      description TEXT,
      image_url TEXT,
      is_active BOOLEAN DEFAULT true,
      sort_order INTEGER DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,

  // Mart products
  `
    CREATE TABLE IF NOT EXISTS mart_products (
      id SERIAL PRIMARY KEY,
      category_id INTEGER REFERENCES mart_categories(id) ON DELETE SET NULL,
      name VARCHAR(255) NOT NULL,
      description TEXT,
      price DECIMAL(10, 2) NOT NULL,
      image_url TEXT,
      is_available BOOLEAN DEFAULT true,
      stock_quantity INTEGER DEFAULT 0,
      unit VARCHAR(50) DEFAULT 'piece',
      barcode VARCHAR(100),
      brand VARCHAR(100),
      weight DECIMAL(8, 2),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,

  // Orders (modified to support both restaurant and mart orders)
  `
    CREATE TABLE IF NOT EXISTS orders (
      id SERIAL PRIMARY KEY,
      customer_id INTEGER REFERENCES customer_profiles(id) ON DELETE CASCADE,
      restaurant_id INTEGER REFERENCES restaurant_profiles(id) ON DELETE CASCADE,
      market_location_id INTEGER REFERENCES market_locations(id) ON DELETE SET NULL,
      driver_id INTEGER REFERENCES driver_profiles(id) ON DELETE SET NULL,
      order_type VARCHAR(20) DEFAULT 'restaurant' CHECK (order_type IN ('restaurant', 'mart')),
      order_number VARCHAR(20) UNIQUE NOT NULL,
      status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'preparing', 'ready', 'assigned', 'picked_up', 'delivered', 'cancelled')),
      total_amount DECIMAL(10, 2) NOT NULL,
      subtotal_amount DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
      delivery_fee DECIMAL(10, 2) DEFAULT 0.00,
      discount_id INTEGER,
      discount_amount DECIMAL(10, 2) DEFAULT 0.00,
      delivery_address TEXT NOT NULL,
      delivery_latitude DECIMAL(10, 8),
      delivery_longitude DECIMAL(11, 8),
      special_instructions TEXT,
      estimated_delivery_time TIMESTAMP,
      assigned_at TIMESTAMP,
      assignment_expires_at TIMESTAMP,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      CONSTRAINT orders_type_check CHECK (
        (order_type = 'restaurant' AND restaurant_id IS NOT NULL AND market_location_id IS NULL) OR
        (order_type = 'mart' AND restaurant_id IS NULL AND market_location_id IS NOT NULL)
      )
    );
  `,
  
  // Order items (modified to support both restaurant and mart items)
  `
    CREATE TABLE IF NOT EXISTS order_items (
      id SERIAL PRIMARY KEY,
      order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
      menu_item_id INTEGER REFERENCES menu_items(id) ON DELETE CASCADE,
      mart_product_id INTEGER REFERENCES mart_products(id) ON DELETE CASCADE,
      quantity INTEGER NOT NULL,
      unit_price DECIMAL(10, 2) NOT NULL,
      total_price DECIMAL(10, 2) NOT NULL,
      special_instructions TEXT,
      CONSTRAINT order_items_type_check CHECK (
        (menu_item_id IS NOT NULL AND mart_product_id IS NULL) OR
        (menu_item_id IS NULL AND mart_product_id IS NOT NULL)
      )
    );
  `,

  // Driver order assignments - tracks which driver is currently assigned to an order
  `
    CREATE TABLE IF NOT EXISTS driver_order_assignments (
      id SERIAL PRIMARY KEY,
      order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
      driver_id INTEGER REFERENCES driver_profiles(id) ON DELETE CASCADE,
      assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      expires_at TIMESTAMP NOT NULL,
      status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'expired')),
      responded_at TIMESTAMP,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(order_id, driver_id)
    );
  `,

  // Declined orders - tracks which drivers have declined which orders
  `
    CREATE TABLE IF NOT EXISTS declined_orders (
      id SERIAL PRIMARY KEY,
      driver_id INTEGER NOT NULL REFERENCES driver_profiles(id) ON DELETE CASCADE,
      order_id INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
      declined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(driver_id, order_id)
    );
  `,

  // Order cancellations - tracks order cancellations by restaurants/customers
  `
    CREATE TABLE IF NOT EXISTS order_cancellations (
      id SERIAL PRIMARY KEY,
      order_id INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
      cancelled_by_type VARCHAR(20) NOT NULL CHECK (cancelled_by_type IN ('restaurant', 'customer', 'admin')),
      cancelled_by_id INTEGER NOT NULL,
      cancellation_reason TEXT,
      cancelled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,

  // Food ratings table
  `
    CREATE TABLE IF NOT EXISTS food_ratings (
      id SERIAL PRIMARY KEY,
      customer_id INTEGER REFERENCES customer_profiles(id) ON DELETE CASCADE,
      menu_item_id INTEGER REFERENCES menu_items(id) ON DELETE CASCADE,
      order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
      rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
      review_text TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(customer_id, menu_item_id, order_id)
    );
  `,

  // Driver ratings table
  `
    CREATE TABLE IF NOT EXISTS driver_ratings (
      id SERIAL PRIMARY KEY,
      customer_id INTEGER REFERENCES customer_profiles(id) ON DELETE CASCADE,
      driver_id INTEGER REFERENCES driver_profiles(id) ON DELETE CASCADE,
      order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
      rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
      review_text TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(customer_id, driver_id, order_id)
    );
  `,

  // Add rating columns to menu_items table
  `
    ALTER TABLE menu_items
    ADD COLUMN IF NOT EXISTS average_rating DECIMAL(3, 2) DEFAULT 0.00,
    ADD COLUMN IF NOT EXISTS total_ratings INTEGER DEFAULT 0;
  `,

  // Create indexes for better performance
  `
    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    CREATE INDEX IF NOT EXISTS idx_users_type ON users(user_type);
    CREATE INDEX IF NOT EXISTS idx_orders_customer ON orders(customer_id);
    CREATE INDEX IF NOT EXISTS idx_orders_restaurant ON orders(restaurant_id);
    CREATE INDEX IF NOT EXISTS idx_orders_market_location ON orders(market_location_id);
    CREATE INDEX IF NOT EXISTS idx_orders_driver ON orders(driver_id);
    CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
    CREATE INDEX IF NOT EXISTS idx_orders_type ON orders(order_type);
    CREATE INDEX IF NOT EXISTS idx_orders_assignment_expires ON orders(assignment_expires_at);
    CREATE INDEX IF NOT EXISTS idx_menu_items_restaurant ON menu_items(restaurant_id);
    CREATE INDEX IF NOT EXISTS idx_mart_products_category ON mart_products(category_id);
    CREATE INDEX IF NOT EXISTS idx_mart_products_available ON mart_products(is_available);
    CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id);
    CREATE INDEX IF NOT EXISTS idx_order_items_menu_item ON order_items(menu_item_id);
    CREATE INDEX IF NOT EXISTS idx_order_items_mart_product ON order_items(mart_product_id);
    CREATE INDEX IF NOT EXISTS idx_driver_assignments_order ON driver_order_assignments(order_id);
    CREATE INDEX IF NOT EXISTS idx_driver_assignments_driver ON driver_order_assignments(driver_id);
    CREATE INDEX IF NOT EXISTS idx_driver_assignments_status ON driver_order_assignments(status);
    CREATE INDEX IF NOT EXISTS idx_driver_assignments_expires ON driver_order_assignments(expires_at);
    CREATE INDEX IF NOT EXISTS idx_declined_orders_driver_id ON declined_orders(driver_id);
    CREATE INDEX IF NOT EXISTS idx_declined_orders_order_id ON declined_orders(order_id);
    CREATE INDEX IF NOT EXISTS idx_order_cancellations_order_id ON order_cancellations(order_id);
    CREATE INDEX IF NOT EXISTS idx_food_ratings_customer ON food_ratings(customer_id);
    CREATE INDEX IF NOT EXISTS idx_food_ratings_menu_item ON food_ratings(menu_item_id);
    CREATE INDEX IF NOT EXISTS idx_food_ratings_order ON food_ratings(order_id);
    CREATE INDEX IF NOT EXISTS idx_driver_ratings_customer ON driver_ratings(customer_id);
    CREATE INDEX IF NOT EXISTS idx_driver_ratings_driver ON driver_ratings(driver_id);
    CREATE INDEX IF NOT EXISTS idx_driver_ratings_order ON driver_ratings(order_id);
  `
];

async function runMigrations() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 Starting database migrations...');
    
    for (let i = 0; i < migrations.length; i++) {
      console.log(`📝 Running migration ${i + 1}/${migrations.length}...`);
      await client.query(migrations[i]);
    }
    
    console.log('✅ All migrations completed successfully!');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run migrations if this file is executed directly
if (require.main === module) {
  runMigrations()
    .then(() => {
      console.log('🎉 Database setup complete!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { runMigrations };
