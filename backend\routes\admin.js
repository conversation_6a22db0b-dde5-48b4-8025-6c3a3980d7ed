const express = require('express');
const bcrypt = require('bcryptjs');
const { query } = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { validateRequest, schemas } = require('../middleware/validation');
const orderAssignmentScheduler = require('../services/orderAssignmentScheduler');

const router = express.Router();

// Create new restaurant (Admin only)
router.post('/restaurants', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { 
      email, 
      password, 
      restaurantName, 
      description, 
      phone, 
      address, 
      latitude, 
      longitude, 
      cuisineType 
    } = req.body;

    // Validate required fields
    if (!email || !password || !restaurantName || !phone || !address || !cuisineType) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Check if user already exists
    const existingUser = await query('SELECT id FROM users WHERE email = $1', [email]);
    
    if (existingUser.rows.length > 0) {
      return res.status(400).json({ error: 'User already exists with this email' });
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create user account
    const userResult = await query(
      'INSERT INTO users (email, password_hash, user_type, is_verified) VALUES ($1, $2, $3, $4) RETURNING id, email, user_type, created_at',
      [email, passwordHash, 'restaurant', true]
    );

    const user = userResult.rows[0];

    // Create restaurant profile
    const profileResult = await query(
      `INSERT INTO restaurant_profiles 
       (user_id, restaurant_name, description, phone, address, latitude, longitude, cuisine_type) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8) 
       RETURNING *`,
      [user.id, restaurantName, description, phone, address, latitude, longitude, cuisineType]
    );

    const profile = profileResult.rows[0];

    res.status(201).json({
      message: 'Restaurant created successfully',
      restaurant: {
        user: {
          id: user.id,
          email: user.email,
          userType: user.user_type,
          createdAt: user.created_at
        },
        profile: {
          id: profile.id,
          restaurantName: profile.restaurant_name,
          description: profile.description,
          phone: profile.phone,
          address: profile.address,
          latitude: profile.latitude,
          longitude: profile.longitude,
          cuisineType: profile.cuisine_type,
          isOpen: profile.is_open,
          rating: profile.rating,
          totalOrders: profile.total_orders,
          createdAt: profile.created_at
        }
      }
    });

  } catch (error) {
    console.error('Create restaurant error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get all restaurants (Admin only)
router.get('/restaurants', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const result = await query(`
      SELECT 
        u.id as user_id, u.email, u.is_active, u.created_at as user_created_at,
        rp.id as profile_id, rp.restaurant_name, rp.description, rp.phone, 
        rp.address, rp.latitude, rp.longitude, rp.cuisine_type, rp.is_open,
        rp.rating, rp.total_orders, rp.created_at as profile_created_at
      FROM users u
      JOIN restaurant_profiles rp ON u.id = rp.user_id
      WHERE u.user_type = 'restaurant'
      ORDER BY rp.created_at DESC
    `);

    const restaurants = result.rows.map(row => ({
      user: {
        id: row.user_id,
        email: row.email,
        isActive: row.is_active,
        createdAt: row.user_created_at
      },
      profile: {
        id: row.profile_id,
        restaurantName: row.restaurant_name,
        description: row.description,
        phone: row.phone,
        address: row.address,
        latitude: row.latitude,
        longitude: row.longitude,
        cuisineType: row.cuisine_type,
        isOpen: row.is_open,
        rating: parseFloat(row.rating),
        totalOrders: row.total_orders,
        createdAt: row.profile_created_at
      }
    }));

    res.json({ restaurants });

  } catch (error) {
    console.error('Get restaurants error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create new driver (Admin only)
router.post('/drivers', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { 
      email, 
      password, 
      fullName, 
      phone, 
      licenseNumber, 
      vehicleType, 
      vehiclePlate,
      vehicleColor 
    } = req.body;

    // Validate required fields
    if (!email || !password || !fullName || !phone || !licenseNumber || !vehicleType || !vehiclePlate) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Check if user already exists
    const existingUser = await query('SELECT id FROM users WHERE email = $1', [email]);
    
    if (existingUser.rows.length > 0) {
      return res.status(400).json({ error: 'User already exists with this email' });
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create user account
    const userResult = await query(
      'INSERT INTO users (email, password_hash, user_type, is_verified) VALUES ($1, $2, $3, $4) RETURNING id, email, user_type, created_at',
      [email, passwordHash, 'driver', true]
    );

    const user = userResult.rows[0];

    // Create driver profile
    const profileResult = await query(
      `INSERT INTO driver_profiles 
       (user_id, full_name, phone, license_number, vehicle_type, vehicle_plate) 
       VALUES ($1, $2, $3, $4, $5, $6) 
       RETURNING *`,
      [user.id, fullName, phone, licenseNumber, vehicleType, vehiclePlate]
    );

    const profile = profileResult.rows[0];

    res.status(201).json({
      message: 'Driver created successfully',
      driver: {
        user: {
          id: user.id,
          email: user.email,
          userType: user.user_type,
          createdAt: user.created_at
        },
        profile: {
          id: profile.id,
          fullName: profile.full_name,
          phone: profile.phone,
          licenseNumber: profile.license_number,
          vehicleType: profile.vehicle_type,
          vehiclePlate: profile.vehicle_plate,
          vehicleColor: null, // Column doesn't exist in current schema
          isAvailable: profile.is_available,
          currentLatitude: profile.current_latitude,
          currentLongitude: profile.current_longitude,
          rating: profile.rating || 0,
          totalDeliveries: profile.total_deliveries || 0,
          totalEarnings: 0, // Column doesn't exist in current schema
          createdAt: profile.created_at
        }
      }
    });

  } catch (error) {
    console.error('Create driver error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get all drivers (Admin only)
router.get('/drivers', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const result = await query(`
      SELECT 
        u.id as user_id, u.email, u.is_active, u.created_at as user_created_at,
        dp.id as profile_id, dp.full_name, dp.phone, dp.license_number,
        dp.vehicle_type, dp.vehicle_plate, dp.is_available,
        dp.current_latitude, dp.current_longitude, dp.rating, dp.total_deliveries,
        dp.created_at as profile_created_at
      FROM users u
      JOIN driver_profiles dp ON u.id = dp.user_id
      WHERE u.user_type = 'driver'
      ORDER BY dp.created_at DESC
    `);

    const drivers = result.rows.map(row => ({
      user: {
        id: row.user_id,
        email: row.email,
        isActive: row.is_active,
        createdAt: row.user_created_at
      },
      profile: {
        id: row.profile_id,
        fullName: row.full_name,
        phone: row.phone,
        licenseNumber: row.license_number,
        vehicleType: row.vehicle_type,
        vehiclePlate: row.vehicle_plate,
        vehicleColor: null, // Column doesn't exist in current schema
        isAvailable: row.is_available,
        currentLatitude: row.current_latitude,
        currentLongitude: row.current_longitude,
        rating: parseFloat(row.rating || 0),
        totalDeliveries: row.total_deliveries || 0,
        totalEarnings: 0, // Column doesn't exist in current schema
        createdAt: row.profile_created_at
      }
    }));

    res.json({ drivers });

  } catch (error) {
    console.error('Get drivers error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get all customers (Admin only)
router.get('/customers', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const result = await query(`
      SELECT 
        u.id as user_id, u.email, u.is_active, u.created_at as user_created_at,
        cp.id as profile_id, cp.full_name, cp.phone, cp.address, 
        cp.latitude, cp.longitude, cp.profile_completed, cp.created_at as profile_created_at
      FROM users u
      LEFT JOIN customer_profiles cp ON u.id = cp.user_id
      WHERE u.user_type = 'customer'
      ORDER BY u.created_at DESC
    `);

    const customers = result.rows.map(row => ({
      user: {
        id: row.user_id,
        email: row.email,
        isActive: row.is_active,
        createdAt: row.user_created_at
      },
      profile: row.profile_id ? {
        id: row.profile_id,
        fullName: row.full_name,
        phone: row.phone,
        address: row.address,
        latitude: row.latitude,
        longitude: row.longitude,
        profileCompleted: row.profile_completed,
        createdAt: row.profile_created_at
      } : null
    }));

    res.json({ customers });

  } catch (error) {
    console.error('Get customers error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get system statistics (Admin only)
router.get('/stats', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    // Get counts
    const customerCount = await query('SELECT COUNT(*) FROM users WHERE user_type = $1', ['customer']);
    const restaurantCount = await query('SELECT COUNT(*) FROM users WHERE user_type = $1', ['restaurant']);
    const driverCount = await query('SELECT COUNT(*) FROM users WHERE user_type = $1', ['driver']);
    
    // Get order count (handle if orders table doesn't exist or is empty)
    let orderCount;
    try {
      orderCount = await query('SELECT COUNT(*) FROM orders');
    } catch (orderError) {
      console.log('Orders table not found or error:', orderError.message);
      orderCount = { rows: [{ count: '0' }] };
    }
    
    // Get recent orders (handle if no orders exist)
    let recentOrders;
    try {
      recentOrders = await query(`
        SELECT o.id, o.order_number, o.status, o.total_amount, o.created_at,
               rp.restaurant_name, cp.full_name as customer_name
        FROM orders o
        LEFT JOIN restaurant_profiles rp ON o.restaurant_id = rp.id
        LEFT JOIN customer_profiles cp ON o.customer_id = cp.id
        ORDER BY o.created_at DESC
        LIMIT 10
      `);
    } catch (ordersError) {
      console.log('Error fetching recent orders:', ordersError.message);
      recentOrders = { rows: [] };
    }

    res.json({
      stats: {
        totalCustomers: parseInt(customerCount.rows[0].count),
        totalRestaurants: parseInt(restaurantCount.rows[0].count),
        totalDrivers: parseInt(driverCount.rows[0].count),
        totalOrders: parseInt(orderCount.rows[0].count)
      },
      recentOrders: recentOrders.rows.map(order => ({
        id: order.id,
        orderNumber: order.order_number,
        status: order.status,
        totalAmount: parseFloat(order.total_amount || 0),
        restaurantName: order.restaurant_name || 'Unknown Restaurant',
        customerName: order.customer_name || 'Unknown Customer',
        createdAt: order.created_at
      }))
    });

  } catch (error) {
    console.error('Get stats error:', error);
    res.status(500).json({ error: 'Internal server error', details: error.message });
  }
});

// Get all menu items for a restaurant (Admin only)
router.get('/restaurants/:restaurantId/menu', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { restaurantId } = req.params;

    // Get categories and menu items
    const categoriesResult = await query(
      'SELECT * FROM categories WHERE restaurant_id = $1 ORDER BY name',
      [restaurantId]
    );

    const menuItemsResult = await query(
      'SELECT * FROM menu_items WHERE restaurant_id = $1 ORDER BY category_id, name',
      [restaurantId]
    );

    const categories = categoriesResult.rows;
    const menuItems = menuItemsResult.rows;

    // Group menu items by category
    const categoriesWithItems = categories.map(category => ({
      ...category,
      items: menuItems.filter(item => item.category_id === category.id)
    }));

    // Add uncategorized items
    const uncategorizedItems = menuItems.filter(item => !item.category_id);
    if (uncategorizedItems.length > 0) {
      categoriesWithItems.push({
        id: null,
        name: 'Uncategorized',
        description: 'Items without a category',
        is_active: true,
        items: uncategorizedItems
      });
    }

    res.json({ categories: categoriesWithItems });

  } catch (error) {
    console.error('Get menu error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create category for restaurant (Admin only)
router.post('/restaurants/:restaurantId/categories', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { restaurantId } = req.params;
    const { name, description } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Category name is required' });
    }

    const result = await query(
      'INSERT INTO categories (restaurant_id, name, description) VALUES ($1, $2, $3) RETURNING *',
      [restaurantId, name, description]
    );

    res.status(201).json({ category: result.rows[0] });

  } catch (error) {
    console.error('Create category error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create menu item for restaurant (Admin only)
router.post('/restaurants/:restaurantId/menu-items', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { restaurantId } = req.params;
    const { name, description, price, categoryId, imageUrl, preparationTime, isAvailable } = req.body;

    if (!name || !price) {
      return res.status(400).json({ error: 'Name and price are required' });
    }

    const result = await query(
      `INSERT INTO menu_items 
       (restaurant_id, category_id, name, description, price, image_url, preparation_time, is_available) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *`,
      [restaurantId, categoryId || null, name, description, parseFloat(price), imageUrl, preparationTime || 15, isAvailable !== false]
    );

    res.status(201).json({ menuItem: result.rows[0] });

  } catch (error) {
    console.error('Create menu item error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update menu item (Admin only)
router.patch('/menu-items/:itemId', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { itemId } = req.params;
    const { name, description, price, categoryId, imageUrl, preparationTime, isAvailable } = req.body;

    const updates = [];
    const values = [];
    let paramCount = 1;

    if (name !== undefined) {
      updates.push(`name = $${paramCount++}`);
      values.push(name);
    }
    if (description !== undefined) {
      updates.push(`description = $${paramCount++}`);
      values.push(description);
    }
    if (price !== undefined) {
      updates.push(`price = $${paramCount++}`);
      values.push(parseFloat(price));
    }
    if (categoryId !== undefined) {
      updates.push(`category_id = $${paramCount++}`);
      values.push(categoryId || null);
    }
    if (imageUrl !== undefined) {
      updates.push(`image_url = $${paramCount++}`);
      values.push(imageUrl);
    }
    if (preparationTime !== undefined) {
      updates.push(`preparation_time = $${paramCount++}`);
      values.push(preparationTime);
    }
    if (isAvailable !== undefined) {
      updates.push(`is_available = $${paramCount++}`);
      values.push(isAvailable);
    }

    if (updates.length === 0) {
      return res.status(400).json({ error: 'No fields to update' });
    }

    updates.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(itemId);

    const result = await query(
      `UPDATE menu_items SET ${updates.join(', ')} WHERE id = $${paramCount} RETURNING *`,
      values
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Menu item not found' });
    }

    res.json({ menuItem: result.rows[0] });

  } catch (error) {
    console.error('Update menu item error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete menu item (Admin only)
router.delete('/menu-items/:itemId', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { itemId } = req.params;

    const result = await query(
      'DELETE FROM menu_items WHERE id = $1 RETURNING *',
      [itemId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Menu item not found' });
    }

    res.json({ message: 'Menu item deleted successfully' });

  } catch (error) {
    console.error('Delete menu item error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Toggle user active status (Admin only)
router.patch('/users/:userId/status', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { userId } = req.params;
    const { isActive } = req.body;

    if (typeof isActive !== 'boolean') {
      return res.status(400).json({ error: 'isActive must be a boolean value' });
    }

    const result = await query(
      'UPDATE users SET is_active = $1 WHERE id = $2 RETURNING id, email, user_type, is_active',
      [isActive, userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
      user: result.rows[0]
    });

  } catch (error) {
    console.error('Update user status error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get all orders with filtering and search (Admin only)
router.get('/orders', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { status, search, page = 1, limit = 50 } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = '';
    let queryParams = [];
    let paramCount = 0;

    if (status && status !== 'undefined') {
      paramCount++;
      whereClause += `WHERE o.status = $${paramCount}`;
      queryParams.push(status);
    }

    if (search) {
      paramCount++;
      const searchCondition = status ? ' AND ' : 'WHERE ';
      whereClause += `${searchCondition}(cp.full_name ILIKE $${paramCount} OR rp.restaurant_name ILIKE $${paramCount + 1} OR o.id::text ILIKE $${paramCount + 2})`;
      queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
      paramCount += 2;
    }

    const ordersQuery = `
      SELECT 
        o.id,
        o.status,
        o.total_amount,
        o.delivery_address,
        o.special_instructions,
        o.estimated_delivery_time,
        o.created_at,
        cp.full_name as customer_name,
        cu.email as customer_email,
        rp.restaurant_name,
        COUNT(*) OVER() as total_count
      FROM orders o
      JOIN customer_profiles cp ON o.customer_id = cp.id
      JOIN users cu ON cp.user_id = cu.id
      JOIN restaurant_profiles rp ON o.restaurant_id = rp.id
      ${whereClause}
      ORDER BY o.created_at DESC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;

    queryParams.push(limit, offset);
    const result = await query(ordersQuery, queryParams);

    const orders = result.rows;
    const totalCount = orders.length > 0 ? parseInt(orders[0].total_count) : 0;

    // Get order items for each order
    for (let order of orders) {
      const itemsResult = await query(`
        SELECT 
          oi.quantity,
          oi.unit_price AS price,
          oi.special_instructions,
          mi.name
        FROM order_items oi
        JOIN menu_items mi ON oi.menu_item_id = mi.id
        WHERE oi.order_id = $1
      `, [order.id]);
      
      order.items = itemsResult.rows;
    }

    res.json({
      orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error('Get orders error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get order details (Admin only)
router.get('/orders/:orderId', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { orderId } = req.params;

    const orderResult = await query(`
      SELECT 
        o.*,
        cp.full_name as customer_name,
        cp.phone as customer_phone,
        cu.email as customer_email,
        rp.restaurant_name,
        rp.phone as restaurant_phone,
        dp.full_name as driver_name,
        dp.phone as driver_phone
      FROM orders o
      JOIN customer_profiles cp ON o.customer_id = cp.id
      JOIN users cu ON cp.user_id = cu.id
      JOIN restaurant_profiles rp ON o.restaurant_id = rp.id
      LEFT JOIN driver_profiles dp ON o.driver_id = dp.user_id
      WHERE o.id = $1
    `, [orderId]);

    if (orderResult.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    const order = orderResult.rows[0];

    // Get order items
    const itemsResult = await query(`
      SELECT 
        oi.quantity,
        oi.unit_price AS price,
        oi.special_instructions,
        mi.name,
        mi.description
      FROM order_items oi
      JOIN menu_items mi ON oi.menu_item_id = mi.id
      WHERE oi.order_id = $1
    `, [orderId]);

    order.items = itemsResult.rows;

    res.json(order);
  } catch (error) {
    console.error('Get order details error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update order status (Admin only)
router.patch('/orders/:orderId/status', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { orderId } = req.params;
    const { status } = req.body;

    const validStatuses = ['pending', 'confirmed', 'preparing', 'ready', 'picked_up', 'delivered', 'cancelled'];
    
    if (!status || !validStatuses.includes(status)) {
      return res.status(400).json({ error: 'Invalid status' });
    }

    // Check if order exists
    const orderCheck = await query('SELECT id, status FROM orders WHERE id = $1', [orderId]);
    
    if (orderCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    // Update order status
    const result = await query(
      'UPDATE orders SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 RETURNING *',
      [status, orderId]
    );

    res.json({
      message: 'Order status updated successfully',
      order: result.rows[0]
    });
  } catch (error) {
    console.error('Update order status error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get order analytics/stats (Admin only)
router.get('/analytics/orders', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { period = '7d' } = req.query;
    
    let dateFilter = '';
    if (period === '7d') {
      dateFilter = "WHERE o.created_at >= CURRENT_DATE - INTERVAL '7 days'";
    } else if (period === '30d') {
      dateFilter = "WHERE o.created_at >= CURRENT_DATE - INTERVAL '30 days'";
    } else if (period === '90d') {
      dateFilter = "WHERE o.created_at >= CURRENT_DATE - INTERVAL '90 days'";
    }

    // Total orders and revenue
    const totalStats = await query(`
      SELECT 
        COUNT(*) as total_orders,
        COALESCE(SUM(total_amount), 0) as total_revenue,
        COUNT(CASE WHEN status = 'delivered' THEN 1 END) as completed_orders,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders
      FROM orders o
      ${dateFilter}
    `);

    // Orders by status
    const statusStats = await query(`
      SELECT 
        status,
        COUNT(*) as count
      FROM orders o
      ${dateFilter}
      GROUP BY status
      ORDER BY count DESC
    `);

    // Daily order trends
    const trendStats = await query(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as orders,
        SUM(total_amount) as revenue
      FROM orders o
      ${dateFilter}
      GROUP BY DATE(created_at)
      ORDER BY date DESC
      LIMIT 30
    `);

    // Top restaurants by orders
    const topRestaurants = await query(`
      SELECT 
        rp.restaurant_name,
        COUNT(*) as order_count,
        SUM(o.total_amount) as revenue
      FROM orders o
      JOIN restaurant_profiles rp ON o.restaurant_id = rp.id
      ${dateFilter}
      GROUP BY rp.restaurant_name, rp.user_id
      ORDER BY order_count DESC
      LIMIT 10
    `);

    res.json({
      totalStats: totalStats.rows[0],
      statusStats: statusStats.rows,
      trendStats: trendStats.rows,
      topRestaurants: topRestaurants.rows
    });
  } catch (error) {
    console.error('Get order analytics error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get rating analytics (Admin only)
router.get('/analytics/ratings', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    // Get overall food rating statistics
    const foodRatingStats = await query(`
      SELECT
        AVG(fr.rating) as average_rating,
        COUNT(fr.id) as total_ratings
      FROM food_ratings fr
    `);

    // Get overall driver rating statistics
    const driverRatingStats = await query(`
      SELECT
        AVG(dr.rating) as average_rating,
        COUNT(dr.id) as total_ratings
      FROM driver_ratings dr
    `);

    // Get top rated food items
    const topRatedItems = await query(`
      SELECT
        mi.id, mi.name, mi.average_rating, mi.total_ratings,
        rp.restaurant_name
      FROM menu_items mi
      JOIN restaurant_profiles rp ON mi.restaurant_id = rp.id
      WHERE mi.total_ratings >= 3
      ORDER BY mi.average_rating DESC, mi.total_ratings DESC
      LIMIT 10
    `);

    // Get low rated food items (items that need improvement)
    const lowRatedItems = await query(`
      SELECT
        mi.id, mi.name, mi.average_rating, mi.total_ratings,
        rp.restaurant_name
      FROM menu_items mi
      JOIN restaurant_profiles rp ON mi.restaurant_id = rp.id
      WHERE mi.total_ratings >= 3 AND mi.average_rating < 3.5
      ORDER BY mi.average_rating ASC, mi.total_ratings DESC
      LIMIT 10
    `);

    // Get top rated drivers
    const topRatedDrivers = await query(`
      SELECT
        dp.id, dp.full_name, dp.rating,
        COUNT(dr.id) as total_ratings
      FROM driver_profiles dp
      LEFT JOIN driver_ratings dr ON dp.id = dr.driver_id
      WHERE dp.rating IS NOT NULL
      GROUP BY dp.id, dp.full_name, dp.rating
      HAVING COUNT(dr.id) >= 3
      ORDER BY dp.rating DESC, COUNT(dr.id) DESC
      LIMIT 10
    `);

    // Calculate overall rating (combination of food and driver ratings)
    const foodAvg = parseFloat(foodRatingStats.rows[0]?.average_rating) || 0;
    const driverAvg = parseFloat(driverRatingStats.rows[0]?.average_rating) || 0;
    const overallRating = (foodAvg + driverAvg) / 2;

    res.json({
      averageFoodRating: foodAvg,
      totalFoodRatings: parseInt(foodRatingStats.rows[0]?.total_ratings) || 0,
      averageDriverRating: driverAvg,
      totalDriverRatings: parseInt(driverRatingStats.rows[0]?.total_ratings) || 0,
      overallRating: overallRating,
      topRatedItems: topRatedItems.rows.map(item => ({
        id: item.id,
        name: item.name,
        average_rating: parseFloat(item.average_rating),
        total_ratings: item.total_ratings,
        restaurant_name: item.restaurant_name
      })),
      lowRatedItems: lowRatedItems.rows.map(item => ({
        id: item.id,
        name: item.name,
        average_rating: parseFloat(item.average_rating),
        total_ratings: item.total_ratings,
        restaurant_name: item.restaurant_name
      })),
      topRatedDrivers: topRatedDrivers.rows.map(driver => ({
        id: driver.id,
        full_name: driver.full_name,
        rating: parseFloat(driver.rating),
        total_ratings: parseInt(driver.total_ratings)
      }))
    });

  } catch (error) {
    console.error('Get rating analytics error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get admin settings
router.get('/settings', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const settings = await query('SELECT * FROM admin_settings ORDER BY setting_key');

    const settingsObj = {};
    settings.rows.forEach(setting => {
      settingsObj[setting.setting_key] = {
        value: setting.setting_value,
        description: setting.description,
        updatedAt: setting.updated_at
      };
    });

    res.json({ settings: settingsObj });
  } catch (error) {
    console.error('Get admin settings error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update admin setting
router.put('/settings/:key', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { key } = req.params;
    const { value } = req.body;

    if (!value) {
      return res.status(400).json({ error: 'Setting value is required' });
    }

    const result = await query(
      `UPDATE admin_settings
       SET setting_value = $1, updated_at = CURRENT_TIMESTAMP
       WHERE setting_key = $2
       RETURNING *`,
      [value, key]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Setting not found' });
    }

    res.json({
      message: 'Setting updated successfully',
      setting: {
        key: result.rows[0].setting_key,
        value: result.rows[0].setting_value,
        description: result.rows[0].description,
        updatedAt: result.rows[0].updated_at
      }
    });
  } catch (error) {
    console.error('Update admin setting error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// ============================================================================
// DISCOUNT MANAGEMENT ENDPOINTS (Admin)
// ============================================================================

// Test endpoint to check discount table
router.get('/discounts/test', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    // Check if discounts table exists
    const tableCheck = await query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'discounts'
      )
    `);

    if (!tableCheck.rows[0].exists) {
      return res.status(500).json({
        error: 'Discounts table does not exist',
        suggestion: 'Run database migrations first'
      });
    }

    // Try to get count of discounts
    const countResult = await query('SELECT COUNT(*) as count FROM discounts');

    res.json({
      success: true,
      tableExists: true,
      discountCount: parseInt(countResult.rows[0].count),
      message: 'Discount system is working'
    });

  } catch (error) {
    console.error('Discount test error:', error);
    res.status(500).json({
      error: 'Database error',
      details: error.message
    });
  }
});

// Get all discounts (Admin only)
router.get('/discounts', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      scope,
      status,
      restaurant_id,
      search
    } = req.query;

    const offset = (page - 1) * limit;
    let whereConditions = [];
    let queryParams = [];
    let paramCount = 0;

    // Build dynamic WHERE clause
    if (scope) {
      paramCount++;
      whereConditions.push(`d.scope = $${paramCount}`);
      queryParams.push(scope);
    }

    if (status === 'active') {
      whereConditions.push(`d.is_active = true AND d.start_date <= NOW() AND d.end_date > NOW()`);
    } else if (status === 'inactive') {
      whereConditions.push(`d.is_active = false OR d.start_date > NOW() OR d.end_date <= NOW()`);
    }

    if (restaurant_id) {
      paramCount++;
      whereConditions.push(`d.restaurant_id = $${paramCount}`);
      queryParams.push(restaurant_id);
    }

    if (search) {
      paramCount++;
      whereConditions.push(`(d.name ILIKE $${paramCount} OR d.code ILIKE $${paramCount} OR d.description ILIKE $${paramCount})`);
      queryParams.push(`%${search}%`);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get discounts with usage statistics (simplified query)
    const discountsResult = await query(`
      SELECT
        d.*,
        COALESCE(usage_stats.total_usage, 0) as total_usage,
        COALESCE(usage_stats.total_savings, 0) as total_savings
      FROM discounts d
      LEFT JOIN (
        SELECT
          discount_id,
          COUNT(*) as total_usage,
          SUM(discount_amount) as total_savings
        FROM discount_usage
        GROUP BY discount_id
      ) usage_stats ON d.id = usage_stats.discount_id
      ${whereClause}
      ORDER BY d.created_at DESC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `, [...queryParams, limit, offset]);

    // Get total count
    const countResult = await query(`
      SELECT COUNT(*) as total
      FROM discounts d
      ${whereClause}
    `, queryParams);

    const discounts = discountsResult.rows.map(row => ({
      id: row.id,
      code: row.code || '',
      name: row.name || '',
      description: row.description || '',
      discountType: row.discount_type || 'percentage',
      discountValue: parseFloat(row.discount_value) || 0,
      scope: row.scope || 'platform',
      restaurantId: row.restaurant_id,
      restaurantName: row.restaurant_name || '',
      menuItemId: row.menu_item_id,
      menuItemName: row.menu_item_name || '',
      minimumOrderAmount: parseFloat(row.minimum_order_amount) || 0,
      maximumDiscountAmount: row.maximum_discount_amount ? parseFloat(row.maximum_discount_amount) : null,
      usageLimit: row.usage_limit,
      usageLimitPerCustomer: parseInt(row.usage_limit_per_customer) || 1,
      startDate: row.start_date,
      endDate: row.end_date,
      isActive: Boolean(row.is_active),
      createdByAdmin: Boolean(row.created_by_admin),
      totalUsage: parseInt(row.total_usage) || 0,
      totalSavings: parseFloat(row.total_savings) || 0,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }));

    res.json({
      discounts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(countResult.rows[0].total),
        totalPages: Math.ceil(countResult.rows[0].total / limit)
      }
    });

  } catch (error) {
    console.error('Get discounts error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create discount (Admin only)
router.post('/discounts', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const {
      code,
      name,
      description,
      discountType,
      discountValue,
      scope,
      restaurantId,
      menuItemId,
      minimumOrderAmount = 0,
      maximumDiscountAmount,
      usageLimit,
      usageLimitPerCustomer = 1,
      startDate,
      endDate,
      timeRestrictions = []
    } = req.body;

    // Validation
    if (!name || !discountType || !discountValue || !scope || !startDate || !endDate) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Validate discount type and value
    if (discountType === 'percentage' && (discountValue <= 0 || discountValue > 100)) {
      return res.status(400).json({ error: 'Percentage discount must be between 0 and 100' });
    }

    if (discountType === 'fixed_amount' && discountValue <= 0) {
      return res.status(400).json({ error: 'Fixed amount discount must be greater than 0' });
    }

    // Validate scope-specific requirements
    if (scope === 'restaurant' && !restaurantId) {
      return res.status(400).json({ error: 'Restaurant ID required for restaurant scope' });
    }

    if (scope === 'menu_item' && (!menuItemId || !restaurantId)) {
      return res.status(400).json({ error: 'Menu item ID and restaurant ID required for menu item scope' });
    }

    // Validate dates
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return res.status(400).json({ error: 'Invalid date format' });
    }

    if (end <= start) {
      return res.status(400).json({ error: 'End date must be after start date' });
    }

    // Check if code is unique (if provided)
    if (code) {
      const existingDiscount = await query('SELECT id FROM discounts WHERE code = $1', [code]);
      if (existingDiscount.rows.length > 0) {
        return res.status(400).json({ error: 'Discount code already exists' });
      }
    }

    // Create discount
    const discountResult = await query(`
      INSERT INTO discounts (
        code, name, description, discount_type, discount_value, scope,
        restaurant_id, menu_item_id, minimum_order_amount, maximum_discount_amount,
        usage_limit, usage_limit_per_customer, start_date, end_date, created_by_admin
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, true)
      RETURNING *
    `, [
      code, name, description, discountType, discountValue, scope,
      restaurantId, menuItemId, minimumOrderAmount, maximumDiscountAmount,
      usageLimit, usageLimitPerCustomer, startDate, endDate
    ]);

    const discount = discountResult.rows[0];

    // Add time restrictions if provided
    if (timeRestrictions.length > 0) {
      for (const restriction of timeRestrictions) {
        await query(`
          INSERT INTO discount_time_restrictions (discount_id, day_of_week, start_time, end_time)
          VALUES ($1, $2, $3, $4)
        `, [discount.id, restriction.dayOfWeek, restriction.startTime, restriction.endTime]);
      }
    }

    res.status(201).json({
      message: 'Discount created successfully',
      discount: {
        id: discount.id,
        code: discount.code,
        name: discount.name,
        discountType: discount.discount_type,
        discountValue: parseFloat(discount.discount_value),
        scope: discount.scope,
        createdAt: discount.created_at
      }
    });

  } catch (error) {
    console.error('Create discount error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get single discount (Admin only)
router.get('/discounts/:discountId', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { discountId } = req.params;

    const discountResult = await query(`
      SELECT
        d.*,
        rp.restaurant_name,
        mi.name as menu_item_name,
        COALESCE(usage_stats.total_usage, 0) as total_usage,
        COALESCE(usage_stats.total_savings, 0) as total_savings
      FROM discounts d
      LEFT JOIN restaurant_profiles rp ON d.restaurant_id = rp.id
      LEFT JOIN menu_items mi ON d.menu_item_id = mi.id
      LEFT JOIN (
        SELECT
          discount_id,
          COUNT(*) as total_usage,
          SUM(discount_amount) as total_savings
        FROM discount_usage
        GROUP BY discount_id
      ) usage_stats ON d.id = usage_stats.discount_id
      WHERE d.id = $1
    `, [discountId]);

    if (discountResult.rows.length === 0) {
      return res.status(404).json({ error: 'Discount not found' });
    }

    // Get time restrictions
    const timeRestrictionsResult = await query(`
      SELECT day_of_week, start_time, end_time
      FROM discount_time_restrictions
      WHERE discount_id = $1
      ORDER BY day_of_week, start_time
    `, [discountId]);

    const row = discountResult.rows[0];
    const discount = {
      id: row.id,
      code: row.code,
      name: row.name,
      description: row.description,
      discountType: row.discount_type,
      discountValue: parseFloat(row.discount_value),
      scope: row.scope,
      restaurantId: row.restaurant_id,
      restaurantName: row.restaurant_name,
      menuItemId: row.menu_item_id,
      menuItemName: row.menu_item_name,
      minimumOrderAmount: parseFloat(row.minimum_order_amount),
      maximumDiscountAmount: row.maximum_discount_amount ? parseFloat(row.maximum_discount_amount) : null,
      usageLimit: row.usage_limit,
      usageLimitPerCustomer: row.usage_limit_per_customer,
      startDate: row.start_date,
      endDate: row.end_date,
      isActive: row.is_active,
      createdByAdmin: row.created_by_admin,
      totalUsage: parseInt(row.total_usage),
      totalSavings: parseFloat(row.total_savings),
      timeRestrictions: timeRestrictionsResult.rows.map(tr => ({
        dayOfWeek: tr.day_of_week,
        startTime: tr.start_time,
        endTime: tr.end_time
      })),
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };

    res.json({ discount });

  } catch (error) {
    console.error('Get discount error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update discount (Admin only)
router.put('/discounts/:discountId', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { discountId } = req.params;
    const {
      code,
      name,
      description,
      discountType,
      discountValue,
      minimumOrderAmount,
      maximumDiscountAmount,
      usageLimit,
      usageLimitPerCustomer,
      startDate,
      endDate,
      isActive,
      timeRestrictions = []
    } = req.body;

    // Check if discount exists
    const existingDiscount = await query('SELECT * FROM discounts WHERE id = $1', [discountId]);
    if (existingDiscount.rows.length === 0) {
      return res.status(404).json({ error: 'Discount not found' });
    }

    // Check if code is unique (if changed)
    if (code && code !== existingDiscount.rows[0].code) {
      const codeCheck = await query('SELECT id FROM discounts WHERE code = $1 AND id != $2', [code, discountId]);
      if (codeCheck.rows.length > 0) {
        return res.status(400).json({ error: 'Discount code already exists' });
      }
    }

    // Update discount
    const updateResult = await query(`
      UPDATE discounts SET
        code = COALESCE($1, code),
        name = COALESCE($2, name),
        description = COALESCE($3, description),
        discount_type = COALESCE($4, discount_type),
        discount_value = COALESCE($5, discount_value),
        minimum_order_amount = COALESCE($6, minimum_order_amount),
        maximum_discount_amount = $7,
        usage_limit = $8,
        usage_limit_per_customer = COALESCE($9, usage_limit_per_customer),
        start_date = COALESCE($10, start_date),
        end_date = COALESCE($11, end_date),
        is_active = COALESCE($12, is_active),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $13
      RETURNING *
    `, [
      code, name, description, discountType, discountValue,
      minimumOrderAmount, maximumDiscountAmount, usageLimit,
      usageLimitPerCustomer, startDate, endDate, isActive, discountId
    ]);

    // Update time restrictions
    if (timeRestrictions !== undefined) {
      // Delete existing restrictions
      await query('DELETE FROM discount_time_restrictions WHERE discount_id = $1', [discountId]);

      // Add new restrictions
      for (const restriction of timeRestrictions) {
        await query(`
          INSERT INTO discount_time_restrictions (discount_id, day_of_week, start_time, end_time)
          VALUES ($1, $2, $3, $4)
        `, [discountId, restriction.dayOfWeek, restriction.startTime, restriction.endTime]);
      }
    }

    res.json({
      message: 'Discount updated successfully',
      discount: {
        id: updateResult.rows[0].id,
        code: updateResult.rows[0].code,
        name: updateResult.rows[0].name,
        updatedAt: updateResult.rows[0].updated_at
      }
    });

  } catch (error) {
    console.error('Update discount error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete discount (Admin only)
router.delete('/discounts/:discountId', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { discountId } = req.params;

    // Check if discount exists
    const existingDiscount = await query('SELECT id FROM discounts WHERE id = $1', [discountId]);
    if (existingDiscount.rows.length === 0) {
      return res.status(404).json({ error: 'Discount not found' });
    }

    // Check if discount has been used
    const usageCheck = await query('SELECT COUNT(*) as usage_count FROM discount_usage WHERE discount_id = $1', [discountId]);
    const usageCount = parseInt(usageCheck.rows[0].usage_count);

    if (usageCount > 0) {
      // Don't delete if used, just deactivate
      await query('UPDATE discounts SET is_active = false WHERE id = $1', [discountId]);
      return res.json({
        message: 'Discount has been deactivated (cannot delete used discounts)',
        deactivated: true
      });
    }

    // Delete discount (this will cascade to time restrictions and usage records)
    await query('DELETE FROM discounts WHERE id = $1', [discountId]);

    res.json({
      message: 'Discount deleted successfully',
      deleted: true
    });

  } catch (error) {
    console.error('Delete discount error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get discount analytics
router.get('/discounts/analytics', authenticateToken, requireRole('admin'), async (req, res) => {
  try {
    const { period = '30d', discountId } = req.query;

    // Calculate date range based on period
    let startDate;
    const endDate = new Date();

    switch (period) {
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    // Base query conditions
    let whereClause = 'WHERE du.created_at >= $1 AND du.created_at <= $2';
    let queryParams = [startDate, endDate];

    if (discountId) {
      whereClause += ' AND du.discount_id = $3';
      queryParams.push(discountId);
    }

    // Overall analytics
    const overallResult = await query(`
      SELECT
        COUNT(du.id) as total_usage,
        SUM(du.discount_amount) as total_savings,
        AVG(du.discount_amount) as avg_discount_amount,
        COUNT(DISTINCT du.customer_id) as unique_customers,
        COUNT(DISTINCT du.discount_id) as unique_discounts,
        AVG(du.original_amount) as avg_order_value
      FROM discount_usage du
      ${whereClause}
    `, queryParams);

    // Usage by discount type
    const typeResult = await query(`
      SELECT
        d.discount_type,
        COUNT(du.id) as usage_count,
        SUM(du.discount_amount) as total_savings,
        AVG(du.discount_amount) as avg_discount_amount
      FROM discount_usage du
      JOIN discounts d ON du.discount_id = d.id
      ${whereClause}
      GROUP BY d.discount_type
      ORDER BY usage_count DESC
    `, queryParams);

    // Top performing discounts
    const topDiscountsResult = await query(`
      SELECT
        d.id,
        d.name,
        d.discount_type,
        d.discount_value,
        COUNT(du.id) as usage_count,
        SUM(du.discount_amount) as total_savings,
        AVG(du.discount_amount) as avg_discount_amount,
        COUNT(DISTINCT du.customer_id) as unique_customers,
        (COUNT(du.id)::float / NULLIF(d.usage_limit, 0)) * 100 as usage_rate
      FROM discount_usage du
      JOIN discounts d ON du.discount_id = d.id
      ${whereClause}
      GROUP BY d.id, d.name, d.discount_type, d.discount_value, d.usage_limit
      ORDER BY usage_count DESC
      LIMIT 10
    `, queryParams);

    // Daily usage trends
    const trendsResult = await query(`
      SELECT
        DATE(du.created_at) as date,
        COUNT(du.id) as usage_count,
        SUM(du.discount_amount) as total_savings,
        COUNT(DISTINCT du.customer_id) as unique_customers
      FROM discount_usage du
      ${whereClause}
      GROUP BY DATE(du.created_at)
      ORDER BY date DESC
      LIMIT 30
    `, queryParams);

    // Customer behavior analytics
    const customerBehaviorResult = await query(`
      SELECT
        AVG(customer_stats.usage_count) as avg_usage_per_customer,
        AVG(customer_stats.total_savings) as avg_savings_per_customer,
        COUNT(CASE WHEN customer_stats.usage_count = 1 THEN 1 END) as one_time_users,
        COUNT(CASE WHEN customer_stats.usage_count > 1 THEN 1 END) as repeat_users
      FROM (
        SELECT
          du.customer_id,
          COUNT(du.id) as usage_count,
          SUM(du.discount_amount) as total_savings
        FROM discount_usage du
        ${whereClause}
        GROUP BY du.customer_id
      ) customer_stats
    `, queryParams);

    res.json({
      success: true,
      data: {
        period: period,
        dateRange: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString()
        },
        overview: overallResult.rows[0] || {},
        discountTypes: typeResult.rows,
        topDiscounts: topDiscountsResult.rows,
        dailyTrends: trendsResult.rows,
        customerBehavior: customerBehaviorResult.rows[0] || {}
      }
    });

  } catch (error) {
    console.error('Get discount analytics error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get discount performance report
router.get('/discounts/:discountId/performance', authenticateToken, requireRole('admin'), async (req, res) => {
  try {
    const { discountId } = req.params;
    const { period = '30d' } = req.query;

    // Calculate date range
    let startDate;
    const endDate = new Date();

    switch (period) {
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    // Get discount details
    const discountResult = await query(`
      SELECT d.*, r.name as restaurant_name, mi.name as menu_item_name
      FROM discounts d
      LEFT JOIN restaurants r ON d.restaurant_id = r.id
      LEFT JOIN menu_items mi ON d.menu_item_id = mi.id
      WHERE d.id = $1
    `, [discountId]);

    if (discountResult.rows.length === 0) {
      return res.status(404).json({ error: 'Discount not found' });
    }

    const discount = discountResult.rows[0];

    // Performance metrics
    const performanceResult = await query(`
      SELECT
        COUNT(du.id) as total_usage,
        SUM(du.discount_amount) as total_savings,
        AVG(du.discount_amount) as avg_discount_amount,
        COUNT(DISTINCT du.customer_id) as unique_customers,
        AVG(du.original_amount) as avg_order_value,
        MIN(du.created_at) as first_usage,
        MAX(du.created_at) as last_usage
      FROM discount_usage du
      WHERE du.discount_id = $1
      AND du.created_at >= $2
      AND du.created_at <= $3
    `, [discountId, startDate, endDate]);

    // Daily usage breakdown
    const dailyUsageResult = await query(`
      SELECT
        DATE(du.created_at) as date,
        COUNT(du.id) as usage_count,
        SUM(du.discount_amount) as total_savings,
        COUNT(DISTINCT du.customer_id) as unique_customers
      FROM discount_usage du
      WHERE du.discount_id = $1
      AND du.created_at >= $2
      AND du.created_at <= $3
      GROUP BY DATE(du.created_at)
      ORDER BY date DESC
    `, [discountId, startDate, endDate]);

    // Customer usage patterns
    const customerPatternsResult = await query(`
      SELECT
        c.name as customer_name,
        c.email as customer_email,
        COUNT(du.id) as usage_count,
        SUM(du.discount_amount) as total_savings,
        AVG(du.original_amount) as avg_order_value,
        MAX(du.created_at) as last_usage
      FROM discount_usage du
      JOIN customers c ON du.customer_id = c.id
      WHERE du.discount_id = $1
      AND du.created_at >= $2
      AND du.created_at <= $3
      GROUP BY c.id, c.name, c.email
      ORDER BY usage_count DESC, total_savings DESC
      LIMIT 20
    `, [discountId, startDate, endDate]);

    res.json({
      success: true,
      data: {
        discount: discount,
        period: period,
        dateRange: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString()
        },
        performance: performanceResult.rows[0] || {},
        dailyUsage: dailyUsageResult.rows,
        customerPatterns: customerPatternsResult.rows
      }
    });

  } catch (error) {
    console.error('Get discount performance error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// ============================================================================
// MART MANAGEMENT ENDPOINTS (Admin)
// ============================================================================

// Get all mart categories (Admin only)
router.get('/mart/categories', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const result = await query(`
      SELECT
        mc.*,
        COUNT(mp.id) as product_count
      FROM mart_categories mc
      LEFT JOIN mart_products mp ON mc.id = mp.category_id AND mp.is_available = true
      GROUP BY mc.id
      ORDER BY mc.sort_order ASC, mc.name ASC
    `);

    res.json({
      categories: result.rows.map(row => ({
        id: row.id,
        name: row.name,
        description: row.description,
        imageUrl: row.image_url,
        isActive: row.is_active,
        sortOrder: row.sort_order,
        productCount: parseInt(row.product_count),
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }))
    });
  } catch (error) {
    console.error('Error fetching mart categories:', error);
    res.status(500).json({ error: 'Failed to fetch categories' });
  }
});

// Create mart category (Admin only)
router.post('/mart/categories', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { name, description, imageUrl, sortOrder = 0 } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Category name is required' });
    }

    const result = await query(`
      INSERT INTO mart_categories (name, description, image_url, sort_order)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `, [name, description, imageUrl, sortOrder]);

    res.status(201).json({
      message: 'Category created successfully',
      category: result.rows[0]
    });
  } catch (error) {
    console.error('Error creating mart category:', error);
    res.status(500).json({ error: 'Failed to create category' });
  }
});

// Update mart category (Admin only)
router.patch('/mart/categories/:categoryId', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { categoryId } = req.params;
    const { name, description, imageUrl, isActive, sortOrder } = req.body;

    const result = await query(`
      UPDATE mart_categories
      SET
        name = COALESCE($1, name),
        description = COALESCE($2, description),
        image_url = COALESCE($3, image_url),
        is_active = COALESCE($4, is_active),
        sort_order = COALESCE($5, sort_order),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $6
      RETURNING *
    `, [name, description, imageUrl, isActive, sortOrder, categoryId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Category not found' });
    }

    res.json({
      message: 'Category updated successfully',
      category: result.rows[0]
    });
  } catch (error) {
    console.error('Error updating mart category:', error);
    res.status(500).json({ error: 'Failed to update category' });
  }
});

// Delete mart category (Admin only)
router.delete('/mart/categories/:categoryId', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { categoryId } = req.params;

    // Check if category has products
    const productCheck = await query(
      'SELECT COUNT(*) as count FROM mart_products WHERE category_id = $1',
      [categoryId]
    );

    if (parseInt(productCheck.rows[0].count) > 0) {
      return res.status(400).json({
        error: 'Cannot delete category with products. Move or delete products first.'
      });
    }

    const result = await query(
      'DELETE FROM mart_categories WHERE id = $1 RETURNING id',
      [categoryId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Category not found' });
    }

    res.json({ message: 'Category deleted successfully' });
  } catch (error) {
    console.error('Error deleting mart category:', error);
    res.status(500).json({ error: 'Failed to delete category' });
  }
});

// Get all mart products (Admin only)
router.get('/mart/products', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { page = 1, limit = 20, categoryId, search, isAvailable } = req.query;
    const offset = (page - 1) * limit;

    let whereConditions = [];
    let queryParams = [];
    let paramCount = 0;

    if (categoryId) {
      paramCount++;
      whereConditions.push(`mp.category_id = $${paramCount}`);
      queryParams.push(categoryId);
    }

    if (search) {
      paramCount++;
      whereConditions.push(`(mp.name ILIKE $${paramCount} OR mp.description ILIKE $${paramCount} OR mp.brand ILIKE $${paramCount})`);
      queryParams.push(`%${search}%`);
    }

    if (isAvailable !== undefined) {
      paramCount++;
      whereConditions.push(`mp.is_available = $${paramCount}`);
      queryParams.push(isAvailable === 'true');
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    const result = await query(`
      SELECT
        mp.*,
        mc.name as category_name
      FROM mart_products mp
      LEFT JOIN mart_categories mc ON mp.category_id = mc.id
      ${whereClause}
      ORDER BY mp.name ASC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `, [...queryParams, limit, offset]);

    // Get total count
    const countResult = await query(`
      SELECT COUNT(*) as total
      FROM mart_products mp
      ${whereClause}
    `, queryParams);

    res.json({
      products: result.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(countResult.rows[0].total),
        pages: Math.ceil(countResult.rows[0].total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching mart products:', error);
    res.status(500).json({ error: 'Failed to fetch products' });
  }
});

// Create mart product (Admin only)
router.post('/mart/products', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const {
      categoryId,
      name,
      description,
      price,
      imageUrl,
      stockQuantity = 0,
      unit = 'piece',
      barcode,
      brand,
      weight
    } = req.body;

    if (!name || !price) {
      return res.status(400).json({ error: 'Name and price are required' });
    }

    if (price <= 0) {
      return res.status(400).json({ error: 'Price must be greater than 0' });
    }

    const result = await query(`
      INSERT INTO mart_products
      (category_id, name, description, price, image_url, stock_quantity, unit, barcode, brand, weight)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `, [categoryId, name, description, price, imageUrl, stockQuantity, unit, barcode, brand, weight]);

    res.status(201).json({
      message: 'Product created successfully',
      product: result.rows[0]
    });
  } catch (error) {
    console.error('Error creating mart product:', error);
    res.status(500).json({ error: 'Failed to create product' });
  }
});

// Update mart product (Admin only)
router.patch('/mart/products/:productId', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { productId } = req.params;
    const {
      categoryId,
      name,
      description,
      price,
      imageUrl,
      isAvailable,
      stockQuantity,
      unit,
      barcode,
      brand,
      weight
    } = req.body;

    const result = await query(`
      UPDATE mart_products
      SET
        category_id = COALESCE($1, category_id),
        name = COALESCE($2, name),
        description = COALESCE($3, description),
        price = COALESCE($4, price),
        image_url = COALESCE($5, image_url),
        is_available = COALESCE($6, is_available),
        stock_quantity = COALESCE($7, stock_quantity),
        unit = COALESCE($8, unit),
        barcode = COALESCE($9, barcode),
        brand = COALESCE($10, brand),
        weight = COALESCE($11, weight),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $12
      RETURNING *
    `, [categoryId, name, description, price, imageUrl, isAvailable, stockQuantity, unit, barcode, brand, weight, productId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Product not found' });
    }

    res.json({
      message: 'Product updated successfully',
      product: result.rows[0]
    });
  } catch (error) {
    console.error('Error updating mart product:', error);
    res.status(500).json({ error: 'Failed to update product' });
  }
});

// Delete mart product (Admin only)
router.delete('/mart/products/:productId', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { productId } = req.params;

    // Check if product is used in any orders
    const orderCheck = await query(
      'SELECT COUNT(*) as count FROM order_items WHERE mart_product_id = $1',
      [productId]
    );

    if (parseInt(orderCheck.rows[0].count) > 0) {
      // Don't delete, just mark as unavailable
      await query(
        'UPDATE mart_products SET is_available = false WHERE id = $1',
        [productId]
      );
      return res.json({
        message: 'Product marked as unavailable (cannot delete products with order history)'
      });
    }

    const result = await query(
      'DELETE FROM mart_products WHERE id = $1 RETURNING id',
      [productId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Product not found' });
    }

    res.json({ message: 'Product deleted successfully' });
  } catch (error) {
    console.error('Error deleting mart product:', error);
    res.status(500).json({ error: 'Failed to delete product' });
  }
});

// Get all market locations (Admin only)
router.get('/mart/locations', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const result = await query(`
      SELECT
        ml.*,
        COUNT(o.id) as total_orders
      FROM market_locations ml
      LEFT JOIN orders o ON ml.id = o.market_location_id
      GROUP BY ml.id
      ORDER BY ml.name ASC
    `);

    res.json({
      locations: result.rows.map(row => ({
        id: row.id,
        name: row.name,
        address: row.address,
        latitude: parseFloat(row.latitude),
        longitude: parseFloat(row.longitude),
        phone: row.phone,
        operatingHours: row.operating_hours,
        isActive: row.is_active,
        totalOrders: parseInt(row.total_orders),
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }))
    });
  } catch (error) {
    console.error('Error fetching market locations:', error);
    res.status(500).json({ error: 'Failed to fetch market locations' });
  }
});

// Create market location (Admin only)
router.post('/mart/locations', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { name, address, latitude, longitude, phone, operatingHours } = req.body;

    if (!name || !address || !latitude || !longitude) {
      return res.status(400).json({ error: 'Name, address, latitude, and longitude are required' });
    }

    const result = await query(`
      INSERT INTO market_locations (name, address, latitude, longitude, phone, operating_hours)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [name, address, latitude, longitude, phone, operatingHours]);

    res.status(201).json({
      message: 'Market location created successfully',
      location: result.rows[0]
    });
  } catch (error) {
    console.error('Error creating market location:', error);
    res.status(500).json({ error: 'Failed to create market location' });
  }
});

// Update market location (Admin only)
router.patch('/mart/locations/:locationId', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { locationId } = req.params;
    const { name, address, latitude, longitude, phone, operatingHours, isActive } = req.body;

    const result = await query(`
      UPDATE market_locations
      SET
        name = COALESCE($1, name),
        address = COALESCE($2, address),
        latitude = COALESCE($3, latitude),
        longitude = COALESCE($4, longitude),
        phone = COALESCE($5, phone),
        operating_hours = COALESCE($6, operating_hours),
        is_active = COALESCE($7, is_active),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $8
      RETURNING *
    `, [name, address, latitude, longitude, phone, operatingHours, isActive, locationId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Market location not found' });
    }

    res.json({
      message: 'Market location updated successfully',
      location: result.rows[0]
    });
  } catch (error) {
    console.error('Error updating market location:', error);
    res.status(500).json({ error: 'Failed to update market location' });
  }
});

// Get driver assignment statistics (Admin only)
router.get('/mart/assignment-stats', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const stats = await orderAssignmentScheduler.getAssignmentStats();

    res.json({
      success: true,
      stats: stats
    });
  } catch (error) {
    console.error('Error fetching assignment stats:', error);
    res.status(500).json({ error: 'Failed to fetch assignment statistics' });
  }
});

// Manually trigger order assignment (Admin only)
router.post('/mart/trigger-assignment/:orderId', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { orderId } = req.params;
    const { orderType = 'restaurant' } = req.body;

    const success = await orderAssignmentScheduler.triggerOrderAssignment(orderId, orderType);

    if (success) {
      res.json({ message: 'Order assignment triggered successfully' });
    } else {
      res.status(400).json({ error: 'Failed to trigger order assignment' });
    }
  } catch (error) {
    console.error('Error triggering order assignment:', error);
    res.status(500).json({ error: 'Failed to trigger order assignment' });
  }
});

// Run mart migrations manually (Admin only)
router.post('/mart/migrate', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    console.log('🚀 Starting manual mart migrations...');

    // Add order_type column to orders table
    await query(`
      ALTER TABLE orders
      ADD COLUMN IF NOT EXISTS order_type VARCHAR(20) DEFAULT 'restaurant'
      CHECK (order_type IN ('restaurant', 'mart'))
    `);

    // Add other mart-related columns to orders table
    await query(`
      ALTER TABLE orders
      ADD COLUMN IF NOT EXISTS market_location_id INTEGER,
      ADD COLUMN IF NOT EXISTS subtotal_amount DECIMAL(10, 2) DEFAULT 0.00,
      ADD COLUMN IF NOT EXISTS discount_id INTEGER,
      ADD COLUMN IF NOT EXISTS discount_amount DECIMAL(10, 2) DEFAULT 0.00,
      ADD COLUMN IF NOT EXISTS assigned_at TIMESTAMP,
      ADD COLUMN IF NOT EXISTS assignment_expires_at TIMESTAMP
    `);

    console.log('✅ Manual mart migrations completed successfully!');

    res.json({
      success: true,
      message: 'Mart system migrations completed successfully'
    });
  } catch (error) {
    console.error('❌ Manual mart migration failed:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to run mart migrations',
      details: error.message
    });
  }
});

// Debug endpoint to set driver availability (for testing)
router.post('/debug/set-driver-available/:driverId', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { driverId } = req.params;
    const { isAvailable = true } = req.body;

    const result = await query(
      'UPDATE driver_profiles SET is_available = $1 WHERE id = $2 RETURNING *',
      [isAvailable, driverId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Driver not found' });
    }

    res.json({
      message: `Driver availability updated to ${isAvailable}`,
      driver: result.rows[0]
    });
  } catch (error) {
    console.error('Error updating driver availability:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Debug endpoint to list all drivers (for testing)
router.get('/debug/drivers', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const result = await query(
      'SELECT id, full_name, is_available, created_at FROM driver_profiles ORDER BY id'
    );

    res.json({
      drivers: result.rows,
      total: result.rows.length,
      available: result.rows.filter(d => d.is_available).length
    });
  } catch (error) {
    console.error('Error fetching drivers:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Debug endpoint to clean up stale orders (for testing)
router.post('/debug/cleanup-stale-orders', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { hoursOld = 24 } = req.body;

    // Find stale orders (assigned/picked_up for more than X hours)
    const staleOrders = await query(`
      SELECT id, order_number, status, driver_id, created_at
      FROM orders
      WHERE status IN ('assigned', 'picked_up')
      AND created_at < NOW() - INTERVAL '${hoursOld} hours'
      ORDER BY created_at
    `);

    console.log(`🧹 Found ${staleOrders.rows.length} stale orders older than ${hoursOld} hours`);

    // Cancel these stale orders
    const cancelledOrders = [];
    for (const order of staleOrders.rows) {
      await query(
        'UPDATE orders SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        ['cancelled', order.id]
      );
      cancelledOrders.push({
        id: order.id,
        orderNumber: order.order_number,
        previousStatus: order.status,
        driverId: order.driver_id,
        createdAt: order.created_at
      });
      console.log(`🧹 Cancelled stale order ${order.id} (${order.status}) from ${order.created_at}`);
    }

    res.json({
      message: `Cleaned up ${cancelledOrders.length} stale orders`,
      cancelledOrders,
      hoursOld
    });
  } catch (error) {
    console.error('Error cleaning up stale orders:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
