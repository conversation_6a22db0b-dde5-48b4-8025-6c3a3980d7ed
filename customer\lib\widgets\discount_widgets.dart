import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/discount.dart';
import '../shared/app_colors.dart';
import '../shared/app_dimensions.dart';

class DiscountBadge extends StatelessWidget {
  final Discount discount;
  final bool isSmall;

  const DiscountBadge({
    super.key,
    required this.discount,
    this.isSmall = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isSmall ? 6 : 8,
        vertical: isSmall ? 2 : 4,
      ),
      decoration: BoxDecoration(
        color: _getDiscountColor(),
        borderRadius: BorderRadius.circular(isSmall ? 4 : 6),
      ),
      child: Text(
        discount.displayText,
        style: GoogleFonts.poppins(
          fontSize: isSmall ? 10 : 12,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
    );
  }

  Color _getDiscountColor() {
    switch (discount.discountType) {
      case DiscountType.percentage:
        return Colors.red[600]!;
      case DiscountType.fixedAmount:
        return Colors.green[600]!;
      case DiscountType.buyOneGetOne:
        return Colors.orange[600]!;
    }
  }
}

class DiscountCard extends StatelessWidget {
  final Discount discount;
  final VoidCallback? onTap;
  final bool isSelected;

  const DiscountCard({
    super.key,
    required this.discount,
    this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingS,
      ),
      elevation: isSelected ? 4 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected
            ? BorderSide(color: AppColors.primary, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  DiscountBadge(discount: discount),
                  const Spacer(),
                  if (discount.code != null) ...[
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        discount.code!,
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 8),
              Text(
                discount.name,
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              if (discount.description != null) ...[
                const SizedBox(height: 4),
                Text(
                  discount.description!,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.store,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    discount.scopeDisplayText,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const Spacer(),
                  if (discount.minimumOrderAmount > 0) ...[
                    Text(
                      'Min: \$${discount.minimumOrderAmount.toStringAsFixed(2)}',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ],
              ),
              if (discount.usageLimit != null || discount.usageLimitPerCustomer > 1) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 14,
                      color: Colors.grey[500],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _getUsageLimitText(),
                      style: GoogleFonts.poppins(
                        fontSize: 11,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  String _getUsageLimitText() {
    List<String> limits = [];
    
    if (discount.usageLimit != null) {
      final remaining = discount.usageLimit! - (discount.totalUsageCount ?? 0);
      limits.add('$remaining uses left');
    }
    
    if (discount.usageLimitPerCustomer > 1) {
      final customerRemaining = discount.usageLimitPerCustomer - (discount.customerUsageCount ?? 0);
      limits.add('$customerRemaining per customer');
    }
    
    return limits.join(' • ');
  }
}

class DiscountCodeInput extends StatefulWidget {
  final Function(String) onCodeSubmitted;
  final bool isLoading;
  final String? errorMessage;

  const DiscountCodeInput({
    super.key,
    required this.onCodeSubmitted,
    this.isLoading = false,
    this.errorMessage,
  });

  @override
  State<DiscountCodeInput> createState() => _DiscountCodeInputState();
}

class _DiscountCodeInputState extends State<DiscountCodeInput> {
  final TextEditingController _controller = TextEditingController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _controller,
                decoration: InputDecoration(
                  hintText: 'Enter discount code',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 16,
                  ),
                  errorText: widget.errorMessage,
                ),
                textCapitalization: TextCapitalization.characters,
                onSubmitted: (value) {
                  if (value.trim().isNotEmpty) {
                    widget.onCodeSubmitted(value.trim());
                  }
                },
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: widget.isLoading
                  ? null
                  : () {
                      final code = _controller.text.trim();
                      if (code.isNotEmpty) {
                        widget.onCodeSubmitted(code);
                      }
                    },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: widget.isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      'Apply',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ],
        ),
      ],
    );
  }
}

class AppliedDiscountCard extends StatelessWidget {
  final Discount discount;
  final double discountAmount;
  final VoidCallback onRemove;

  const AppliedDiscountCard({
    super.key,
    required this.discount,
    required this.discountAmount,
    required this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingS,
      ),
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Colors.green[50],
        border: Border.all(color: Colors.green[200]!),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.green[600],
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  discount.name,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.green[800],
                  ),
                ),
                Text(
                  'You saved \$${discountAmount.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: onRemove,
            icon: Icon(
              Icons.close,
              color: Colors.green[600],
              size: 20,
            ),
            tooltip: 'Remove discount',
          ),
        ],
      ),
    );
  }
}
