const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

// Initialize Firebase (this will also validate configuration)
require('./config/firebase');

// Import database and routes
const { pool } = require('./config/database');

// Import and start order assignment scheduler
const orderAssignmentScheduler = require('./services/orderAssignmentScheduler');
const authRoutes = require('./routes/auth');
const customerRoutes = require('./routes/customer');
const restaurantRoutes = require('./routes/restaurant');
const driverRoutes = require('./routes/driver');
const adminRoutes = require('./routes/admin');
const uploadRoutes = require('./routes/upload');
const ratingsRoutes = require('./routes/ratings');
const martRoutes = require('./routes/mart');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.get('/', (req, res) => {
  res.json({
    message: 'BRSIMA Backend Server',
    status: 'Running',
    timestamp: new Date().toISOString()
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    uptime: process.uptime(),
    timestamp: new Date().toISOString()
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/customer', customerRoutes);
app.use('/api/restaurant', restaurantRoutes);
app.use('/api/driver', driverRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/ratings', ratingsRoutes);
app.use('/api/mart', martRoutes);

// API info endpoint
app.get('/api', (req, res) => {
  res.json({
    message: 'BRSIMA API v1.0',
    availableEndpoints: [
      'POST /api/auth/register',
      'POST /api/auth/login',
      'GET /api/auth/me',
      'POST /api/customer/profile',
      'GET /api/customer/profile',
      'PATCH /api/customer/location',
      'POST /api/driver/profile',
      'GET /api/driver/profile',
      'PATCH /api/driver/availability',
      'PATCH /api/driver/location',
      'GET /api/driver/available-orders',
      'POST /api/driver/accept-order/:orderId',
      'GET /api/driver/my-orders',
      'PATCH /api/driver/complete-order/:orderId',
      'GET /api/driver/stats'
    ],
    documentation: 'https://github.com/your-repo/brsima-api'
  });
});

// Temporary migration endpoint (no auth required)
app.post('/migrate-mart', async (req, res) => {
  try {
    console.log('🚀 Starting mart migration via API...');

    const { query } = require('./config/database');

    // Add order_type column to orders table
    await query(`
      ALTER TABLE orders
      ADD COLUMN IF NOT EXISTS order_type VARCHAR(20) DEFAULT 'restaurant'
      CHECK (order_type IN ('restaurant', 'mart'))
    `);

    // Add other mart-related columns to orders table
    await query(`
      ALTER TABLE orders
      ADD COLUMN IF NOT EXISTS market_location_id INTEGER,
      ADD COLUMN IF NOT EXISTS subtotal_amount DECIMAL(10, 2) DEFAULT 0.00,
      ADD COLUMN IF NOT EXISTS discount_id INTEGER,
      ADD COLUMN IF NOT EXISTS discount_amount DECIMAL(10, 2) DEFAULT 0.00,
      ADD COLUMN IF NOT EXISTS assigned_at TIMESTAMP,
      ADD COLUMN IF NOT EXISTS assignment_expires_at TIMESTAMP
    `);

    // Create market_locations table
    await query(`
      CREATE TABLE IF NOT EXISTS market_locations (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        address TEXT NOT NULL,
        latitude DECIMAL(10, 8) NOT NULL,
        longitude DECIMAL(11, 8) NOT NULL,
        phone VARCHAR(20),
        operating_hours JSONB,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create mart_categories table
    await query(`
      CREATE TABLE IF NOT EXISTS mart_categories (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        image_url TEXT,
        is_active BOOLEAN DEFAULT true,
        sort_order INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create mart_products table
    await query(`
      CREATE TABLE IF NOT EXISTS mart_products (
        id SERIAL PRIMARY KEY,
        category_id INTEGER REFERENCES mart_categories(id) ON DELETE SET NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        price DECIMAL(10, 2) NOT NULL,
        image_url TEXT,
        is_available BOOLEAN DEFAULT true,
        stock_quantity INTEGER DEFAULT 0,
        unit VARCHAR(50) DEFAULT 'piece',
        barcode VARCHAR(100),
        brand VARCHAR(100),
        weight DECIMAL(8, 2),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create driver_order_assignments table
    await query(`
      CREATE TABLE IF NOT EXISTS driver_order_assignments (
        id SERIAL PRIMARY KEY,
        order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
        driver_id INTEGER REFERENCES driver_profiles(id) ON DELETE CASCADE,
        assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NOT NULL,
        status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'expired')),
        responded_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(order_id, driver_id)
      )
    `);

    // Add mart_product_id column to order_items
    await query(`
      ALTER TABLE order_items
      ADD COLUMN IF NOT EXISTS mart_product_id INTEGER REFERENCES mart_products(id) ON DELETE CASCADE
    `);

    // Insert default categories
    await query(`
      INSERT INTO mart_categories (name, description, sort_order) VALUES
      ('Groceries', 'Fresh groceries and daily essentials', 1),
      ('Beverages', 'Drinks and beverages', 2),
      ('Snacks', 'Snacks and quick bites', 3),
      ('Personal Care', 'Personal care and hygiene products', 4),
      ('Household', 'Household items and cleaning supplies', 5)
      ON CONFLICT DO NOTHING
    `);

    // Insert default market location
    await query(`
      INSERT INTO market_locations (name, address, latitude, longitude, phone, operating_hours) VALUES
      ('Main Market Hub', '123 Market Street, City Center', 40.7128, -74.0060, '+1234567890',
       '{"monday": "06:00-22:00", "tuesday": "06:00-22:00", "wednesday": "06:00-22:00", "thursday": "06:00-22:00", "friday": "06:00-22:00", "saturday": "06:00-22:00", "sunday": "08:00-20:00"}')
      ON CONFLICT DO NOTHING
    `);

    console.log('✅ Mart migration completed successfully via API!');

    res.json({
      success: true,
      message: 'Mart system migrations completed successfully'
    });
  } catch (error) {
    console.error('❌ Mart migration failed via API:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to run mart migrations',
      details: error.message
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Something went wrong!',
    message: err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `Cannot ${req.method} ${req.originalUrl}`
  });
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 BRSIMA Backend Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📱 Android emulator: http://********:${PORT}/health`);

  // Start the order assignment scheduler
  orderAssignmentScheduler.start();
});
