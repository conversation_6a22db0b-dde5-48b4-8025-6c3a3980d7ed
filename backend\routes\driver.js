const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { validateDriverProfile } = require('../middleware/validation');
const driverAssignmentService = require('../services/driverAssignmentService');

const router = express.Router();

// Helper function to calculate distance between two points using Haversine formula
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // Distance in kilometers
  return distance;
}

// Complete driver profile
router.post('/profile', authenticateToken, validateDriverProfile, async (req, res) => {
  try {
    const { fullName, phone, licenseNumber, vehicleType, vehiclePlate } = req.body;
    const userId = req.user.id;

    // Check if profile already exists
    const existingProfile = await query(
      'SELECT id FROM driver_profiles WHERE user_id = $1',
      [userId]
    );

    let result;
    if (existingProfile.rows.length > 0) {
      // Update existing profile
      result = await query(
        `UPDATE driver_profiles 
         SET full_name = $1, phone = $2, license_number = $3, vehicle_type = $4, vehicle_plate = $5, updated_at = CURRENT_TIMESTAMP
         WHERE user_id = $6 
         RETURNING *`,
        [fullName, phone, licenseNumber, vehicleType, vehiclePlate, userId]
      );
    } else {
      // Create new profile
      result = await query(
        `INSERT INTO driver_profiles (user_id, full_name, phone, license_number, vehicle_type, vehicle_plate)
         VALUES ($1, $2, $3, $4, $5, $6) 
         RETURNING *`,
        [userId, fullName, phone, licenseNumber, vehicleType, vehiclePlate]
      );
    }

    res.json({
      message: 'Driver profile completed successfully',
      profile: result.rows[0]
    });
  } catch (error) {
    console.error('Error completing driver profile:', error);
    res.status(500).json({ error: 'Failed to complete driver profile' });
  }
});

// Get driver profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    
    const result = await query(
      'SELECT * FROM driver_profiles WHERE user_id = $1',
      [userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Driver profile not found' });
    }

    res.json({ profile: result.rows[0] });
  } catch (error) {
    console.error('Error fetching driver profile:', error);
    res.status(500).json({ error: 'Failed to fetch driver profile' });
  }
});

// Update driver availability status
router.patch('/availability', authenticateToken, async (req, res) => {
  try {
    const { isAvailable } = req.body;
    const userId = req.user.id;

    const result = await query(
      `UPDATE driver_profiles 
       SET is_available = $1, updated_at = CURRENT_TIMESTAMP
       WHERE user_id = $2 
       RETURNING *`,
      [isAvailable, userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Driver profile not found' });
    }

    res.json({
      message: 'Availability updated successfully',
      profile: result.rows[0]
    });
  } catch (error) {
    console.error('Error updating availability:', error);
    res.status(500).json({ error: 'Failed to update availability' });
  }
});

// Update driver location
router.patch('/location', authenticateToken, async (req, res) => {
  try {
    const { latitude, longitude } = req.body;
    const userId = req.user.id;

    if (!latitude || !longitude) {
      return res.status(400).json({ error: 'Latitude and longitude are required' });
    }

    const result = await query(
      `UPDATE driver_profiles 
       SET current_latitude = $1, current_longitude = $2, updated_at = CURRENT_TIMESTAMP
       WHERE user_id = $3 
       RETURNING *`,
      [latitude, longitude, userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Driver profile not found' });
    }

    res.json({
      message: 'Location updated successfully',
      profile: result.rows[0]
    });
  } catch (error) {
    console.error('Error updating location:', error);
    res.status(500).json({ error: 'Failed to update location' });
  }
});

// Get current assignment for driver (new system)
router.get('/current-assignment', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get driver profile
    const driverResult = await query(
      'SELECT id FROM driver_profiles WHERE user_id = $1',
      [userId]
    );

    if (driverResult.rows.length === 0) {
      return res.status(404).json({ error: 'Driver profile not found' });
    }

    const driverId = driverResult.rows[0].id;

    // Get current assignment
    const assignment = await driverAssignmentService.getCurrentAssignment(driverId);

    if (!assignment) {
      return res.json({ assignment: null });
    }

    // Get full order details
    const orderResult = await query(`
      SELECT
        o.id, o.order_number, o.order_type, o.status, o.total_amount, o.delivery_fee,
        o.delivery_address, o.delivery_latitude, o.delivery_longitude,
        o.special_instructions, o.estimated_delivery_time, o.created_at,
        -- Restaurant details (for restaurant orders)
        rp.restaurant_name, rp.address as restaurant_address, rp.phone as restaurant_phone,
        rp.latitude as restaurant_latitude, rp.longitude as restaurant_longitude,
        -- Market location details (for mart orders)
        ml.name as market_name, ml.address as market_address, ml.phone as market_phone,
        ml.latitude as market_latitude, ml.longitude as market_longitude,
        -- Customer details
        cp.full_name as customer_name, cp.phone as customer_phone
      FROM orders o
      LEFT JOIN restaurant_profiles rp ON o.restaurant_id = rp.id
      LEFT JOIN market_locations ml ON o.market_location_id = ml.id
      JOIN customer_profiles cp ON o.customer_id = cp.id
      WHERE o.id = $1
    `, [assignment.order_id]);

    if (orderResult.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    const order = orderResult.rows[0];

    res.json({
      assignment: {
        ...assignment,
        order: order,
        expires_in_seconds: Math.max(0, Math.floor((new Date(assignment.expires_at) - new Date()) / 1000))
      }
    });
  } catch (error) {
    console.error('Error fetching current assignment:', error);
    res.status(500).json({ error: 'Failed to fetch current assignment' });
  }
});

// Get available orders for pickup (legacy endpoint - kept for backward compatibility)
router.get('/available-orders', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get driver profile
    const driverResult = await query(
      'SELECT id FROM driver_profiles WHERE user_id = $1',
      [userId]
    );

    if (driverResult.rows.length === 0) {
      return res.status(404).json({ error: 'Driver profile not found' });
    }

    const driverId = driverResult.rows[0].id;

    // Check if driver has a current assignment
    const currentAssignment = await driverAssignmentService.getCurrentAssignment(driverId);

    if (currentAssignment) {
      // Return the current assignment as the only "available" order
      const orderResult = await query(`
        SELECT
          o.id, o.order_number, o.order_type, o.status, o.total_amount, o.delivery_fee,
          o.delivery_address, o.delivery_latitude, o.delivery_longitude,
          o.special_instructions, o.estimated_delivery_time, o.created_at,
          -- Restaurant details (for restaurant orders)
          rp.restaurant_name, rp.address as restaurant_address, rp.phone as restaurant_phone,
          rp.latitude as restaurant_latitude, rp.longitude as restaurant_longitude,
          -- Market location details (for mart orders)
          ml.name as market_name, ml.address as market_address, ml.phone as market_phone,
          ml.latitude as market_latitude, ml.longitude as market_longitude,
          -- Customer details
          cp.full_name as customer_name, cp.phone as customer_phone
        FROM orders o
        LEFT JOIN restaurant_profiles rp ON o.restaurant_id = rp.id
        LEFT JOIN market_locations ml ON o.market_location_id = ml.id
        JOIN customer_profiles cp ON o.customer_id = cp.id
        WHERE o.id = $1
      `, [currentAssignment.order_id]);

      return res.json({
        orders: orderResult.rows,
        assignment_expires_at: currentAssignment.expires_at
      });
    }

    // No current assignment, return empty array (new system assigns orders automatically)
    res.json({ orders: [] });
  } catch (error) {
    console.error('Error fetching available orders:', error);
    res.status(500).json({ error: 'Failed to fetch available orders' });
  }
});

// Decline an order (updated for new assignment system)
router.post('/decline-order/:orderId', authenticateToken, async (req, res) => {
  try {
    const { orderId } = req.params;
    const userId = req.user.id;

    // Get driver profile
    const driverResult = await query(
      'SELECT id FROM driver_profiles WHERE user_id = $1',
      [userId]
    );

    if (driverResult.rows.length === 0) {
      return res.status(404).json({ error: 'Driver profile not found' });
    }

    const driverId = driverResult.rows[0].id;

    // Get order type for proper reassignment
    const orderResult = await query(
      'SELECT order_type FROM orders WHERE id = $1',
      [orderId]
    );

    if (orderResult.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    const orderType = orderResult.rows[0].order_type;

    // Use the new assignment service to decline the order
    const declined = await driverAssignmentService.declineOrderAssignment(orderId, driverId, orderType);

    if (!declined) {
      return res.status(400).json({ error: 'Failed to decline order' });
    }

    res.json({ message: 'Order declined successfully' });
  } catch (error) {
    console.error('Error declining order:', error);
    res.status(500).json({ error: 'Failed to decline order' });
  }
});

// Accept an order (updated for new assignment system)
router.post('/accept-order/:orderId', authenticateToken, async (req, res) => {
  try {
    const { orderId } = req.params;
    const userId = req.user.id;

    // Get driver profile
    const driverResult = await query(
      'SELECT id FROM driver_profiles WHERE user_id = $1',
      [userId]
    );

    if (driverResult.rows.length === 0) {
      return res.status(404).json({ error: 'Driver profile not found' });
    }

    const driverId = driverResult.rows[0].id;

    // Use the new assignment service to accept the order
    const accepted = await driverAssignmentService.acceptOrderAssignment(orderId, driverId);

    if (!accepted) {
      return res.status(400).json({ error: 'Order is no longer available or assignment has expired' });
    }

    // Get the complete order details with joined data
    const orderResult = await query(`
      SELECT
        o.id, o.order_number, o.order_type, o.status, o.total_amount, o.delivery_fee,
        o.delivery_address, o.delivery_latitude, o.delivery_longitude,
        o.special_instructions, o.estimated_delivery_time, o.created_at,
        -- Restaurant details (for restaurant orders)
        rp.restaurant_name, rp.address as restaurant_address, rp.phone as restaurant_phone,
        rp.latitude as restaurant_latitude, rp.longitude as restaurant_longitude,
        -- Market location details (for mart orders)
        ml.name as market_name, ml.address as market_address, ml.phone as market_phone,
        ml.latitude as market_latitude, ml.longitude as market_longitude,
        -- Customer details
        cp.full_name as customer_name, cp.phone as customer_phone
      FROM orders o
      LEFT JOIN restaurant_profiles rp ON o.restaurant_id = rp.id
      LEFT JOIN market_locations ml ON o.market_location_id = ml.id
      JOIN customer_profiles cp ON o.customer_id = cp.id
      WHERE o.id = $1
    `, [orderId]);

    if (orderResult.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    res.json({
      message: 'Order accepted successfully',
      order: orderResult.rows[0]
    });
  } catch (error) {
    console.error('Error accepting order:', error);
    res.status(500).json({ error: 'Failed to accept order' });
  }
});

// Get driver's current orders
router.get('/my-orders', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get driver profile
    const driverResult = await query(
      'SELECT id FROM driver_profiles WHERE user_id = $1',
      [userId]
    );

    if (driverResult.rows.length === 0) {
      return res.status(404).json({ error: 'Driver profile not found' });
    }

    const driverId = driverResult.rows[0].id;

    const result = await query(
      `SELECT
         o.id, o.order_number, o.order_type, o.total_amount, o.delivery_fee, o.status,
         o.delivery_address, o.delivery_latitude, o.delivery_longitude,
         o.special_instructions, o.estimated_delivery_time, o.created_at,
         -- Restaurant details (for restaurant orders)
         rp.restaurant_name, rp.address as restaurant_address, rp.phone as restaurant_phone,
         rp.latitude as restaurant_latitude, rp.longitude as restaurant_longitude,
         -- Market location details (for mart orders)
         ml.name as market_name, ml.address as market_address, ml.phone as market_phone,
         ml.latitude as market_latitude, ml.longitude as market_longitude,
         -- Customer details
         cp.full_name as customer_name, cp.phone as customer_phone
       FROM orders o
       LEFT JOIN restaurant_profiles rp ON o.restaurant_id = rp.id
       LEFT JOIN market_locations ml ON o.market_location_id = ml.id
       JOIN customer_profiles cp ON o.customer_id = cp.id
       WHERE o.driver_id = $1 AND o.status IN ('assigned', 'picked_up', 'delivered')
       ORDER BY o.created_at DESC`,
      [driverId]
    );

    res.json({ orders: result.rows });
  } catch (error) {
    console.error('Error fetching driver orders:', error);
    res.status(500).json({ error: 'Failed to fetch driver orders' });
  }
});

// Get specific order details with items
router.get('/orders/:orderId', authenticateToken, async (req, res) => {
  try {
    const { orderId } = req.params;
    const userId = req.user.id;

    // Get driver profile
    const driverResult = await query(
      'SELECT id FROM driver_profiles WHERE user_id = $1',
      [userId]
    );

    if (driverResult.rows.length === 0) {
      return res.status(404).json({ error: 'Driver profile not found' });
    }

    const driverId = driverResult.rows[0].id;

    // Get order details - allow access to orders assigned to this driver or available orders
    const orderResult = await query(
      `SELECT
         o.id, o.order_number, o.order_type, o.status, o.total_amount, o.delivery_fee,
         o.delivery_address, o.delivery_latitude, o.delivery_longitude,
         o.special_instructions, o.estimated_delivery_time,
         o.created_at, o.updated_at,
         -- Restaurant details (for restaurant orders)
         rp.restaurant_name, rp.address as restaurant_address, rp.phone as restaurant_phone,
         rp.latitude as restaurant_latitude, rp.longitude as restaurant_longitude,
         -- Market location details (for mart orders)
         ml.name as market_name, ml.address as market_address, ml.phone as market_phone,
         ml.latitude as market_latitude, ml.longitude as market_longitude,
         -- Customer details
         cp.full_name as customer_name, cp.phone as customer_phone
       FROM orders o
       LEFT JOIN restaurant_profiles rp ON o.restaurant_id = rp.id
       LEFT JOIN market_locations ml ON o.market_location_id = ml.id
       JOIN customer_profiles cp ON o.customer_id = cp.id
       WHERE o.id = $1 AND (o.driver_id = $2 OR (o.status = 'ready' AND o.driver_id IS NULL))`,
      [orderId, driverId]
    );

    if (orderResult.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found or not accessible' });
    }

    const order = orderResult.rows[0];

    // Get order items
    const itemsResult = await query(
      `SELECT
         oi.quantity, oi.unit_price, oi.total_price, oi.special_instructions,
         mi.name, mi.description, mi.image_url
       FROM order_items oi
       JOIN menu_items mi ON oi.menu_item_id = mi.id
       WHERE oi.order_id = $1`,
      [orderId]
    );

    const orderDetails = {
      id: order.id,
      order_number: order.order_number || '',
      status: order.status || 'pending',
      total_amount: order.total_amount || '0',
      delivery_fee: order.delivery_fee || '0',
      delivery_address: order.delivery_address || '',
      delivery_latitude: order.delivery_latitude,
      delivery_longitude: order.delivery_longitude,
      special_instructions: order.special_instructions,
      estimated_delivery_time: order.estimated_delivery_time,
      created_at: order.created_at,
      restaurant_name: order.restaurant_name || '',
      restaurant_address: order.restaurant_address || '',
      restaurant_phone: order.restaurant_phone,
      restaurant_latitude: order.restaurant_latitude,
      restaurant_longitude: order.restaurant_longitude,
      customer_name: order.customer_name || '',
      customer_phone: order.customer_phone || '',
      items: itemsResult.rows.map(item => ({
        name: item.name || '',
        description: item.description || '',
        image_url: item.image_url,
        quantity: item.quantity || 0,
        unit_price: item.unit_price || '0',
        total_price: item.total_price || '0',
        special_instructions: item.special_instructions
      }))
    };

    res.json({ order: orderDetails });

  } catch (error) {
    console.error('Get driver order details error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update order status (picked_up, on_the_way, delivered)
router.patch('/update-order-status/:orderId', authenticateToken, async (req, res) => {
  try {
    const { orderId } = req.params;
    const { status, driverLatitude, driverLongitude } = req.body;
    const userId = req.user.id;

    // Validate status
    const validStatuses = ['assigned', 'picked_up', 'delivered'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: 'Invalid status' });
    }

    // For location-sensitive status updates, validate driver location
    if ((status === 'picked_up' || status === 'delivered') && (!driverLatitude || !driverLongitude)) {
      return res.status(400).json({ error: 'Driver location required for this status update' });
    }

    console.log(`📍 Driver location: ${driverLatitude}, ${driverLongitude}`);
    console.log(`🎯 Target status: ${status}`);

    // Get driver profile
    const driverResult = await query(
      'SELECT id FROM driver_profiles WHERE user_id = $1',
      [userId]
    );

    if (driverResult.rows.length === 0) {
      return res.status(404).json({ error: 'Driver profile not found' });
    }

    const driverId = driverResult.rows[0].id;

    // Get current order details for location validation
    const orderResult = await query(
      `SELECT o.*, r.latitude as restaurant_latitude, r.longitude as restaurant_longitude
       FROM orders o
       JOIN restaurant_profiles r ON o.restaurant_id = r.id
       WHERE o.id = $1 AND o.driver_id = $2`,
      [orderId, driverId]
    );

    if (orderResult.rows.length === 0) {
      return res.status(400).json({ error: 'Order not found or not assigned to you' });
    }

    const order = orderResult.rows[0];

    // Location validation for status updates
    if (status === 'picked_up') {
      // Validate driver is near restaurant (within 50 meters)
      const distance = calculateDistance(
        driverLatitude,
        driverLongitude,
        order.restaurant_latitude,
        order.restaurant_longitude
      );

      console.log(`🏪 Restaurant pickup validation - Distance: ${Math.round(distance * 1000)}m (limit: 50m)`);

      if (distance > 0.05) { // 0.05 km = 50 meters
        console.log(`❌ Driver too far from restaurant: ${Math.round(distance * 1000)}m away`);
        return res.status(400).json({
          error: 'You must be at the restaurant location to mark as picked up',
          distance: Math.round(distance * 1000) + 'm away'
        });
      }

      console.log(`✅ Driver is within range of restaurant`);
    } else if (status === 'delivered') {
      // Validate driver is near customer (within 50 meters)
      if (!order.delivery_latitude || !order.delivery_longitude) {
        return res.status(400).json({ error: 'Customer location not available' });
      }

      const distance = calculateDistance(
        driverLatitude,
        driverLongitude,
        order.delivery_latitude,
        order.delivery_longitude
      );

      console.log(`🏠 Customer delivery validation - Distance: ${Math.round(distance * 1000)}m (limit: 50m)`);

      if (distance > 0.05) { // 0.05 km = 50 meters
        console.log(`❌ Driver too far from customer: ${Math.round(distance * 1000)}m away`);
        return res.status(400).json({
          error: 'You must be at the customer location to mark as delivered',
          distance: Math.round(distance * 1000) + 'm away'
        });
      }

      console.log(`✅ Driver is within range of customer`);
    }

    // Update order status
    const result = await query(
      `UPDATE orders
       SET status = $1, updated_at = CURRENT_TIMESTAMP
       WHERE id = $2 AND driver_id = $3
       RETURNING id`,
      [status, orderId, driverId]
    );

    if (result.rows.length === 0) {
      return res.status(400).json({ error: 'Failed to update order status' });
    }

    // If status is delivered, update driver's total deliveries
    if (status === 'delivered') {
      await query(
        `UPDATE driver_profiles
         SET total_deliveries = total_deliveries + 1, updated_at = CURRENT_TIMESTAMP
         WHERE id = $1`,
        [driverId]
      );
    }

    // Get the complete order details with joined data
    const orderDetailsResult = await query(
      `SELECT
         o.id, o.order_number, o.total_amount, o.delivery_fee, o.status,
         o.delivery_address, o.delivery_latitude, o.delivery_longitude,
         o.special_instructions, o.estimated_delivery_time, o.created_at,
         r.restaurant_name as restaurant_name, r.address as restaurant_address,
         r.phone as restaurant_phone, r.latitude as restaurant_latitude,
         r.longitude as restaurant_longitude,
         cp.full_name as customer_name, cp.phone as customer_phone
       FROM orders o
       JOIN restaurant_profiles r ON o.restaurant_id = r.id
       JOIN customer_profiles cp ON o.customer_id = cp.id
       WHERE o.id = $1`,
      [orderId]
    );

    const orderDetail = orderDetailsResult.rows[0];

    // Get order items
    const itemsResult = await query(
      `SELECT
         oi.quantity, oi.unit_price, oi.total_price, oi.special_instructions,
         mi.name, mi.description, mi.image_url
       FROM order_items oi
       JOIN menu_items mi ON oi.menu_item_id = mi.id
       WHERE oi.order_id = $1`,
      [orderId]
    );

    const orderDetails = {
      id: orderDetail.id,
      orderNumber: orderDetail.order_number,
      totalAmount: parseFloat(orderDetail.total_amount),
      deliveryFee: parseFloat(orderDetail.delivery_fee),
      status: orderDetail.status,
      deliveryAddress: orderDetail.delivery_address,
      deliveryLatitude: orderDetail.delivery_latitude ? parseFloat(orderDetail.delivery_latitude) : null,
      deliveryLongitude: orderDetail.delivery_longitude ? parseFloat(orderDetail.delivery_longitude) : null,
      specialInstructions: orderDetail.special_instructions,
      estimatedDeliveryTime: orderDetail.estimated_delivery_time,
      createdAt: orderDetail.created_at,
      restaurantName: orderDetail.restaurant_name,
      restaurantAddress: orderDetail.restaurant_address,
      restaurantPhone: orderDetail.restaurant_phone,
      restaurantLatitude: orderDetail.restaurant_latitude ? parseFloat(orderDetail.restaurant_latitude) : null,
      restaurantLongitude: orderDetail.restaurant_longitude ? parseFloat(orderDetail.restaurant_longitude) : null,
      customerName: orderDetail.customer_name,
      customerPhone: orderDetail.customer_phone,
      items: itemsResult.rows.map(item => ({
        name: item.name,
        description: item.description,
        imageUrl: item.image_url,
        quantity: item.quantity,
        unitPrice: parseFloat(item.unit_price),
        totalPrice: parseFloat(item.total_price),
        specialInstructions: item.special_instructions
      }))
    };

    res.json({ order: orderDetails });

  } catch (error) {
    console.error('Update order status error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Mark order as delivered
router.patch('/complete-order/:orderId', authenticateToken, async (req, res) => {
  try {
    const { orderId } = req.params;
    const userId = req.user.id;

    // Get driver profile
    const driverResult = await query(
      'SELECT id FROM driver_profiles WHERE user_id = $1',
      [userId]
    );

    if (driverResult.rows.length === 0) {
      return res.status(404).json({ error: 'Driver profile not found' });
    }

    const driverId = driverResult.rows[0].id;

    // Update order status
    const result = await query(
      `UPDATE orders
       SET status = 'delivered', updated_at = CURRENT_TIMESTAMP
       WHERE id = $1 AND driver_id = $2 AND status = 'picked_up'
       RETURNING id`,
      [orderId, driverId]
    );

    if (result.rows.length === 0) {
      return res.status(400).json({ error: 'Order not found or cannot be completed' });
    }

    // Update driver's total deliveries
    await query(
      `UPDATE driver_profiles
       SET total_deliveries = total_deliveries + 1, updated_at = CURRENT_TIMESTAMP
       WHERE id = $1`,
      [driverId]
    );

    // Get the complete order details with joined data
    const orderResult = await query(
      `SELECT
         o.id, o.order_number, o.status, o.total_amount, o.delivery_fee,
         o.delivery_address, o.delivery_latitude, o.delivery_longitude,
         o.special_instructions, o.estimated_delivery_time, o.created_at,
         rp.restaurant_name, rp.address as restaurant_address, rp.phone as restaurant_phone,
         rp.latitude as restaurant_latitude, rp.longitude as restaurant_longitude,
         cp.full_name as customer_name, cp.phone as customer_phone
       FROM orders o
       JOIN restaurant_profiles rp ON o.restaurant_id = rp.id
       JOIN customer_profiles cp ON o.customer_id = cp.id
       WHERE o.id = $1`,
      [orderId]
    );

    res.json({
      message: 'Order completed successfully',
      order: orderResult.rows[0]
    });
  } catch (error) {
    console.error('Error completing order:', error);
    res.status(500).json({ error: 'Failed to complete order' });
  }
});

// Get driver statistics
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get driver profile
    const driverResult = await query(
      'SELECT id, total_deliveries, rating FROM driver_profiles WHERE user_id = $1',
      [userId]
    );

    if (driverResult.rows.length === 0) {
      return res.status(404).json({ error: 'Driver profile not found' });
    }

    const driverId = driverResult.rows[0].id;

    // Get earnings for current month
    const currentMonth = new Date();
    const firstDay = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
    const lastDay = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);

    const earningsResult = await query(
      `SELECT COALESCE(SUM(delivery_fee), 0) as monthly_earnings
       FROM orders 
       WHERE driver_id = $1 AND status = 'delivered' 
       AND created_at >= $2 AND created_at <= $3`,
      [driverId, firstDay, lastDay]
    );

    // Get today's deliveries
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayDeliveriesResult = await query(
      `SELECT COUNT(*) as today_deliveries
       FROM orders 
       WHERE driver_id = $1 AND status = 'delivered' 
       AND created_at >= $2 AND created_at < $3`,
      [driverId, today, tomorrow]
    );

    res.json({
      totalDeliveries: driverResult.rows[0].total_deliveries,
      rating: driverResult.rows[0].rating,
      monthlyEarnings: parseFloat(earningsResult.rows[0].monthly_earnings),
      todayDeliveries: parseInt(todayDeliveriesResult.rows[0].today_deliveries)
    });
  } catch (error) {
    console.error('Error fetching driver stats:', error);
    res.status(500).json({ error: 'Failed to fetch driver statistics' });
  }
});

module.exports = router;
