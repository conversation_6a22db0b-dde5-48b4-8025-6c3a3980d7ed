const driverAssignmentService = require('./driverAssignmentService');
const { query } = require('../config/database');

class OrderAssignmentScheduler {
  constructor() {
    this.cleanupInterval = null;
    this.assignmentInterval = null;
    this.isRunning = false;
  }

  /**
   * Start the scheduler
   */
  start() {
    if (this.isRunning) {
      console.log('📅 Order assignment scheduler is already running');
      return;
    }

    console.log('🚀 Starting order assignment scheduler...');
    this.isRunning = true;

    // Clean up expired assignments every 30 seconds
    this.cleanupInterval = setInterval(async () => {
      await this.cleanupExpiredAssignments();
    }, 30 * 1000);

    // Check for unassigned orders every 10 seconds
    this.assignmentInterval = setInterval(async () => {
      await this.assignUnassignedOrders();
    }, 10 * 1000);

    console.log('✅ Order assignment scheduler started');
  }

  /**
   * Stop the scheduler
   */
  stop() {
    if (!this.isRunning) {
      console.log('📅 Order assignment scheduler is not running');
      return;
    }

    console.log('🛑 Stopping order assignment scheduler...');

    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    if (this.assignmentInterval) {
      clearInterval(this.assignmentInterval);
      this.assignmentInterval = null;
    }

    this.isRunning = false;
    console.log('✅ Order assignment scheduler stopped');
  }

  /**
   * Clean up expired assignments
   */
  async cleanupExpiredAssignments() {
    try {
      await driverAssignmentService.cleanupExpiredAssignments();
    } catch (error) {
      console.error('Error in cleanup expired assignments:', error);
    }
  }

  /**
   * Check for unassigned orders and assign them
   */
  async assignUnassignedOrders() {
    try {
      // Get orders that are ready but not assigned or have expired assignments
      const unassignedOrders = await query(`
        SELECT o.id, o.order_type
        FROM orders o
        WHERE o.status = 'ready'
        AND (
          o.assigned_at IS NULL 
          OR o.assignment_expires_at IS NULL 
          OR o.assignment_expires_at < CURRENT_TIMESTAMP
        )
        AND NOT EXISTS (
          SELECT 1 FROM driver_order_assignments doa 
          WHERE doa.order_id = o.id 
          AND doa.status = 'pending' 
          AND doa.expires_at > CURRENT_TIMESTAMP
        )
        ORDER BY o.created_at ASC
      `);

      if (unassignedOrders.rows.length > 0) {
        console.log(`📋 Found ${unassignedOrders.rows.length} unassigned orders`);

        for (const order of unassignedOrders.rows) {
          const assigned = await driverAssignmentService.assignOrderToNextDriver(
            order.id, 
            order.order_type
          );

          if (assigned) {
            console.log(`✅ Assigned order ${order.id} (${order.order_type})`);
          } else {
            console.log(`❌ Could not assign order ${order.id} (${order.order_type})`);
          }

          // Small delay between assignments to prevent overwhelming the system
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }
    } catch (error) {
      console.error('Error in assign unassigned orders:', error);
    }
  }

  /**
   * Manually trigger assignment for a specific order
   * @param {number} orderId - The order ID
   * @param {string} orderType - 'restaurant' or 'mart'
   */
  async triggerOrderAssignment(orderId, orderType = 'restaurant') {
    try {
      console.log(`🎯 Manually triggering assignment for order ${orderId}`);
      const assigned = await driverAssignmentService.assignOrderToNextDriver(orderId, orderType);
      
      if (assigned) {
        console.log(`✅ Successfully assigned order ${orderId}`);
        return true;
      } else {
        console.log(`❌ Failed to assign order ${orderId}`);
        return false;
      }
    } catch (error) {
      console.error(`Error manually assigning order ${orderId}:`, error);
      return false;
    }
  }

  /**
   * Get scheduler status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      cleanupInterval: this.cleanupInterval !== null,
      assignmentInterval: this.assignmentInterval !== null,
      uptime: this.isRunning ? Date.now() - this.startTime : 0
    };
  }

  /**
   * Get assignment statistics
   */
  async getAssignmentStats() {
    try {
      const stats = await query(`
        SELECT 
          COUNT(*) FILTER (WHERE status = 'pending') as pending_assignments,
          COUNT(*) FILTER (WHERE status = 'accepted') as accepted_assignments,
          COUNT(*) FILTER (WHERE status = 'declined') as declined_assignments,
          COUNT(*) FILTER (WHERE status = 'expired') as expired_assignments,
          COUNT(*) FILTER (WHERE status = 'pending' AND expires_at < CURRENT_TIMESTAMP) as expired_pending
        FROM driver_order_assignments
        WHERE created_at > CURRENT_TIMESTAMP - INTERVAL '24 hours'
      `);

      const orderStats = await query(`
        SELECT 
          COUNT(*) FILTER (WHERE status = 'ready' AND assigned_at IS NULL) as unassigned_orders,
          COUNT(*) FILTER (WHERE status = 'ready' AND assigned_at IS NOT NULL) as assigned_orders,
          COUNT(*) FILTER (WHERE status = 'assigned') as accepted_orders,
          COUNT(*) FILTER (WHERE order_type = 'restaurant') as restaurant_orders,
          COUNT(*) FILTER (WHERE order_type = 'mart') as mart_orders
        FROM orders
        WHERE created_at > CURRENT_TIMESTAMP - INTERVAL '24 hours'
      `);

      return {
        assignments: stats.rows[0],
        orders: orderStats.rows[0],
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error getting assignment stats:', error);
      return null;
    }
  }
}

module.exports = new OrderAssignmentScheduler();
